from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field

from bekb.schemas.schemas import (
    BusinessPartner,
    Partner,
    DossierName,
    User,
    AccountNameFipla,
    JWTAuthSchemaBase,
)


class Langugage(str, Enum):
    de = "de"
    fr = "fr"


class DossierCreateJWTFipla(BaseModel):
    exp: int = Field(
        description="Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, 2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day is accounted for by exactly 86400 seconds, other than that non-integer values can be represented. See RFC 3339 [RFC3339] for details regarding date/times in general and UTC in particular."
    )

    account_name: AccountNameFipla

    business_partner: BusinessPartner
    partner_partner: Optional[Partner] = None

    dossier_name: DossierName

    language: Langugage

    current_user: User = Field(
        description="The current user (from the DBP). Will be the owner of the dossier during creation."
    )


class DossierShowJWTFipla(BaseModel):
    exp: int = Field(
        description="Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, 2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day is accounted for by exactly 86400 seconds, other than that non-integer values can be represented. See RFC 3339 [RFC3339] for details regarding date/times in general and UTC in particular."
    )

    account_name: AccountNameFipla
    business_partner: BusinessPartner

    language: Langugage

    current_user: User = Field(
        description="The current user (from the DBP). Will be the owner of the dossier."
    )


class JWTAuthSchemaFipla(JWTAuthSchemaBase):
    account_key: AccountNameFipla
