from pathlib import Path
from typing import List, <PERSON><PERSON>, Optional
from uuid import UUID

import structlog
from django.conf import settings
from django.core.exceptions import PermissionDenied
from django.http import (
    HttpRequest,
    HttpResponseRedirect,
    StreamingHttpResponse,
    HttpResponse,
)
from ninja import NinjaAPI
from ninja.openapi.schema import OpenAPISchema
from ninja.responses import Response
from pydantic import HttpUrl
from pydantic import ValidationError

from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from bekb.models import (
    BEKBDossierProperties,
)
from bekb.schemas import schemas
from bekb.schemas.schemas import (
    Parkey,
    AccountNameFipla,
)
from bekb.schemas.schemas_fipla import (
    DossierShowJWTFipla,
    DossierCreateJWTFipla,
    JWTAuthSchemaFipla,
)
from bekb.services import (
    list_actively_used_business_parkeys,
    process_ready_for_export_dossiers,
)
from bekb.services_api import (
    decode_jwt_or_render_error,
    process_common_dossier_logic,
    get_dossiers_for_business_partner,
    create_dossier_from_jwt,
    get_hypodossier_users,
    update_user_attributes_service,
    process_business_partner_update,
    api_download_file,
    api_add_dossier_export_feedback,
    create_test_dossiers_ready_for_export,
    validate_jwt_schema_with_http_error,
    validate_jwt_account_name_access,
)
from core.schema import Message, Result
from dossier.helpers_access_check import get_dossier_from_request_with_access_check
from dossier.helpers_api import handle_api_validation_error
from dossier.models import DossierUser
from dossier.services import (
    get_grant_token_for_dossier,
)
from projectconfig.authentication import JWTAuth, Auth
from statemgmt.services import log_work_status_transitions

logger = structlog.get_logger()


class BEKBFLIPAJWTAuth(JWTAuth):
    def authenticate(self, request, token, *args, **kw) -> Optional[DossierUser]:
        """Authenticates a user using JWTAuth, only if the user has the role defined in
        settings.BEKB_API_ROLE.

               Args:
                   request (HttpRequest): The HTTP request from the client.
                   token (str): The token to be authenticated.
               Returns:
                   HttpResponse: The authentication response.
        """
        auth = Auth(token)

        validate_jwt_schema_with_http_error(JWTAuthSchemaFipla, auth.decoded_payload)

        if not auth.has_role(settings.BEKB_API_ROLE):
            logger.error(
                "Authentication failed: Required BEKB API role not found in token"
            )
            return

        return super().authenticate(request, token, *args, **kw)


api = NinjaAPI(
    title="Hypodossier - FIPLA API",
    csrf=False,
    auth=BEKBFLIPAJWTAuth(),
    urls_namespace="fipla-api",
    version="1.0.0",
)


@api.get(
    path="/{dossier_uuid}/token",
    response={200: str, 401: Message},
    url_name="cdp-dossier-access-token",
)
def get_dossier_grant_token(request, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)
    return get_grant_token_for_dossier(dossier)


@api.get(
    "/openapi_3_1_0.json",
    url_name="openapi-3-1-0",
    auth=None,
    include_in_schema=False,
)
def fipla_openapi_3_1_0_schema(request):
    # Actual schema
    return Response(OpenAPISchema(api, str(Path(request.path).parent)))


@api.exception_handler(ValidationError)
def custom_validation_errors(request, exc):
    return handle_api_validation_error(api, logger, exc, request)


@api.get("/dossier", response={303: HttpUrl}, auth=None, url_name="show-dossier")
def show_dossier(request, encoded_jwt: str) -> HttpResponse:
    """Shows all the dossiers associated with the corresponding businesscase_parkey
    and creates a redirect to the bekb_dossier list view.

    for the encoding of the encoded_jwt see @create_dossier

    Args:
    request (HttpRequest): The HTTP request from the client.
    encoded_jwt (str): The encoded JWT.

    Returns:
        HttpResponse: The HTTP response.
    """
    decoded_jwt_or_response = decode_jwt_or_render_error(
        encoded_jwt, error_redirect_url="https://fipla.bekb.hypodossier.ch/dashboard"
    )
    if isinstance(decoded_jwt_or_response, HttpResponse):
        # Decoding failed => return error page
        return decoded_jwt_or_response

    decoded_jwt = decoded_jwt_or_response

    validate_jwt_schema_with_http_error(DossierShowJWTFipla, decoded_jwt)

    # Process the shared dossier logic
    redirect_url = process_common_dossier_logic(
        decoded_jwt=decoded_jwt,
        schema_cls=DossierShowJWTFipla,
        use_fico=False,  # fipla schema does not have fico
    )
    return HttpResponseRedirect(redirect_to=redirect_url)


@api.get(
    "/list-dossiers/{account_name}/{business_parkey}",
    response={200: List[schemas.DossierShow]},
    url_name="list-dossiers",
)
def list_dossiers(request, account_name: AccountNameFipla, business_parkey: Parkey):
    """Lists all dossiers associated with the specified business partner."""
    account = validate_jwt_account_name_access(request, account_name)

    return get_dossiers_for_business_partner(account, business_parkey)


@api.get("/dossier/create", response={302: str}, auth=None, url_name="create-dossier")
def create_dossier(request: HttpRequest, encoded_jwt: str):
    """Creates a new Dossier based on the provided parameters

    This endpoint is called via URL integration in the DBP (Digitales Berater Portal - Create a new Dossier).
    After the creation of the dossier, the user is redirected to the detail view of the newly created dossier.

    encoded_jwt is a jwt which contains the following parameters:
    * ***expiration*** defines the expiration time of the token timezone.now() + timedelta(minutes=60)
    * ***businesscase_parkey*** corresponds to the business case to which the dossier is associated.
    * ***pers*** sets the dossier property to pers, which indicates that only users with pers attributes are allow to open the dossier
    * ***fico*** sets the username (email address) of the responsible financial coach of the dossier

    * ***current_user*** is the username of the person trying to create the dossier.
        if the pers attribute is set, it is assumed that the current_user has the permission to view dossiers with the pers attribut of the dossier set to true

    ```python
    import jwt
    shared_secret = "a secret shared between bekb and hypodossier"
    parameters = {'exp': expiration, 'businesscase_parkey': '1234566', 'pers': True, "team": "team1", "fico": "<EMAIL>", "current_user": "<EMAIL>" }
    encoded_jwt = jwt.encode(parameters, shared_secret, algorithm='HS256')
    ```
    """

    result = create_dossier_from_jwt(
        encoded_jwt, DossierCreateJWTFipla, use_fico=False, account_type="bekbfipla"
    )

    if isinstance(result, HttpResponse):
        return result

    _, response_url = result
    return HttpResponseRedirect(redirect_to=response_url)


# Same users as main BEKB?
@api.get(
    "/users",
    response={401: Message, 200: List[schemas.User]},
    url_name="list-hypodossier-users",
)
def list_hypodossier_users(
    request, account_name: AccountNameFipla
) -> List[schemas.User]:
    """Lists all HypoDossier users for a specific account.

    Args:
        request (HttpRequest): The HTTP request from the client.
        account_name (AccountName): The name of the account.

    Returns:
        List[schemas.User]: The list of HypoDossier users for the specified account.
    """
    validate_jwt_account_name_access(request, account_name)

    return get_hypodossier_users(account_name)


@api.put("/users", response={401: Message, 204: str}, url_name="update-users")
def update_user_attributes(
    request, account_name: AccountNameFipla, updates: List[schemas.UserAttributes]
) -> Tuple[int, str]:
    """Updates the attributes of a user.

    Args:
        request (HttpRequest): The HTTP request from the client.
        account_name (AccountName): The name of the account where the user belongs.
        updates (List[schemas.UserAttributes]): The list of user attributes to update.

    Returns:
        int: HTTP status code indicating the result of the operation.
        str: Empty string.
    """
    validate_jwt_account_name_access(request, account_name)

    update_user_attributes_service(account_name, updates)
    return 204, ""


@api.get(
    "/business-parkeys",
    response={401: Message, 200: List[Parkey]},
    url_name="list-business-parkeys",
)
def list_business_parkeys(request, account_name: AccountNameFipla) -> List[Parkey]:
    """Returns a list of all business parkeys (Geschäftsfall Parkey) actively used at HypoDossier.

    Args:
        request (HttpRequest): The HTTP request from the client.
        account_name (AccountName): The name of the account.

    Returns:
        List[Parkey]: The list of business parkeys.
    """
    validate_jwt_account_name_access(request, account_name)

    ret = list_actively_used_business_parkeys(account_name)

    context = f"BEKBAPI GET /business-parkeys, account={account_name}"
    logger.info(f"{context}, {len(ret)} parkeys: {ret}")
    return ret


@api.put(
    "/business-partners",
    response={401: Message, 204: None},
    url_name="update-business-partners",
)
def update_business_partners(
    request, account_name: AccountNameFipla, updates: schemas.BusinessPartnerUpdates
) -> Tuple[int, str]:
    """Updates the information of business partners.

    Args:
        request (HttpRequest): The HTTP request from the client.
        account_name (AccountName): The name of the account where the business partners belong.
        updates (schemas.BusinessPartnerUpdates): The updates to be applied to the business partners.

    Returns:
        int: HTTP status code indicating the result of the operation.
        str: Empty string.
    """
    context = f"BEKBAPI PUT /business-partners, account={account_name}"
    logger.info(f"{context}, {len(updates.root)} updates: start processing...")

    account = validate_jwt_account_name_access(request, account_name)

    for update in updates.root:
        old_business_partner = process_business_partner_update(account, update)
        logger.info(
            f"{context}, update={update}, old_business_partner={old_business_partner}"
        )

    logger.info(f"{context}, {len(updates.root)} processing done.")

    return 204, ""


@api.get(
    "/export/dossier-ready-for-export",
    response={401: Message, 200: List[schemas.DossierExport]},
    url_name="show-dossier-ready-for-export",
)
def show_dossier_ready_for_export(
    request, account_name: AccountNameFipla
) -> List[schemas.DossierExport]:
    """
    Returns a list of dossier exports that are ready for export.

    Args:
        request: The incoming request object.
        account_name: The name of the account whose exports are to be retrieved.

    Returns:
        List[schemas.DossierExport]: A list of exports for the specified account.
    """

    # CAUTION: this shows all dossiers in work status EXPORT_ARCHIVE_AVAILABLE.
    # It does NOT show the work status READY_FOR_EXPORT_XXX
    #
    # Returns a list of dossier exports that have an export/archive ready and can be exported.

    account = validate_jwt_account_name_access(request, account_name)

    # Fetching the BEKBDossierProperties associated with the account where the key is "EXPORT_ARCHIVE_AVAILABLE".
    candidates_for_export_bekb_dossiers = BEKBDossierProperties.objects.filter(
        account=account, dossier__work_status__key="EXPORT_ARCHIVE_AVAILABLE"
    ).all()

    ready_for_export_bekb_dossiers = candidates_for_export_bekb_dossiers.filter(
        dossier__bekbexport__isnull=False
    )

    broken_for_export_bekb_dossiers = candidates_for_export_bekb_dossiers.filter(
        dossier__bekbexport__isnull=True
    )

    for bd in broken_for_export_bekb_dossiers:
        dossier_uuid = str(bd.dossier.uuid)
        logger.error(
            "No dossier export found but dossier is in work status EXPORT_ARCHIVE_AVAILABLE",
            bekb_dossier=bd,
            dossier_uuid=bd.dossier.uuid,
        )

        log_work_status_transitions(
            dossier_uuid, context="Found these events for the broken dossier export"
        )

    return [
        schemas.DossierExport(
            dossier_uuid=bekb_dossier.dossier.uuid,
            export_uuid=bekb_dossier.dossier.bekbexport.uuid,
        )
        for bekb_dossier in ready_for_export_bekb_dossiers
    ]


@api.get(
    "/export/file",
    response={401: Message, 404: Message, 200: bytes},
    url_name="download-file",
)
def download_file(
    request, account_name: AccountNameFipla, export_uuid: UUID
) -> StreamingHttpResponse:
    """
    Downloads a Dossier associated with a given account and export UUID.

    Args:
        request: The incoming request object.
        account_name: The name of the account.
        export_uuid: The unique identifier of the export.

    Returns:
        StreamingHttpResponse: The file to be downloaded.
    """
    validate_jwt_account_name_access(request, account_name)

    return api_download_file(account_name=account_name, export_uuid=export_uuid)


@api.post(
    "/export/feedback",
    response={
        401: Message,
        # 200: Message,
        204: None,
    },
    url_name="add-dossier-export-feedback",
)
def add_dossier_export_feedback(
    request,
    account_name: AccountNameFipla,
    feedbacks: List[schemas.DossierExportFeedback],
) -> Tuple[int, Message]:
    """
    Adds feedback for a given export.

    Args:
        request: The incoming request object.
        account_name: The name of the account.
        feedbacks: A list of DossierExportFeedback schema objects.

    Returns:
        int, str: 204 status code and an empty string indicating successful operation.
        HTTP 204 if update has been done successfully. HTTP 401 if not allowed.
    """
    validate_jwt_account_name_access(request, account_name)

    api_add_dossier_export_feedback(account_name=account_name, feedbacks=feedbacks)

    return 204, ""
    # res_msg = ",".join(messages) if messages else ""
    # if res_msg:
    #     return 200, Message(detail=res_msg)
    # else:
    #     return 204, ""


@api.post(
    "/test/dossier/create/ready-for-export",
    url_name="add-some-sample-dossier-ready-for-export",
)
def add_some_sample_dossier_ready_for_export(
    request, account_name: AccountNameFipla, count: int = 1
) -> Tuple[int, Message]:
    """
    Create up to count (max 10, default 1) fake dossier in state ready for export

    Args:
        request: The incoming request object.
        account_name: The name of the account.
        count: The number of fake dossiers to be created (default is 1).

    Returns:
        int, Message: In case of a production account, returns a 403 status code and an error message.
    """

    validate_jwt_account_name_access(request, account_name)

    try:
        create_test_dossiers_ready_for_export(
            account_name=account_name,
            factory_class=BekbFiplaAccountFactory,
            count=count,
            update_statemgmt=True,
        )
        return 200, Message(detail="Dossiers created successfully")
    except PermissionDenied as e:
        return 403, Message(detail=str(e))


@api.post(
    "/test/dossier/process-ready-for-export",
    response={403: Message, 201: List[Result[UUID]]},
    url_name="process-ready-for-export",
)
def process_ready_for_export(
    request,
    account_name: AccountNameFipla,
    processing_request: schemas.ProcessReadyForExportRequest,
) -> Tuple[int, Message] or Tuple[int, List[Result[UUID]]]:
    """
    Checks whether a dossier is ready for export

    Args:
        request: The incoming request object.
        account_name: The name of the account.
        processing_request: A ProcessReadyForExportRequest schema object containing dossier UUIDs
                             and 'not_updated_since' parameter.

    Returns:
        int, function: In case of a production account, returns a 403 status code and an error message.
        Otherwise, processes the dossiers and returns a 201 status code.
    """
    if account_name == AccountNameFipla.bekbfiplap:
        return 403, Message(
            detail=f"Unauthorized for production account {account_name}"
        )

    account = validate_jwt_account_name_access(request, account_name)

    return 201, process_ready_for_export_dossiers(
        account=account,
        dossier_uuids=processing_request.dossier_uuids,
        not_updated_since=processing_request.not_updated_since,
    )
