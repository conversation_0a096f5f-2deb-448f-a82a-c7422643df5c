import structlog
import random
from typing import Optional, List, Literal


from faker import Faker
from faker.providers import person, address, internet, date_time, lorem

from assets import ASSETS_PATH
from bekb.bekb_instance_type import BekbInstanceType
from bekb.bekbload import load_document_category2ekd_mappings
from bekb.data import DATA_PATH
from bekb.fakes import handle_update_ekd_ids_and_external_titles
from bekb.models import (
    BEKBDossierProperties,
)
from bekb.schemas import schemas
from bekb.schemas.schemas_fipla import DossierCreateJWTFipla
from bekb.services import (
    create_bekb_dossier,
    create_bekb_export_file,
    VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA,
)
from dossier.doc_cat_helpers import load_document_categories_from_path
from dossier.fakes import add_some_fake_semantic_documents
from dossier.helpers_timezone import create_faker_past_datetime_with_timezone
from dossier.models import (
    Account,
    Dossier<PERSON><PERSON>,
    Document<PERSON>ate<PERSON><PERSON>,
    Do<PERSON><PERSON>,
    BusinessCaseType,
)
from dossier.services import change_dossier_work_status, create_businesscase_type_import
from statemgmt import STATEMGMT_PATH
from statemgmt.export import update_state_machine
from statemgmt.models import StateMachine, Status

logger = structlog.get_logger()

PATH_CURRENT_BEKBFIPLA_STATEMGMT_EXPORT = (
    STATEMGMT_PATH / "configurations/bekb/fipla/bekbfipla_state_machine_20250103.json"
)

LIST_OF_VISIBLE_CATEGORIES = [
    "BEKB_FIPLA_FORM",
    "DIVORCE_CONVENTION",
    "MARRIAGE_CONTRACT",
    "TAX_DECLARATION",
    "TAX_ASSESSMENT",
    "TAX_LIST_FINANCIAL_ASSETS",
    "TAX_MISC",
    "BANK_STATEMENT_OF_INTEREST_CAPITAL",
    "SALARY_CERTIFICATE",
    "PENSION_PAYMENT_AHV",
    "PENSION_PAYMENT_BVG",
    "LOAN_AGREEMENT",
    "BEKB_EKD134",
    "BEKB_FIPLA_RESULT",
    # "BEKB_EKD139",        # Fipla Diverses -> removed
    "EXTRACT_AHV_ACCOUNT",
    "BEKB_EKD121_SIMULATION1_REQUEST",
    "PENSION_SIMULATION1",
    "BEKB_EKD121_SPLITTING_APPLICATION",
    "PILLAR_ONE_MISC",
    "PENSION_CERTIFICATE",
    "PENSION_REGULATIONS",
    "PENSION_CERTIFICATE_SIM_ALL",
    "PENSION_CERTIFICATE_LETTER",
    "VESTED_BENEFITS_ACCOUNT",
    "VESTED_BENEFITS_STATEMENT",
    "PILLAR_TWO_MISC",
    "PENSION3A_ACCOUNT",
    "PENSION3A_INSURANCE_CONTRACT",
    "PILLAR_THREE_MISC",
    "FINANCIAL_STATEMENT_COMPANY",
    "PROPERTY_VALUATION",
    "RENTAL_MISC",
    "PROPERTY_MISC",
    "BEKB_TOTAL_ENGAGEMENT",
    "AUTHORIZATION_FOR_INQUIRIES",
    "STATEMENT_OF_ASSETS",
    "RETIREMENT_ANALYSIS",
] + ["CORRESPONDENCE_EMAIL", "CORRESPONDENCE_LETTER"]


def load_bekbfipla_document_categories(account_key: str):
    account = Account.objects.get(key=account_key)

    # Step 1: load default categories
    filename = None
    load_document_categories_from_path(account, filename)

    document_categories_json_path_custom = (
        ASSETS_PATH / "document_category/bekb/DocumentCategory-2025-03-15bekb.json"
    )
    load_document_categories_from_path(
        account, document_categories_json_path=document_categories_json_path_custom
    )

    handle_update_ekd_ids_and_external_titles(
        account_key, BekbInstanceType.FIPLA, dry_run=False
    )

    # All Fipla doccats are marked as exclude_for_recommendation (because of normal bekb account)
    # Revert that here for EKD 138 (Finanzplanung) and the BEKB_FIPLA_FORM.
    # Keep these generic Fipla documents invisible both for Mortgage and for Fipla:
    # "BEKB_EKD120",
    # "BEKB_EKD121",
    # "BEKB_EKD122",
    # "BEKB_EKD123",
    # "BEKB_EKD124",
    # "BEKB_EKD139",
    valid_keys = ["BEKB_FIPLA_RESULT", "BEKB_FIPLA_FORM", "BEKB_TOTAL_ENGAGEMENT"]
    num_fipla = DocumentCategory.objects.filter(
        account__key=account_key, name__in=valid_keys
    ).update(exclude_for_recommendation=False)
    logger.info("Activated Fipla doccats", num_fipla=num_fipla)
    assert num_fipla == len(valid_keys)

    num_inheritance = DocumentCategory.objects.filter(
        account__key=account_key, name="BEKB_EKD134"
    ).update(exclude_for_recommendation=False)
    logger.info("Activated inheritance doccats", num_inheritance=num_inheritance)
    assert num_inheritance == 1

    num_legacy_ekd56 = DocumentCategory.objects.filter(
        account__key=account_key, name="BEKB_EKD56"
    ).update(exclude_for_recommendation=True)
    logger.info("Deactivated legacy ekd56", num_legacy_ekd56=num_legacy_ekd56)
    assert num_legacy_ekd56 == 1

    # Add 3 custom document categories that only exist for Fipla
    DocumentCategory.objects.update_or_create(
        defaults={
            "id": "404-EKD121",
            "id_external": "EKD121",
            "de": "AHV Antrag Rentenvorausberechnung",
            "en": "Pillar 1 Simulation Request",
            "fr": "Demande Estimation rentes AVS",
            "it": "Domanda AVS di calcolo preliminare della rendita",
            "description_de": "Antrag, Rente im Voraus zu berechnen",
            "de_external": "FinPla: AHV",
            "en_external": "FinPla: AHV	",
            "fr_external": "PlaFi: AVS",
        },
        name="BEKB_EKD121_SIMULATION1_REQUEST",
        account=account,
    )

    DocumentCategory.objects.update_or_create(
        defaults={
            "id": "406-EKD121",
            "id_external": "EKD121",
            "de": "AHV Splittingantrag",
            "en": "AHV Splitting Application",
            "fr": "Demande de splitting AVS",
            "it": "Domanda di splitting AVS",
            "description_de": "Antrag, dass AHV-Rente auf beide Ehepartner aufgeteilt wird",
            "de_external": "FinPla: AHV",
            "en_external": "FinPla: AHV	",
            "fr_external": "PlaFi: AVS",
        },
        name="BEKB_EKD121_SPLITTING_APPLICATION",
        account=account,
    )

    # Remove "FinPla: " prefix from all document categories
    doccats = DocumentCategory.objects.filter(account__key=account_key)
    for dc in doccats:
        changed = False
        strip_list = ["FinPla: ", "PlaFi: "]
        for s in strip_list:
            if dc.de.startswith(s):
                dc.de = dc.de[len(s) :]
                changed = True
            if dc.en.startswith(s):
                dc.en = dc.en[len(s) :]
                changed = True
            if dc.fr.startswith(s):
                dc.fr = dc.fr[len(s) :]
                changed = True
            if dc.it.startswith(s):
                dc.it = dc.it[len(s) :]
                changed = True
        if changed:
            dc.save()
            logger.info(
                "Removed FinPla: prefix from document category",
                dc_id=dc.id,
                dc_name=dc.name,
                dc_de=dc.de,
            )

    # Final step: change visibility of document categories so that only a defined set is visible:
    num_invisible = (
        DocumentCategory.objects.filter(account__key=account_key)
        .exclude(name__in=LIST_OF_VISIBLE_CATEGORIES)
        .update(exclude_for_recommendation=True)
    )
    logger.info("Set invisible", num_invisible=num_invisible)
    assert num_invisible == 292

    # Validate if everything is configured already correctly in terms of visiblity.
    list_wrongly_invisible = list(
        DocumentCategory.objects.filter(
            account__key=account_key, name__in=LIST_OF_VISIBLE_CATEGORIES
        ).filter(exclude_for_recommendation=True)
    )
    logger.info("check visible", list_wrongly_invisible=list_wrongly_invisible)
    assert len(list_wrongly_invisible) == 0


class BekbFiplaAccountFactory:
    def __init__(
        self,
        update_statemgmt: bool = False,
        account: Optional[Account] = None,
        account_key: Optional[str] = None,
        default_bucket_name: Optional[str] = None,
    ):
        """
        Create or update test environment for BEKB Fipla (Finanzplanung).

        @param account: If this is set then use the provided account. Else set up or update account
        @param update_statemgmt:
        """

        if account is None:
            if account_key is None:
                account_key = schemas.AccountNameFipla.bekbfiplae.value
            account, created = Account.objects.update_or_create(key=account_key)

            state_machine_name = "Dossier State Machine BEKB Fipla"
            state_machine = StateMachine.objects.filter(name=state_machine_name).first()
            if update_statemgmt or state_machine is None:
                p = PATH_CURRENT_BEKBFIPLA_STATEMGMT_EXPORT
                assert p.exists()
                state_machine = update_state_machine(p, state_machine_name)
                account.active_work_status_state_machine = state_machine
            assert state_machine is not None

            if account_key == schemas.AccountNameFipla.bekbfiplae.value:
                # Used for development HD internally and shared dev environment
                account.name = "BEKB Fipla Echo"
                account.default_bucket_name = "production-v2-test-bekbfiplae"
                account.dmf_endpoint = "https://fipla.bekbe.test.hypodossier.ch"
                account.enable_button_create = True
            elif account_key == schemas.AccountNameFipla.bekbfiplau.value:
                account.name = "BEKB Fipla Uniform"
                account.default_bucket_name = "production-v2-test-bekbfiplau"
                account.dmf_endpoint = "https://fipla.bekbu.test.hypodossier.ch"
                account.enable_button_create = True
            elif account_key == schemas.AccountNameFipla.bekbfiplas.value:
                account.name = "BEKB Fipla Sierra"
                account.default_bucket_name = "production-v2-test-bekbfiplas"
                account.dmf_endpoint = "https://fipla.bekbs.test.hypodossier.ch"
                account.enable_button_create = True
            elif account_key == schemas.AccountNameFipla.bekbfiplaz.value:
                account.name = "BEKB Fipla Zulu"
                account.default_bucket_name = "production-v2-test-bekbfiplaz"
                account.dmf_endpoint = "https://fipla.bekbz.test.hypodossier.ch"
                account.enable_button_create = True
            elif account_key == schemas.AccountNameFipla.bekbfiplap.value:
                account.name = "BCGE Fipla Production"
                account.default_bucket_name = "production-v2-bekbfiplap-dms"
                account.dmf_endpoint = "https://fipla.bekb.hypodossier.ch"
                account.enable_button_create = False
            else:
                raise ValueError(f"Invalid account key '{account_key}'")

            if default_bucket_name is not None:
                account.default_bucket_name = default_bucket_name

            # These settings are the same as in prod
            account.enable_dossier_search = False  # don't know what this is for

            account.default_dossier_expiry_duration_days = 720
            account.max_dossier_expiry_duration_days = 720
            account.show_document_category_external = True
            account.show_business_case_type = True
            account.enable_rendering_structure_tab = False
            account.enable_rendering_structure_details_tab = True
            account.enable_rendering_bekb_mortgage_archiving_tab = (
                False  # different from BEKB
            )
            account.enable_rendering_hurdles_tab = False
            account.enable_rendering_plans_tab = False  # different from BEKB
            account.enable_rendering_photos_tab = False
            account.enable_download_document = True
            account.enable_bekb_export = True
            account.enable_bekb_automatic_collateral = False  # different from BEKB

            account.enable_download_extraction_excel = False
            account.enable_dossier_permission = False
            account.enable_semantic_page_image_lazy_loading = True
            account.enable_documents_delta_view = True

            account.enable_dossier_assignment = True
            account.enable_dossier_assignment_to_someone_else = True

            account.enable_area_calculator = False
            account.enable_doccheck_in_statemgmt = False
            account.enable_semantic_document_splitting = True

            account.frontend_theme = ""
            account.photo_album_docx_template = ""
            account.valid_dossier_languages = ["De", "Fr"]
            account.valid_ui_languages = ["de", "fr", "en"]

            account.enable_semantic_document_annotations = True
            account.save()

        self.account = account

        p = (
            DATA_PATH
            / "businesscase_types/fipla/bekbfipla_businesscase_types_20250103.json"
        )
        create_businesscase_type_import(p, account_key)

        self.load_initial_document_categories()

        # self.business_types = create_business_types(account)

        DossierRole.objects.get_or_create(
            defaults=dict(
                name_de="Zuständiger",
                name_fr="Responsable",
                name_en="Assignee",
                name_it="Assegnatario",
            ),
            key="ASSIGNEE",
            user_selectable=True,
            show_separate_filter=True,
            account=account,
        )

        faker = Faker(locale="de_CH")
        faker.add_provider(person)
        faker.add_provider(address)
        faker.add_provider(internet)
        faker.add_provider(date_time)
        faker.add_provider(lorem)
        self.faker = faker

    def load_initial_document_categories(self):

        num_expected_categories = 331

        load_bekbfipla_document_categories(self.account.key)

        self.document_categories = list(
            DocumentCategory.objects.filter(account=self.account).all()
        )

        assert (
            len(self.document_categories) == num_expected_categories
        ), f"Instead of {num_expected_categories}, {len(self.document_categories)} have been found "

        return self.document_categories

    def create_dossier(
        self,
        business_parkey=None,
        current_user: schemas.User = None,
        pers: bool = random.choice([True, False]),
        dossier_name: str = None,
    ) -> BEKBDossierProperties:
        business_partner = self.create_business_partner(
            parkey=business_parkey, pers=pers
        )
        partner_partner = self.create_partner_partner()

        self.create_fico(pers=pers)

        if current_user is None:
            current_user = self.create_user(pers=pers)

        if dossier_name is None:
            dossier_name = f"Fipla Dossier von {business_partner.firstname} {business_partner.name}"

        return create_bekb_dossier(
            self.account,
            DossierCreateJWTFipla(
                exp=0,
                account_name=schemas.AccountNameFipla(self.account.key),
                business_partner=business_partner,
                partner_partner=partner_partner,
                dossier_name=dossier_name,
                language=random.choice(list(schemas.Langugage)),
                current_user=current_user,
            ),
            use_fico=False,
        )

    def create_business_partner(
        self, parkey=None, pers: bool = random.choice([True, False])
    ):
        if parkey is None:
            effective_parkey = self.faker.bothify("#########")
        else:
            effective_parkey = parkey

        business_partner = schemas.BusinessPartner(
            parkey=effective_parkey,
            name=self.faker.last_name(),
            firstname=self.faker.first_name(),
            pers=pers,
        )
        return business_partner

    def create_fico(self, pers: bool = random.choice([True, False])):
        """
        Dummy implementation for Fipla. So no Fico is created.
        @param pers:
        @return:
        """
        return None

    def create_user(self, pers: bool = random.choice([True, False])):
        current_user = schemas.User(
            firstname=self.faker.first_name(),
            name=self.faker.last_name(),
            username=self.faker.email(domain=f"{self.account.key}.ch"),
            active=random.choice([True, False]),
            pers=pers,
        )
        return current_user

    def create_partner_partner(self) -> Optional[schemas.Partner]:
        # Partner_partner not needed for Fipla
        return None

    def create_businesscase(self, bekb_dossier: BEKBDossierProperties):
        # businesscase not needed for Fipla
        return None

    def create_sample_dossier(
        self,
        pers: bool = random.randint(0, 10) > 8,
        dossier_name: str = None,
        # num_business_cases: int = random.randint(0, 10),
        add_semantic_documents: bool = True,
        valid_document_category_keys: Optional[List[str]] = None,
    ):
        new_bekb_dossier = self.create_dossier(pers=pers, dossier_name=dossier_name)
        new_bekb_dossier.created_at = create_faker_past_datetime_with_timezone(
            self.faker
        )
        new_bekb_dossier.dossier.created_at = new_bekb_dossier.created_at

        if add_semantic_documents:
            add_some_fake_semantic_documents(
                new_bekb_dossier.dossier,
                valid_document_category_keys=valid_document_category_keys,
            )
        return new_bekb_dossier

    def create_export_archive_available_dossier(
        self,
        min_num_documents: int = 0,
        max_num_documents: int = 10,
        possible_work_status_keys=["READY_FOR_EXPORT_FIPLA"],
        valid_document_category_keys=None,
        businesscase_type_key="FINANCIAL_PLANNING",
    ):
        bekb_dossier = self.create_ready_for_export_dossier(
            min_num_documents=min_num_documents,
            max_num_documents=max_num_documents,
            valid_document_category_keys=valid_document_category_keys,
            possible_work_status_keys=possible_work_status_keys,
        )

        create_bekb_export_file(bekb_dossier, is_bekb_fipla=True)
        dossier: Dossier = bekb_dossier.dossier
        account: Account = dossier.account

        assert self.account.active_work_status_state_machine is not None

        new_status = Status.objects.get(
            state_machine=self.account.active_work_status_state_machine,
            key="EXPORT_ARCHIVE_AVAILABLE",
        )

        change_dossier_work_status(
            dossier, dict(is_system=True, is_user=False), new_status
        )
        businesscase_type, _ = BusinessCaseType.objects.get_or_create(
            account=account, key=businesscase_type_key
        )
        dossier.businesscase_type = businesscase_type
        dossier.save()
        return bekb_dossier

    def create_ready_for_export_dossier(
        self,
        min_num_documents: int = 0,
        max_num_documents: int = 10,
        possible_work_status_keys: List[str] = VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA,
        valid_document_category_keys: Optional[List[str]] = None,
        allow_empty_docs: bool = True,
        max_pages=10,
        min_num_pages=1,
        dossier_name: str = None,
        businesscase_type_key: Literal[
            "INHERITANCE", "FINANCIAL_PLANNING"
        ] = "FINANCIAL_PLANNING",
    ) -> BEKBDossierProperties:

        assert possible_work_status_keys

        # Create a dossier with 80% probability non-pers and 20% probability pers
        bekb_dossier: BEKBDossierProperties = self.create_dossier(
            pers=random.randint(0, 10) > 8,
            dossier_name=dossier_name,
        )
        dossier = bekb_dossier.dossier
        account: Account = dossier.account

        businesscase_type, _ = BusinessCaseType.objects.get_or_create(
            account=account, key=businesscase_type_key
        )
        dossier.businesscase_type = businesscase_type
        dossier.save()

        num_docs = random.randint(min_num_documents, max_num_documents)
        semantic_documents = add_some_fake_semantic_documents(
            dossier,
            num_docs=num_docs,
            valid_document_category_keys=valid_document_category_keys,
            allow_empty_docs=allow_empty_docs,
            max_pages=max_pages,
            min_num_pages=min_num_pages,
        )

        mappings = load_document_category2ekd_mappings(BekbInstanceType.MORTGAGE)

        for semantic_document in semantic_documents:
            # documents without ekd numbers should not be archived
            if semantic_document.document_category.name not in mappings.keys():
                semantic_document.delete()
                continue

        possible_work_status_list = list(
            Status.objects.filter(key__in=possible_work_status_keys).all()
        )

        dossier.work_status = random.choice(possible_work_status_list)

        dossier.save()
        bekb_dossier.business_case = None
        bekb_dossier.save()

        return bekb_dossier
