from datetime import datetime
from uuid import uuid4

from datetime import timezone as datetime_timezone
import structlog

import pytest

from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from bekb.fakes import (
    BekbAccountFactoryFaker,
)

from bekb.constants import archive
from bekb.models import BEKBDossierProperties
from bekb.services import (
    calculate_bekb_dossier_semdoc_grouping,
    merge_bekb_semdoc_grouping_for_archive,
    partition_grouping_collateral_assignment,
    prepare_document_package_for_export,
    create_index_file,
    VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
    VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA,
)
from core.temporary_path import temporary_path
from dossier import models
from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import Dossier, DocumentCategory
from semantic_document.models import SemanticPage, SemanticDocument

logger = structlog.get_logger()

endpoint = "/partner/bekb/api/0.7"

pytestmark = pytest.mark.django_db


def assert_semantic_pages_order(dossier: Dossier):
    # Check that all semantic pages are ordered correctly
    for sem_doc in dossier.semantic_documents.all():
        sem_pages: models.QuerySet[SemanticPage] = sem_doc.semantic_pages.order_by(
            "number"
        )

        page_no = 0

        for sem_page in sem_pages:
            assert sem_page.number == page_no
            page_no += 1


def test_archive_bekb_dossier_basic(load_bekbe_account):
    # Should not throw exception

    bekbe_account, bekbe_account_factory = load_bekbe_account

    bekb_dossier = bekbe_account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=[
            item for sublist in archive.ALL_GROUP_TYPES.values() for item in sublist
        ],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)


def test_archive_bekb_dossier_same_category_name(load_bekbe_account):
    # handle edge case 2: change document_category_key_of_grouped_document in map if all
    # documents in list have same document category
    # Should not throw exception

    bekbe_account, bekbe_account_factory = load_bekbe_account

    bekb_dossier = bekbe_account_factory.create_ready_for_export_dossier(
        min_num_documents=20,
        max_num_documents=20,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    # Expect all to be of type CONSTRUCTION_PLAN, as they are all the same
    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    assert len(merge_groupings["CONSTRUCTION_PLAN"]) == 20
    assert list(merge_groupings.keys()) == ["CONSTRUCTION_PLAN"]

    # Expect them to be under PLAN_ANY as we have different types
    bekb_dossier = bekbe_account_factory.create_ready_for_export_dossier(
        min_num_documents=20,
        max_num_documents=20,
        valid_document_category_keys=["CONSTRUCTION_PLAN", "PLAN_FLOOR"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    assert len(merge_groupings["PLAN_ANY"]) == 20
    assert list(merge_groupings.keys()) == ["PLAN_ANY"]


def test_merge_bekb_semantic_documents_for_archiving_merge_plan_any(load_bekbe_account):
    # Test the case where we have 3 docs, of PLAN_ANY, PLAN_FLOOR and CONSTRUCTION_PLAN
    # and that they all merge into a single semantic doc of type PLAN_ANY

    bekbe_account, bekbe_account_factory = load_bekbe_account

    bekb_dossier: BEKBDossierProperties = bekbe_account_factory.create_dossier(
        pers=True
    )

    dossier = bekb_dossier.dossier

    assert dossier.semantic_documents.count() == 0

    semantic_doc_plan_any = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["PLAN_ANY"],
        allow_empty_docs=False,
    )[0]
    semantic_doc_plan_floor = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["PLAN_FLOOR"],
        allow_empty_docs=False,
    )[0]
    semantic_doc_construction_plan = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        allow_empty_docs=False,
    )[0]

    semantic_pages = SemanticPage.objects.filter(dossier=dossier)
    assert dossier.semantic_documents.count() == 3
    assert SemanticPage.objects.filter(dossier=dossier).count() == 6

    semantic_document_map = calculate_bekb_dossier_semdoc_grouping(dossier)

    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=semantic_document_map)

    # Assert that we now have only one semantic document, and still have all 6 semantic pages
    assert dossier.semantic_documents.count() == 1
    assert SemanticPage.objects.filter(dossier=dossier).count() == 6

    assert_semantic_pages_order(dossier=dossier)

    # Test that all semantic pages have been moved into semantic_doc_plan_any
    # It was moved into it, as we internally we filter an order by document_category__id
    # in calculate_bekb_dossier_merge_groupings

    assert semantic_doc_plan_any.semantic_pages.count() == 6
    assert all(
        doc in semantic_pages.all()
        for doc in semantic_doc_plan_any.semantic_pages.all()
    )

    with pytest.raises(SemanticDocument.DoesNotExist):
        semantic_doc_plan_floor.refresh_from_db()
        semantic_doc_construction_plan.refresh_from_db()

    assert dossier.semantic_documents.first().title_suffix == "(zusammengefasst)"


def test_merge_bekb_semantic_documents_for_archiving_no_documents(load_bekbe_account):
    # Test the case where there are no documents to merge

    bekbe_account, bekbe_account_factory = load_bekbe_account

    bekb_dossier: BEKBDossierProperties = bekbe_account_factory.create_dossier(
        pers=True
    )
    dossier = bekb_dossier.dossier
    assert dossier.semantic_documents.count() == 0

    merge_groupings = semantic_document_map = calculate_bekb_dossier_semdoc_grouping(
        dossier
    )

    assert merge_groupings == {}
    # merge should not raise error
    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=semantic_document_map)

    assert dossier.semantic_documents.count() == 0
    assert_semantic_pages_order(dossier=bekb_dossier.dossier)


def test_archive_bekb_dossier_single_document(load_bekbe_account):
    # Test that a single document is not merged

    bekbe_account, bekbe_account_factory = load_bekbe_account

    bekb_dossier = bekbe_account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    # Expect empty as single CONSTRUCTION_PLAN
    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    assert merge_groupings == {}

    # Merge should not raise error
    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=merge_groupings)
    # Doc count should not change
    assert bekb_dossier.dossier.semantic_documents.count() == 1

    assert_semantic_pages_order(dossier=bekb_dossier.dossier)

    # Add another document of same type
    add_some_fake_semantic_documents(
        bekb_dossier.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        allow_empty_docs=False,
    )

    assert bekb_dossier.dossier.semantic_documents.count() == 2

    # Expect all to be of type CONSTRUCTION_PLAN, as they are all the same
    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    assert len(merge_groupings["CONSTRUCTION_PLAN"]) == 2

    assert list(merge_groupings.keys()) == ["CONSTRUCTION_PLAN"]

    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=merge_groupings)

    assert bekb_dossier.dossier.semantic_documents.count() == 1
    assert_semantic_pages_order(bekb_dossier.dossier)


def test_archive_bekb_document_categories_exist(load_bekbe_account):
    # Test that default categories mentioned in archive.ALL_GROUP_TYPES exist for bekbe

    bekbe_account, bekbe_account_factory = load_bekbe_account

    bekb_document_categories_keys = DocumentCategory.objects.filter(
        name__in=archive.ALL_GROUP_TYPES.keys(),
        account=bekbe_account,
    )

    assert set(list(archive.ALL_GROUP_TYPES.keys())) == set(
        bekb_document_categories_keys.values_list("name", flat=True)
    )

    bekb_document_categories_values = DocumentCategory.objects.filter(
        name__in=list(x for v in archive.ALL_GROUP_TYPES.values() for x in v),
        account=bekbe_account,
    )

    assert {x for v in archive.ALL_GROUP_TYPES.values() for x in v} == set(
        bekb_document_categories_values.values_list("name", flat=True)
    )


@pytest.mark.django_db
def test_merge_bekb_semantic_documents_all_categories(load_bekbe_account):

    bekbe_account, bekbe_account_factory = load_bekbe_account

    assert Dossier.objects.filter(account=bekbe_account).count() == 0

    bekbe_account_factory = BekbAccountFactoryFaker()

    d_merge_2 = bekbe_account_factory.create_sample_dossier(
        pers=False,
        dossier_name="All categories for merging",
        num_business_cases=0,
        num_collaterals=0,
        collateral_type=bekbe_account_factory.attribute_collateral_grundpfand,
        add_semantic_documents=False,
    )

    dossier: Dossier = d_merge_2.dossier
    dossier.save()

    for grouping_name, grouping_values in archive.ALL_GROUP_TYPES.items():
        add_some_fake_semantic_documents(
            d_merge_2.dossier,
            num_docs=5,
            max_pages=2,
            min_num_pages=2,
            no_page_objects_per_page=1,
            valid_document_category_keys=grouping_values,
            allow_empty_docs=False,
        )

    assert dossier.semantic_documents.count() == 30
    total_semantic_page_count = dossier.semantic_pages.count()
    assert total_semantic_page_count == 60
    assert_semantic_pages_order(dossier=dossier)

    merge_groupings = semantic_document_map = calculate_bekb_dossier_semdoc_grouping(
        dossier
    )

    assert merge_groupings
    # merge should not raise error
    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=semantic_document_map)

    assert total_semantic_page_count == dossier.semantic_pages.count()

    assert_semantic_pages_order(dossier=dossier)


def test_partition_grouping_collateral_assignment_no_collateral(load_bekbe_account):
    # Check that partition_grouping_collateral_assignment does nothing when there are no collaterals

    bekbe_account, bekbe_account_factory = load_bekbe_account

    bekb_dossier = bekbe_account_factory.create_sample_dossier(
        pers=False,
        dossier_name="All categories for merging",
        num_business_cases=0,
        num_collaterals=0,
        collateral_type=None,
        add_semantic_documents=False,
    )

    add_some_fake_semantic_documents(
        bekb_dossier.dossier,
        num_docs=5,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        allow_empty_docs=False,
    )

    assert bekb_dossier.dossier.semantic_documents.count() == 5

    semantic_document_list = partition_grouping_collateral_assignment(
        bekb_dossier.dossier.semantic_documents.all()
    )

    # Expect only one element in the list, as there are no collaterals, so we didn't partition
    assert len(semantic_document_list) == 1
    assert semantic_document_list[0].count() == 5

    # Expect all to be of type CONSTRUCTION_PLAN, as they are all the same
    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    assert len(merge_groupings["CONSTRUCTION_PLAN"]) == 5

    assert list(merge_groupings.keys()) == ["CONSTRUCTION_PLAN"]

    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=merge_groupings)

    assert bekb_dossier.dossier.semantic_documents.count() == 1
    assert_semantic_pages_order(dossier=bekb_dossier.dossier)


def test_partition_grouping_collateral_assignment_single_collateral(load_bekbe_account):
    # Check that partition_grouping_collateral_assignment does nothing when there's a single collateral

    bekbe_account, bekbe_account_factory = load_bekbe_account

    # Creates a single collateral by default
    bekb_dossier = bekbe_account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    assert bekb_dossier.dossier.semantic_documents.count() == 1

    semantic_document_list = partition_grouping_collateral_assignment(
        bekb_dossier.dossier.semantic_documents.all()
    )

    assert len(semantic_document_list) == 1
    assert semantic_document_list[0].count() == 1

    # Expect all to be of type CONSTRUCTION_PLAN, as they are all the same
    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    assert merge_groupings == {}

    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=merge_groupings)

    # No change
    assert bekb_dossier.dossier.semantic_documents.count() == 1
    assert_semantic_pages_order(dossier=bekb_dossier.dossier)


def _setup_test_multiple_different_collaterals(
    bekbe_account_factory, valid_document_category_keys=None
):
    # Load multiple different collaterals and attempt to merge them
    # They should not merge as they are different collaterals, same category set

    # Creates a unique collateral, per doc by default
    if valid_document_category_keys is None:
        valid_document_category_keys = ["CONSTRUCTION_PLAN"]

    bekb_dossier = bekbe_account_factory.create_ready_for_export_dossier(
        min_num_documents=5,
        max_num_documents=5,
        max_pages=2,
        min_num_pages=2,
        allow_empty_docs=False,
        valid_document_category_keys=valid_document_category_keys,
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    # Check that they each have a collateral
    for semdoc in bekb_dossier.dossier.semantic_documents.all():
        assert semdoc.collateralassignment.collateral

    assert bekb_dossier.dossier.semantic_documents.count() == 5
    assert bekb_dossier.dossier.semantic_pages.count() == 10
    # Keep track of UUIDs to make sure we don't loose pages
    semantic_page_uuids = bekb_dossier.dossier.semantic_pages.values_list(
        "uuid", flat=True
    )

    semantic_document_list = partition_grouping_collateral_assignment(
        bekb_dossier.dossier.semantic_documents.all()
    )

    assert len(semantic_document_list) == 5
    assert semantic_document_list[0].count() == 1

    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    assert_semantic_pages_order(dossier=bekb_dossier.dossier)

    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=merge_groupings)

    # No merge, as all different collaterals
    assert bekb_dossier.dossier.semantic_documents.count() == 5
    assert bekb_dossier.dossier.semantic_pages.count() == 10
    assert (
        bekb_dossier.dossier.semantic_pages.filter(uuid__in=semantic_page_uuids).count()
        == 10
    )

    # Keep 5 unique collaterals, so we haven't lost any
    assert (
        bekb_dossier.dossier.semantic_documents.values_list(
            "collateralassignment__collateral", flat=True
        )
        .exclude(collateralassignment__collateral=None)
        .distinct()
        .count()
        == 5
    )

    assert_semantic_pages_order(dossier=bekb_dossier.dossier)

    return bekb_dossier


@pytest.mark.parametrize(
    "valid_document_category_keys",
    [["CONSTRUCTION_PLAN"], archive.ALL_GROUP_TYPES["PLAN_ANY"]],
)
def test_partition_grouping_collateral_assignment_multiple_different_unique_collaterals_no_merge(
    load_bekbe_account, valid_document_category_keys
):

    bekbe_account, bekbe_account_factory = load_bekbe_account

    # Load multiple different collaterals and attempt to merge them
    # They should not merge as they are different
    _setup_test_multiple_different_collaterals(
        bekbe_account_factory, valid_document_category_keys
    )


@pytest.mark.parametrize(
    "valid_document_category_keys",
    [["CONSTRUCTION_PLAN"], archive.ALL_GROUP_TYPES["PLAN_ANY"]],
)
def test_partition_grouping_collateral_assignment_multiple_different_collaterals_merge(
    load_bekbe_account, valid_document_category_keys
):

    bekbe_account, bekbe_account_factory = load_bekbe_account

    # Load multiple different collaterals and attempt to merge them
    # They should not merge as they are different
    bekb_dossier = _setup_test_multiple_different_collaterals(bekbe_account_factory)

    # Add 5 extra semantic documents with same collateral, so we have two of each collateral
    for sem_doc in bekb_dossier.dossier.semantic_documents.all():
        new_sem_docs = add_some_fake_semantic_documents(
            bekb_dossier.dossier,
            num_docs=1,
            max_pages=2,
            min_num_pages=2,
            no_page_objects_per_page=1,
            valid_document_category_keys=valid_document_category_keys,
            allow_empty_docs=False,
        )
        assert len(new_sem_docs) == 1
        new_sem_docs[0].collateralassignment = sem_doc.collateralassignment
        new_sem_docs[0].save()

    assert bekb_dossier.dossier.semantic_documents.count() == 10
    assert bekb_dossier.dossier.semantic_pages.count() == 20

    semantic_page_uuids = bekb_dossier.dossier.semantic_pages.values_list(
        "uuid", flat=True
    )

    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=merge_groupings)

    # Should have merged 10 docs into 5, page count should remain the same
    assert bekb_dossier.dossier.semantic_documents.count() == 5
    assert bekb_dossier.dossier.semantic_pages.count() == 20
    assert (
        bekb_dossier.dossier.semantic_pages.filter(uuid__in=semantic_page_uuids).count()
        == 20
    )

    # Keep 5 unique collaterals, so we haven't lost any
    assert (
        bekb_dossier.dossier.semantic_documents.values_list(
            "collateralassignment__collateral", flat=True
        )
        .exclude(collateralassignment__collateral=None)
        .distinct()
        .count()
        == 5
    )

    assert_semantic_pages_order(dossier=bekb_dossier.dossier)


@pytest.mark.parametrize(
    "valid_document_category_keys",
    [["CONSTRUCTION_PLAN"], archive.ALL_GROUP_TYPES["PLAN_ANY"]],
)
def test_partition_grouping_collateral_assignment_multiple_different_collaterals_merge_collateral(
    load_bekbe_account, valid_document_category_keys
):

    bekbe_account, bekbe_account_factory = load_bekbe_account

    # Load multiple different collaterals and attempt to merge them
    # They should not merge as they are different
    bekb_dossier = _setup_test_multiple_different_collaterals(
        bekbe_account_factory, valid_document_category_keys
    )

    # Add 5 extra semantic documents with same collateral, so we have two of each collateral
    # And add one doc without a collateral
    for sem_doc in bekb_dossier.dossier.semantic_documents.all():
        new_sem_docs = add_some_fake_semantic_documents(
            bekb_dossier.dossier,
            num_docs=1,
            max_pages=2,
            min_num_pages=2,
            no_page_objects_per_page=1,
            valid_document_category_keys=valid_document_category_keys,
            allow_empty_docs=False,
        )
        assert len(new_sem_docs) == 1
        new_sem_docs[0].collateralassignment = sem_doc.collateralassignment
        new_sem_docs[0].save()

    # One doc without a collateral
    add_some_fake_semantic_documents(
        bekb_dossier.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=valid_document_category_keys,
        allow_empty_docs=False,
    )

    assert bekb_dossier.dossier.semantic_documents.count() == 11
    assert bekb_dossier.dossier.semantic_pages.count() == 22

    semantic_page_uuids = bekb_dossier.dossier.semantic_pages.values_list(
        "uuid", flat=True
    )

    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=merge_groupings)

    # Should have merged 11 docs into 5, the one without a collateral should have been merged
    # page count should remain the same
    assert bekb_dossier.dossier.semantic_documents.count() == 5
    assert bekb_dossier.dossier.semantic_pages.count() == 22
    assert (
        bekb_dossier.dossier.semantic_pages.filter(uuid__in=semantic_page_uuids).count()
        == 22
    )

    # Keep 5 unique collaterals, so we haven't lost any
    assert (
        bekb_dossier.dossier.semantic_documents.values_list(
            "collateralassignment__collateral", flat=True
        )
        .exclude(collateralassignment__collateral=None)
        .distinct()
        .count()
        == 5
    )

    assert_semantic_pages_order(dossier=bekb_dossier.dossier)


def test_partition_grouping_collateral_assignment_single_collateral_null_collateral(
    load_bekbe_account,
):
    # Test that a single document is not merged

    bekbe_account, bekbe_account_factory = load_bekbe_account

    # Creates a single collateral by default
    bekb_dossier = bekbe_account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    add_some_fake_semantic_documents(
        bekb_dossier.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        allow_empty_docs=False,
    )

    collateral_assignment = (
        bekb_dossier.dossier.semantic_documents.exclude(
            collateralassignment__collateral=None
        )
        .first()
        .collateralassignment
    )

    assert bekb_dossier.dossier.semantic_documents.count() == 2

    semantic_document_list = partition_grouping_collateral_assignment(
        bekb_dossier.dossier.semantic_documents.all()
    )

    assert len(semantic_document_list) == 1
    assert semantic_document_list[0].count() == 2

    assert semantic_document_list[0][0].collateralassignment.collateral

    assert semantic_document_list[0][0].collateralassignment == collateral_assignment

    with pytest.raises(SemanticDocument.collateralassignment.RelatedObjectDoesNotExist):
        semantic_document_list[0][1].collateralassignment.collateral

    # Expect all to be of type CONSTRUCTION_PLAN, as they are all the same
    merge_groupings = calculate_bekb_dossier_semdoc_grouping(bekb_dossier.dossier)

    assert len(merge_groupings["CONSTRUCTION_PLAN"]) == 2

    assert list(merge_groupings.keys()) == ["CONSTRUCTION_PLAN"]

    total_semantic_page_count = bekb_dossier.dossier.semantic_pages.count()

    merge_bekb_semdoc_grouping_for_archive(semantic_document_map=merge_groupings)

    assert bekb_dossier.dossier.semantic_documents.count() == 1

    assert (
        bekb_dossier.dossier.semantic_documents.first().collateralassignment.collateral
    )
    assert (
        bekb_dossier.dossier.semantic_documents.first().collateralassignment
        == collateral_assignment
    )

    assert total_semantic_page_count == bekb_dossier.dossier.semantic_pages.count()

    assert_semantic_pages_order(dossier=bekb_dossier.dossier)


@pytest.mark.parametrize(
    "account_factory,url_namespace,update_statemgmt,EXPORT_STATUS_KEYS",
    [
        (
            BekbAccountFactoryFaker,
            "bekb-api",
            True,
            VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
        ),
        (
            BekbFiplaAccountFactory,
            "fipla-api",
            True,
            VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA,
        ),
    ],
)
def test_create_index_file_bekb_fipla(
    db, account_factory, url_namespace, update_statemgmt, EXPORT_STATUS_KEYS
):
    account_fact = account_factory(
        update_statemgmt=update_statemgmt, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_fact.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=[EXPORT_STATUS_KEYS[0]],
    )

    dossier: Dossier = bekb_dossier_properties.dossier

    dossier.semantic_documents.first()

    creation = datetime(2023, 1, 2, 14, 30, 23, tzinfo=datetime_timezone.utc)

    with temporary_path() as temp_path:
        document_package = prepare_document_package_for_export(
            bekb_dossier_properties, temp_path
        )

        index_file = create_index_file(
            uuid4(),
            bekb_dossier_properties,
            creation,
            document_package,
        )

        assert "GROUP_FIELD_NAME:SCANDATUM" in index_file
        assert "GROUP_FIELD_NAME:PERSCODE" in index_file

        assert "CODEPAGE:819" in index_file
        assert "GROUP_FIELD_NAME:FORMULAR" in index_file
        assert "GROUP_FIELD_NAME:ORDNER" in index_file
        assert "GROUP_FIELD_NAME:FORMNR" in index_file
        assert "GROUP_FIELD_NAME:BATCHID" in index_file
        assert "GROUP_FIELD_NAME:KURZFORM" in index_file
        assert "GROUP_FIELD_NAME:SEITEN" in index_file
        assert "GROUP_FIELD_NAME:FILENAME" in index_file

        assert "GROUP_FIELD_VALUE:1" in index_file
        assert "GROUP_FIELD_VALUE:6" in index_file

        assert "GROUP_FIELD_VALUE:0" in index_file

        assert "GROUP_OFFSET:0" in index_file
        assert "GROUP_LENGTH:0" in index_file
