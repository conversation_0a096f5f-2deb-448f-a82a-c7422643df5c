import uuid
import pytest
from django.urls import reverse
from PyPDF2 import PdfReader
from io import BytesIO

from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from bekb.fakes import BekbAccountFactoryFaker
from bekb.models import BEKBDossierProperties
from bekb.schemas.schemas import AccountNameMortgage, AccountNameFipla
from semantic_document.services_external import (
    create_highlight_annotations,
    create_comment_annotation,
)


@pytest.mark.parametrize(
    "account_name,factory_class,url_namespace",
    [
        (AccountNameMortgage.bekbe, BekbAccountFactoryFaker, "bekb-api"),
        (AccountNameFipla.bekbfiplae, BekbFiplaAccountFactory, "fipla-api"),
    ],
)
def test_export_semantic_document_pdf_with_annotations(
    db,
    bekb_api_client,
    bekb_fipla_api_client,
    account_name,
    factory_class,
    url_namespace,
):
    """Test exporting a semantic document to PDF with both highlight and comment annotations"""

    account_factory = factory_class(
        account_key=account_name,
        update_statemgmt=True,
        default_bucket_name="dms-default-bucket",
    )

    # Create test dossiers using the factory with a specific document category that has EKD mapping
    bekb_dossier: BEKBDossierProperties = (
        account_factory.create_ready_for_export_dossier(
            min_num_documents=1,
            max_num_documents=1,
            allow_empty_docs=False,
            max_pages=2,  # Ensure we have at least 2 pages for different annotation types
            min_num_pages=2,
            valid_document_category_keys=[
                "MORTGAGE_CONTRACT"
            ],  # This has EKD133 mapping
        )
    )
    dossier = bekb_dossier.dossier
    semantic_document = dossier.semantic_documents.first()

    # Validate that we have a document with the correct category
    assert semantic_document is not None, "No semantic document was created"
    assert (
        semantic_document.document_category.name == "MORTGAGE_CONTRACT"
    ), "Wrong document category"

    # Get the first two pages
    semantic_pages = semantic_document.semantic_pages.all()[:2]
    assert len(semantic_pages) == 2, "Not enough pages were created"

    # Add highlight annotations to the first page using dimensions from simply_law example
    highlight1_boxes = [
        {
            "bbox_top": 0.2012014678030303,
            "bbox_left": 0.11800449346405228,
            "bbox_width": 0.03758169934640523,
            "bbox_height": 0.01893939393939394,
        },
        {
            "bbox_top": 0.2012014678030303,
            "bbox_left": 0.15558619281045752,
            "bbox_width": 0.006535947712418301,
            "bbox_height": 0.01893939393939394,
        },
        {
            "bbox_top": 0.2012014678030303,
            "bbox_left": 0.16212214052287582,
            "bbox_width": 0.021241830065359478,
            "bbox_height": 0.01893939393939394,
        },
        {
            "bbox_top": 0.2012014678030303,
            "bbox_left": 0.18499795751633988,
            "bbox_width": 0.09967320261437909,
            "bbox_height": 0.01893939393939394,
        },
    ]
    create_highlight_annotations(
        semantic_page=semantic_pages[0],
        highlight_group_uuid=uuid.UUID("da0e11b4-da30-4f09-b2f8-28e572f421e2"),
        boxes=highlight1_boxes,
        hexcolor="#FFFF00",  # Yellow highlight
    )

    # Add a comment annotation to the second page using dimensions from simply_law example
    create_comment_annotation(
        semantic_page=semantic_pages[1],
        comment_group_uuid=uuid.UUID("ccd2ed50-b997-4590-802c-5214cba7d0f7"),
        text="Comment for Law of simplicity",
        bbox_top=0.8458688261583712,
        bbox_left=0.13301031764575674,
        bbox_width=0.11805649495185508,
        bbox_height=0.1,
        hexcolor="#FFFF00",  # Yellow comment box
    )

    # def mock_publish_side_effect(*args, **kwargs):
    #     request = SemanticDocumentPDFRequestV1.model_validate_json(kwargs["message"])
    #     response_json = process_semantic_dossier_pdf_request(
    #         semantic_document_pdf_request=request
    #     )
    #     set_semantic_document_export_done(response_json)
    #
    # mock_dispatch_publish_request = mocker.patch(
    #     f"{url_namespace.split('-')[0]}.api.publish",
    #     side_effect=mock_publish_side_effect,
    # )

    # Choose the appropriate API client based on the account type
    api_client = (
        bekb_fipla_api_client if url_namespace == "fipla-api" else bekb_api_client
    )

    # Request to generate an export
    response = api_client.post(
        reverse(f"{url_namespace}:process-ready-for-export")
        + f"?account_name={account_name.value}",
        data="{}",
        content_type="application/json",
    )

    assert response.status_code == 201

    # Get the exported PDF
    semantic_document_export = dossier.bekbexport
    assert semantic_document_export is not None, "Export was not created"
    assert semantic_document_export.file, "Export file is missing"

    # Read the PDF content
    pdf_content = semantic_document_export.file.data.read()
    pdf_reader = PdfReader(BytesIO(pdf_content))

    # Verify the PDF has the correct number of pages
    assert len(pdf_reader.pages) == len(semantic_pages)

    # Verify annotations
    # First page should have a highlight annotation
    first_page = pdf_reader.pages[0]
    assert "/Annots" in first_page, "No annotations found in first page"
    assert len(first_page["/Annots"]) > 0, "No highlight annotations found"
    highlight_annot = first_page["/Annots"][0].get_object()
    assert highlight_annot["/Subtype"] == "/Highlight"
    assert highlight_annot["/C"] == [1, 1, 0]  # Yellow in RGB

    # Second page should have content stream for the comment box
    second_page = pdf_reader.pages[1]
    assert second_page.get_contents() is not None, "Comment box content not found"
