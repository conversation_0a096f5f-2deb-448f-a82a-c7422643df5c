import json
import os
import uuid
import pytest

from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from core.authentication import AuthenticatedClient, create_token

from bekb.fakes import BekbAccountFactoryFaker
from bekb.schemas.schemas import Account<PERSON>ameFipla
from dossier.models import Account, DossierUser
from dossier.services import create_expiration_date
from django.conf import settings
from jwcrypto import jwk
import jwt

from projectconfig.jwk import load_jwk_from_env
from bekb.tests.data import DATA_PATH


@pytest.fixture
def load_bekbe_account() -> tuple[Account, BekbAccountFactoryFaker]:
    # Ensures BEKBE account is created with correct categories
    # Not to be confused with BEKB test account
    # BEKBE account is newer dev account used for testing purposes

    bekbe_account_factory = BekbAccountFactoryFaker()

    bekbe_account = bekbe_account_factory.account
    bekbe_account.save()

    return bekbe_account, bekbe_account_factory


@pytest.fixture
def bekb_cdp_dossier():
    bekb_api__account_factory = BekbAccountFactoryFaker(account_key="bekbe")
    bekb_api_account = bekb_api__account_factory.account
    bekb_api_account.cdp_field_set = "bekbFieldSet"
    bekb_api_account.save()
    dossier = bekb_api__account_factory.create_dossier(pers=False).dossier
    return dossier


def check_user(created_user, account: Account):
    dossier_user: DossierUser = DossierUser.objects.prefetch_related(
        "account", "user"
    ).get(user__username=created_user.username, account=account)
    assert dossier_user.user.email == created_user.username
    assert dossier_user.user.first_name == created_user.firstname or (
        created_user.firstname is None and dossier_user.user.first_name == ""
    )
    assert dossier_user.user.last_name == created_user.name
    assert dossier_user.user.username == created_user.username


@pytest.fixture
def bekbfiplae_account_factory():
    bekb_api__account_factory = BekbFiplaAccountFactory(
        account_key=AccountNameFipla.bekbfiplae.value
    )
    bekb_api_account = bekb_api__account_factory.account
    bekb_api_account.save()
    return bekb_api__account_factory


@pytest.fixture(scope="session")
def token_data_bekbfipla():
    return {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "service-bekbfipla-first service-bekbfipla-last",
        "given_name": "service-bekbfipla-first",
        "family_name": "service-bekbfipla-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        "user_roles": ["api_role"],
        "account_key": AccountNameFipla.bekbfiplae.value,
        "jti": str(uuid.uuid4()),
    }


@pytest.fixture(scope="session")
def mock_jwks_public_private():
    # JWKS with both public and private keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
    ).model_dump()


@pytest.fixture(scope="session")
def mock_token_bekbfipla(mock_jwks_public_private, token_data_bekbfipla):
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][1]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    return jwt.encode(
        payload={
            "aud": "account",
            "user_roles": [settings.API_ROLE],
            **token_data_bekbfipla,
        },
        key=pem,
        algorithm="RS256",
    )


@pytest.fixture(scope="session")
def bekb_fipla_api_client():
    token = create_token(
        "FIPLA API given name",
        "FIPLA API family name",
        "<EMAIL>",
        account_key=AccountNameFipla.bekbfiplae.value,
        roles=[settings.BEKB_API_ROLE],
    )
    return AuthenticatedClient(token)
