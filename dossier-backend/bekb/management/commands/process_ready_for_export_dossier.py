from typing import List, Optional

import structlog
import time
import uuid
from datetime import timed<PERSON><PERSON>, datetime

import djclick as click
import schedule
from django.utils import timezone

from bekb.services import (
    process_ready_for_export_dossiers,
)
from dossier.models import Account

logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_name")
def worker(account_name: str):
    """
    Example:

    python manage.py process_ready_for_export_dossier worker bekbe
    python manage.py process_ready_for_export_dossier worker bekbfiplae
    python manage.py process_ready_for_export_dossier worker bekbe,bekbfiplae

    @param account_name:
    @return:
    """

    account_names: List[str] = account_name.split(",")

    accounts: List[Account] = list(Account.objects.filter(key__in=account_names).all())

    if len(accounts) < len(account_names):
        raise Exception(
            f"At least one of the accounts '{account_name}' does not exists. len_accounts={len(accounts)}, accounts={accounts})"
        )

    if accounts is None or len(accounts) == 0:
        logger.error(f"account {account_name} does not exists.")
        return

    def do_it():
        not_updated_since = calc_not_updated_since()
        for account in accounts:
            process_ready_for_export_dossiers(
                account, not_updated_since=not_updated_since
            )

    def show_live_probe():
        print(f"still running at {timezone.now()}")

    schedule.every(10).minutes.do(show_live_probe)
    schedule.every().hour.do(do_it)

    logger.info(
        "starting scheduler",
        accounts=accounts,
        account_names=account_names,
        not_updated_since=calc_not_updated_since(),
    )
    while True:
        schedule.run_pending()
        time.sleep(60)


def calc_not_updated_since(tsnow: Optional[datetime] = None):
    if tsnow is None:
        tsnow = timezone.now()

    if tsnow.hour > 21:
        # Use the timestamp of today at 19:00
        target_time = tsnow.replace(hour=19, minute=0, second=0, microsecond=0)
    else:
        # Use yesterday's 23:00
        target_time = (tsnow - timedelta(days=1)).replace(
            hour=23, minute=0, second=0, microsecond=0
        )

    return target_time


@grp.command()
@click.argument("account_name")
@click.argument("dossier_uuid", type=click.STRING, default="")
def now(account_name: str, dossier_uuid: str = None):
    """
         mcli cp -r exo/production-v2-bekb-dms/946c03bb-a075-4ad6-a802-b7f17524ed62/ local/production-v2-bekb-dms/946c03bb-a075-4ad6-a802-b7f17524ed62/
         mcli cp -r exo/production-v2-bekb-dms/915a3942-4781-45cc-a8af-6a6bdb8e2098/ local/production-v2-bekb-dms/915a3942-4781-45cc-a8af-6a6bdb8e2098/

        mcli mirror --newer-than "30d10h30m"  exo/production-v2-bekb-dms/4559a48c-da15-4175-845b-768492e870a5 local/production-v2-bekb-dms/4559a48c-da15-4175-845b-768492e870a5


        python manage.py process_ready_for_export_dossier now default 915a3942-4781-45cc-a8af-6a6bdb8e2098

        python manage.py process_ready_for_export_dossier now bekbe

        python manage.py process_ready_for_export_dossier now bekbfiplae

        python manage.py process_ready_for_export_dossier now bekbp 4559a48c-da15-4175-845b-768492e870a5
    14f9faa8-e149-4c22-9315-3a66b984855a

        @param account_name:
        @return:
    """
    account = Account.objects.filter(key=account_name).first()

    list_dossier_uuids = []
    if dossier_uuid:
        list_dossier_uuids.append(uuid.UUID(dossier_uuid))

    logger.info(
        f"process_ready_for_export_dossier now({account_name = }, {dossier_uuid = }) -> {list_dossier_uuids = }..."
    )

    if account is None:
        logger.error(f"account {account_name} does not exists.")
        return

    result_uuids = process_ready_for_export_dossiers(account, list_dossier_uuids)
    logger.info(f"processed {result_uuids = }")
