import uuid

import djclick as click
import structlog
from django.contrib.auth import get_user_model

from bekb.services import (
    calculate_bekb_dossier_semdoc_grouping,
    merge_bekb_semdoc_grouping_for_archive,
)
from dossier.factories import (
    DossierFactory,
    SemanticDocumentFactory,
    SemanticPageFactory,
    medium_color_cycle,
)
from dossier.models import Dossier, Account, DocumentCategory, DossierUser
from dossier.services_dmf_v3 import assign_user_to_dossier

logger = structlog.get_logger()

User = get_user_model()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("dossier_uuid", type=click.UUID)
def calculate(
    dossier_uuid: uuid.UUID,
):
    """
    Example:
    python manage.py reset-db

    python manage.py load_bekb_data load-account bekbe

    python manage.py load_bekb_data load-dossiers-all bekbe
    OR
    python manage.py load_bekb_data load-dossiers-group-merge-testing bekbe

    python manage.py bekb_semdoc_grouping_for_archive calculate 254e93ec-c0f2-4133-be04-24170c60e650

    @param dossier_uuid:
    @return:
    """

    semantic_document_map = calculate_bekb_dossier_semdoc_grouping(
        Dossier.objects.get(uuid=dossier_uuid)
    )

    if not semantic_document_map:
        logger.info("Nothing found that can be grouped")
        return


@grp.command()
@click.argument(
    # "dossier_uuid", type=click.UUID, default="f453a847-27a2-45d2-a59b-f74dc50a4495"
    "dossier_uuid",
    type=click.UUID,
)
@click.argument("handle_collateral_assignment", type=click.BOOL, default=True)
def merge(dossier_uuid: uuid.UUID, handle_collateral_assignment: bool = True):
    """
    Example:
    python manage.py reset-db
    python manage.py load_bekb_data load-account bekbe

    Optional: make a copy before merging:
    python manage.py copy_dossier 254e93ec-c0f2-4133-be04-24170c60e650 254e93ec-c0f2-4133-be04-24170c60e65z

    python manage.py add_semdoc_grouping_testing_dossiers_collateral 254e93ec-c0f2-4133-be04-24170c60e650

    python manage.py bekb_semdoc_grouping_for_archive merge uuid

    @param handle_collateral_assignment:
    @param dossier_uuid:
    @return:
    """

    semdoc_map = calculate_bekb_dossier_semdoc_grouping(
        Dossier.objects.get(uuid=dossier_uuid)
    )

    merge_bekb_semdoc_grouping_for_archive(
        semantic_document_map=semdoc_map,
        handle_collateral_assignment=handle_collateral_assignment,
    )

    logger.info(
        "Merge process finished",
        dossier_uuid=dossier_uuid,
        handle_collateral_assignment=handle_collateral_assignment,
    )


@grp.command()
@click.argument(
    # "dossier_uuid", type=click.UUID, default="f453a847-27a2-45d2-a59b-f74dc50a4495"
    "dossier_uuid",
    type=click.UUID,
)
@click.argument("handle_collateral_assignment", type=click.BOOL, default=True)
def create(dossier_uuid: uuid.UUID, handle_collateral_assignment: bool = True):
    """
    Create/Re-create (overwrite) a test dossier for archiving

    Example:
    python manage.py reset-db
    python manage.py load_bekb_data load-account bekbe


    python manage.py bekb_semdoc_grouping_for_archive create

    python manage.py bekb_semdoc_grouping_for_archive merge

    <NAME_EMAIL>


    @param handle_collateral_assignment:
    @param dossier_uuid:
    @return:
    """

    # Delete an existing one if it exists
    Dossier.objects.filter(uuid=dossier_uuid).delete()

    account = Account.objects.get(key="bekbe")

    username = f"{account.key}<EMAIL>"

    test_user1, _ = User.objects.get_or_create(username=username)

    dossier_test_user1, _ = DossierUser.objects.get_or_create(
        user=test_user1, account=account
    )

    dossier = DossierFactory(
        account=account,
        owner=test_user1,
        name="Dossier for archiving",
        uuid=dossier_uuid,
    )

    assign_user_to_dossier(dossier=dossier, dossier_user=dossier_test_user1)

    # 330-EKD51 Lohnausweis (SALARY_CERTIFICATE, Wage statement) Islam_Beka_2023 1,2,3,4 (ordered)
    semantic_document = SemanticDocumentFactory(
        dossier=dossier,
        document_category=DocumentCategory.objects.get(
            account=account, name="SALARY_CERTIFICATE"
        ),
        title_suffix="Islam_Beka_2023",
    )
    color = next(medium_color_cycle)
    for i in range(1, 5):
        SemanticPageFactory(
            dossier=dossier,
            semantic_document=semantic_document,
            page_number=i,
            # Add 64 so we get a capital letter
            additional_text=chr(i + 64),
            color=color,
        )

    # 330-EKD51 Lohnausweis Shpresa Beka 2023 2,3,1

    semantic_document = SemanticDocumentFactory(
        dossier=dossier,
        document_category=DocumentCategory.objects.get(
            account=account, name="SALARY_CERTIFICATE"
        ),
        title_suffix="Lohnausweis Shpresa Beka 2023",
    )
    color = next(medium_color_cycle)
    SemanticPageFactory(
        dossier=dossier,
        semantic_document=semantic_document,
        page_number=1,
        additional_text="C",
        color=color,
    )

    SemanticPageFactory(
        dossier=dossier,
        semantic_document=semantic_document,
        page_number=2,
        additional_text="A",
        color=color,
    )

    SemanticPageFactory(
        dossier=dossier,
        semantic_document=semantic_document,
        page_number=3,
        additional_text="B",
        color=color,
    )

    # 340-EKD51 Salarabrechnung (Salary statement) Islam Beka 2023-11 3,2,1

    semantic_document = SemanticDocumentFactory(
        dossier=dossier,
        document_category=DocumentCategory.objects.get(account=account, name="PAYSLIP"),
        title_suffix="Islam Beka 2023-11",
    )
    color = next(medium_color_cycle)
    for i in range(1, 4):
        SemanticPageFactory(
            dossier=dossier,
            semantic_document=semantic_document,
            page_number=i,
            additional_text=chr(64 - i + 4),
            color=color,
        )

    # 340-EKD51 Salarabrechnung Shpresa Beka 2023-11 3,1,2

    semantic_document = SemanticDocumentFactory(
        dossier=dossier,
        document_category=DocumentCategory.objects.get(account=account, name="PAYSLIP"),
        title_suffix="Shpresa Beka 2023-11",
    )
    color = next(medium_color_cycle)

    for i in range(1, 3):
        SemanticPageFactory(
            dossier=dossier,
            semantic_document=semantic_document,
            page_number=i,
            additional_text=chr(i + 64 + 1),
            color=color,
        )

    SemanticPageFactory(
        dossier=dossier,
        semantic_document=semantic_document,
        page_number=3,
        additional_text="A",
        color=color,
    )

    # 371-EKD51 Leasingvertrag (Leasing contract) 1
    color = next(medium_color_cycle)
    semantic_document = SemanticDocumentFactory(
        dossier=dossier,
        document_category=DocumentCategory.objects.get(
            account=account, name="LEASING_AGREEMENT"
        ),
    )
    SemanticPageFactory(
        dossier=dossier, semantic_document=semantic_document, page_number=1, color=color
    )

    # Should all merged into 368-EKD51 Einkpmmensnachweise (zusammengefasst)

    # lohn Islam: 1,2,3,4 (ordered)
    # lohn Shpresa: 2,3,1
    # salär islam: 3,2,1
    # salär shpresa: 3,1,2
