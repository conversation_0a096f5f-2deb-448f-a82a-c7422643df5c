from enum import Enum


class BekbInstanceType(Enum):
    MORTGAGE = "mortgage"
    FIPLA = "fipla"


def get_bekb_instance_type(account_key: str) -> BekbInstanceType:
    if account_key.startswith("bekbfipla"):
        return BekbInstanceType.FIPLA
    return BekbInstanceType.MORTGAGE


def has_collaterals(instance_type: BekbInstanceType) -> bool:
    return instance_type == BekbInstanceType.MORTGAGE


def has_businesscases(instance_type: BekbInstanceType) -> bool:
    return instance_type == BekbInstanceType.MORTGAGE


def is_bekb_fipla_account(account_key: str) -> bool:
    instance_type = get_bekb_instance_type(account_key)
    return instance_type == BekbInstanceType.FIPLA
