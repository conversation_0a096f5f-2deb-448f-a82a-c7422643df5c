version: '3.8'
services:
  dec:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py dossier_event_consumer
    secrets:
      - source: DMS_DEMO_ENV_V01
        target: /app/.env
  dms:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    networks:
      caddy:
    # this part is needed to register the service at the reverse proxy
    deploy:
      labels:
        caddy: dms.demo.hypodossier.ch
        caddy.reverse_proxy: "{{upstreams 8000}}"
        caddy.import: tls
    environment:
      - DEBUG=1
      - DJANGO_ALLOWED_HOSTS=127.0.0.1:8000 localhost localhost:8000 dms.hypo.duckdns.org dms.demo.hypodossier.ch
      - DJANGO_CORS_ORIGIN_WHITELIST=http://localhost:3000 http://localhost:8000 https://demo.hypodossier.ch https://dms.demo.hypodossier.ch
    secrets:
      - source: DMS_DEMO_ENV_V01
        target: /app/.env
    healthcheck:
      test: curl --fail http://localhost:8000/api/docs || exit 1
      start_period: 30s
      interval: 5s
      timeout: 5s
      retries: 20

secrets:
  DMS_DEMO_ENV_V01:
    external: true


networks:
  default:
  caddy:
    external: true