import json
import os
import uuid
from typing import List

import pytest
from django.conf import settings

from faker import Faker
from pytest_mock import Mo<PERSON><PERSON>ixture

from clientis.schemas.schemas import AccountName
from core.authentication import AuthenticatedClient
from dossier.fakes import add_some_fake_semantic_documents

from dossier.models import (
    Account,
    DocumentCategory,
    JW<PERSON>,
    Dossier,
)
from dossier.services import create_expiration_date
from projectconfig.authentication import get_user_or_create
from projectconfig.jwk import load_jwk_from_env
from clientis.factories import ClientisAccountFactoryFaker
import jwt
from jwcrypto import jwk
from clientis.tests.data import DATA_PATH

from dossier_zipper.conftest import mocked_get_dossier  # noqa: F401
from dossier.management.commands.dossier_event_consumer_v2 import (
    set_semantic_document_export_done,
)
from statemgmt.configurations.semantic_document_state_machine import (
    STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    SemanticDocumentState,
    create_semantic_document_state_machine,
)
from statemgmt.models import StateMachine, Status
from workers import schemas as worker_schemas
from workers.workers import process_semantic_dossier_pdf_request


@pytest.fixture(scope="session")
def faker():
    return Faker(locale="de_CH")


@pytest.fixture
def clientis_account() -> Account:
    account, _ = Account.objects.update_or_create(
        defaults=dict(
            name="clientis development account",
            default_bucket_name="dms-default-bucket",
            dmf_endpoint="https://www.localhost",
        ),
        key=AccountName.clientistest.value,
    )
    # Ensure that a semantic_document state machine is created
    created_objects = create_semantic_document_state_machine()

    semantic_document_state_machine = created_objects["machine"]

    # Associate state machine with account
    account.active_semantic_document_work_status_state_machine = (
        semantic_document_state_machine
    )

    account.save()

    account.refresh_from_db()

    return account


@pytest.fixture(scope="session")
def mock_jwks_public_private():
    # JWKS with both public and private keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
    ).model_dump()


@pytest.fixture
def mock_jwks_public():
    # JWKS with only public keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example-public.json")
    ).model_dump(exclude_unset=True)


@pytest.fixture
def set_clientis_JWK(clientis_account, mock_jwks_public):
    # Set public key used for clientis authentication
    return JWK.objects.create(jwk=mock_jwks_public["keys"][1], account=clientis_account)


@pytest.fixture(scope="session")
def token_data(faker):
    return {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "pytest-service-clientis-first pytest-service-clientis-last",
        "given_name": "pytest-service-clientis-first",
        "family_name": "pytest-service-clientis-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        # "external_dossier_id": schemas.DossierName(
        #     faker.sentence()
        # )
        # during dossier creation via API parameter
        "user_roles": ["api_role"],
        "account_key": AccountName.clientistest.value,
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }


@pytest.fixture(scope="session")
def mock_token(mock_jwks_public_private, token_data):
    # need a PEM-formatted key
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][1]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    return jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,
        algorithm="RS256",
    )


@pytest.fixture
def clientis_account_factory(clientis_account):
    account_factory = ClientisAccountFactoryFaker(account_key=clientis_account.key)

    account = account_factory.account
    account.default_bucket_name = "dms-default-bucket"
    account.save()
    return account_factory


@pytest.fixture
def get_user_or_create_clientis_user(clientis_account_factory):
    return get_user_or_create(
        email="<EMAIL>",
        account=clientis_account_factory.account,
        username="<EMAIL>",
    )


@pytest.fixture
def document_categories(clientis_account_factory) -> List[DocumentCategory]:
    return clientis_account_factory.load_initial_document_categories()


@pytest.fixture(scope="session")
def clientis_authenticated_client(mock_token):
    return AuthenticatedClient(mock_token)


@pytest.fixture(scope="session")
def clientis_miss_signed_authenticated_client(mock_jwks_public_private, token_data):
    """Test auth failure using key signed by clientis"""
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][0]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    wrong_token = jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,  # use wrong key, key [0] from clientis
        algorithm="RS256",
    )
    return AuthenticatedClient(wrong_token)


@pytest.fixture
def create_clientis_dossier(clientis_account_factory) -> tuple[Dossier, str]:
    dossier = clientis_account_factory.create_dossier()

    dossier.external_id = "949.401678.001.110794"
    dossier.save()

    return dossier, dossier.external_id


@pytest.fixture()
def prepare_data_export(
    mocked_get_dossier,  # noqa: W0404
    clientis_authenticated_client,
    clientis_account,
    document_categories,
    set_clientis_JWK,
    mocker: MockerFixture,
    create_clientis_dossier,
):

    state_machine = StateMachine.objects.get(
        name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS
    )
    assert (
        clientis_account.active_semantic_document_work_status_state_machine
        == state_machine
    )

    dossier, external_dossier_id = create_clientis_dossier

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier,
        num_docs=1,
        max_pages=10,
        min_num_pages=10,
        no_page_objects_per_page=10,
        valid_document_category_keys=["PASSPORT_CH"],
        allow_empty_docs=False,
    )
    assert len(semantic_documents) == 1

    # Pick a first one
    semantic_document = semantic_documents[0]

    in_front_office_state = Status.objects.get(
        key=SemanticDocumentState.IN_FRONT_OFFICE.value, state_machine=state_machine
    )

    assert semantic_document.work_status == in_front_office_state

    def mock_publish_side_effect(*args, **kwargs):
        request = worker_schemas.SemanticDocumentPDFRequestV1.model_validate_json(
            kwargs["message"]
        )

        # Process the pdf generation
        # Returns json dump in format SemanticDocumentPDFResponseV1
        process_semantic_document_response = process_semantic_dossier_pdf_request(
            semantic_document_pdf_request=request
        )

        # which is then collected by dossier events consumer
        # and sets event as done
        set_semantic_document_export_done(process_semantic_document_response)

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect,
    )

    return external_dossier_id, semantic_document, mock_dispatch_publish_request
