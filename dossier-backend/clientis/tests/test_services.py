import pytest
from django.contrib.auth import get_user_model
from django.utils import timezone
import datetime

from clientis.services import create_clientis_export_package
from dossier.models import Document<PERSON><PERSON><PERSON>y, DossierUser
from semantic_document.services import create_semantic_document_pdf_filename
from clientis.factories import ClientisAccountFactoryFaker
from clientis.schemas.schemas import AccountName
from dossier.fakes import add_some_fake_semantic_documents

User = get_user_model()


@pytest.mark.parametrize(
    "external_id,document_category_key,has_frameaccount_id,valid",
    [
        ("949.401678.001.110794", "PENSION_CERTIFICATE", False, True),
        ("invalid.format", "PENSION_CERTIFICATE", False, False),
        # Add more test cases here if needed
    ],
)
@pytest.mark.django_db
def test_create_clientis_export_package(
    db, external_id, document_category_key, has_frameaccount_id, valid
):
    # Create a dummy account and dossier using ClientisAccountFactoryFaker
    account_factory = ClientisAccountFactoryFaker(
        account_key=AccountName.clientistest.value,
        default_bucket_name="dms-default-bucket",
    )

    # Load document categories from the factory
    document_categories, _, _, _ = account_factory.load_initial_document_categories()

    # Create the expected user
    user1, _ = User.objects.get_or_create(username="<EMAIL>")
    dossier_user1, _ = DossierUser.objects.get_or_create(
        user=user1, account=account_factory.account
    )

    # Create a dossier with the test user
    dossier = account_factory.create_dossier(current_user=dossier_user1)

    # Set a valid external_id format for testing
    dossier.external_id = external_id
    dossier.lang = "de"
    dossier.save()

    # Get the document category from the loaded categories
    doc_category = DocumentCategory.objects.get(
        name=document_category_key, account=dossier.account
    )
    assert doc_category.id_external

    # Create a semantic document with the specified category using add_some_fake_semantic_documents
    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category_key],
        max_pages=2,
        min_num_pages=2,
        log=False,
    )

    # Get the created semantic document
    semantic_document = semantic_documents[0]

    # Update the title to match our test case
    semantic_document.title_suffix = (
        "Thiemann Manuel AXA Basis Kader 2017-03-01_some_special_chars_äöü/-?"
    )
    semantic_document.title_custom = None
    semantic_document.save()

    # Set the created_at timestamp to match the expected XML output
    created_at = timezone.make_aware(datetime.datetime(2022, 5, 12, 10, 9))
    semantic_document.created_at = created_at
    semantic_document.save()

    # Set the same timestamp on the original file
    first_semantic_page = semantic_document.semantic_pages.first()
    original_file = (
        first_semantic_page.processed_page.processed_file.extracted_file.original_file
    )
    original_file.created_at = created_at
    original_file.save()

    # Re-calculate the title using the model's property method
    base_title = semantic_document.title
    assert (
        base_title
        == "410 PK Ausweis Thiemann Manuel AXA Basis Kader 2017-03-01_some_special_chars_äöü/-?"
    )

    # Create filename by removing invalid chars and appending .pdf
    dokumentenname = create_semantic_document_pdf_filename(base_title)

    # remove all special chars we no not want in a filename
    assert (
        dokumentenname
        == "410 PK Ausweis Thiemann Manuel AXA Basis Kader 2017-03-01_some_special_chars_äöü-.pdf"
    )

    # Get the actual number of non-deleted pages for the test
    page_count = semantic_document.semantic_pages.alive().count()

    if not valid:
        with pytest.raises(ValueError):
            create_clientis_export_package(semantic_document, dokumentenname)
        return

    # Call the function
    xml_bytes = create_clientis_export_package(
        semantic_document, dokumentenname
    ).to_xml()

    # Convert bytes to string for assertion
    xml_str = xml_bytes.decode("utf-8")

    # Assert expected values in XML
    expected_xml = (
        "<?xml version='1.0' encoding='UTF-8'?>\n"
        "<ClientisExport>\n"
        "  <Mandant>949</Mandant>\n"
        "  <Kundennummer>401678</Kundennummer>\n"
        f"  <Dokumententyp>{doc_category.id_external}</Dokumententyp>\n"
        "  <DokumentenID></DokumentenID>\n"
        "  <Auffindcode>0</Auffindcode>\n"
        f"  <Dokumentenname>{dokumentenname}</Dokumentenname>\n"
        "  <DokumentenRECID></DokumentenRECID>\n"
        "  <Dokumentendatum>2022-05-12</Dokumentendatum>\n"
        "  <Kontonummer></Kontonummer>\n"
        "  <Nummernkontocode>0</Nummernkontocode>\n"
        "  <Personalcode>0</Personalcode>\n"
        "  <Portfolionummer></Portfolionummer>\n"
        f"  <Rahmennummer>{'001' if has_frameaccount_id else ''}</Rahmennummer>\n"
        "  <Scandatum>2022-05-12 10:09</Scandatum>\n"
        "  <ClientAdvisorId></ClientAdvisorId>\n"
        "  <CreatorId><EMAIL></CreatorId>\n"
        f"  <Dokumentenbezeichnung>{base_title}</Dokumentenbezeichnung>\n"
        "  <Kommentar></Kommentar>\n"
        f"  <Seiten>{page_count}</Seiten>\n"
        "  <DirectArchive>False</DirectArchive>\n"
        "  <SourceSystem>HypoDossier</SourceSystem>\n"
        "</ClientisExport>\n"
    )
    assert xml_str == expected_xml


def normalize_newlines(text: str) -> str:
    return text.replace("\r\n", "\n").replace("\r", "\n")
