@startuml Transfer documents and Close Dossier with HypoDossier API
autonumber "<b>[0]"
skinparam SequenceMessageAlign center

box CLIENTIS System  #LightBlue
    participant "eDossier / Clientis Integration" as mfiles
end box

box Hypodossier #LightGreen
    participant "Hypodossier Frontend\n(clientis.hypodossier.ch)" as hypo_frontend
    participant "Hypodossier Clientis API" as hypo_api
end box

title Document Export and Dossier Closing Workflow

== 1. Trigger Transfer ==
' This section shows how documents might become ready, leading up to the API interaction '
actor "Standard User" as clientisuser

alt User manually marks dossier transfer to eDossier
    clientisuser -> hypo_frontend: But<PERSON> Click "Transfer Document to eDossier"
    activate clientisuser
    activate hypo_frontend
    hypo_frontend -> hypo_api: set read only
    activate hypo_api
    hypo_api -> hypo_api: Prepare documents for export
    deactivate hypo_api
    deactivate hypo_frontend
    deactivate clientisuser
end



== 2. Document Export (Polling by Clientis System) ==
' This is the main loop driven by the Clientis external system '

activate mfiles
note over mfiles: Scheduled Task (e.g., every 5 mins)
mfiles -> hypo_api: **GET** /export/all-semantic-documents-available\n(Poll for documents)
activate hypo_api
hypo_api --> mfiles: JSON List of ExportStatusSemanticDocument\n(Filter for status EXPORT_AVAILABLE)
deactivate hypo_api

loop for each document with status EXPORT_AVAILABLE in response
    note over mfiles: Process one document at a time
    ' Extract dossier_id, semantic_document_uuid, download_url from list item '

    mfiles -> hypo_api: **POST** /export/{dossier_id}/semantic-documents/{semantic_document_uuid}/set-export-in-progress
    activate hypo_api
    hypo_api --> mfiles: 200 OK (Confirms status changed internally)
    deactivate hypo_api

    note over mfiles: Download ZIP via\nsemantic_document_url (provided in polling response)\nRequires Auth Header
    mfiles -> mfiles: (External HTTP GET to download_url)
    ' ... Download occurs ... '
    mfiles -> mfiles: Process ZIP (Extract PDF, XML), Archive PDF\nusing XML Metadata

    alt Archiving Successful
        mfiles -> hypo_api: **POST** /export/{dossier_id}/semantic-documents/{semantic_document_uuid}/set-done
        activate hypo_api
        hypo_api --> mfiles: 200 OK (Confirms export completion internally)
        deactivate hypo_api
    else Archiving Failed
        mfiles -> hypo_api: **POST** /export/{dossier_id}/semantic-documents/{semantic_document_uuid}/set-error\n(Optional: body with error message)
        activate hypo_api
        hypo_api --> mfiles: 200 OK (Confirms export error internally)
        deactivate hypo_api
    end
end loop
deactivate mfiles

== 3. Dossier Closing (Triggered by Clientis System) ==
' This process finalizes the dossier after exports are handled '

activate mfiles
note over mfiles: Triggered when Clientis process\ndetermines dossier should be closed.

' Optional but recommended check '
mfiles -> hypo_api: **GET** /dossier/{dossier_id}/check-dossier-close-ready
activate hypo_api
hypo_api --> mfiles: JSON DossierCloseReadyResponse\n(ready_for_close: true/false, details)
deactivate hypo_api

alt Dossier is Ready (ready_for_close: true) or Force Prepare
    mfiles -> hypo_api: **POST** /dossier/{dossier_id}/prepare-close-dossier\n?close_dossier_if_possible=true (or false)
    activate hypo_api
    ' Prepare call might trigger exports for remaining docs (if any) '
    ' If close_dossier_if_possible=true and conditions met, it might also attempt final close '
    hypo_api --> mfiles: JSON PrepareCloseDossierResponse\n(success: true/false, exports_triggered_count, close_result)
    deactivate hypo_api

    alt Prepare succeeded (success: true) AND Dossier not already closed by prepare call
        note over mfiles: If exports were triggered by 'prepare',\nwait until polling (Section 2) confirms\nall documents are finalized.\nRe-check readiness if needed.

        mfiles -> hypo_api: **POST** /dossier/{dossier_id}/close-dossier
        activate hypo_api
        hypo_api --> mfiles: JSON DossierCloseResponse\n(success: true/false, message if failed)
        deactivate hypo_api
    else Prepare failed or dossier already closed
         note over mfiles: Handle failure based on response.
    end
else Dossier Not Ready (ready_for_close: false)
    note over mfiles: Review response messages (msg_nok_*).\nResolve issues (e.g., wait for exports,\nuser action needed in HypoDossier). Retry later.
end
deactivate mfiles

@enduml