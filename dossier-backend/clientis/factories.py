from typing import Op<PERSON>, Tu<PERSON>, List, Any

import structlog

from assets import ASSETS_PATH
from clientis.document_category_mapping_external_id import (
    update_external_id_to_document_categories_clientis,
)
from clientis.schemas.schemas import AccountName

from dossier.factories import (
    set_new_account_defaults,
    DefaultAccountFactoryFaker,
)
from dossier.fakes import load_initial_document_categories
from dossier.management.commands.duplicate_sample_dossier_to_account import (
    generate_random_clientis_external_id,
)
from dossier.models import (
    Account,
    NavigationStrategy,
    DocumentCategory,
    DossierCloseStrategy,
    <PERSON><PERSON>rUser,
    <PERSON><PERSON>r,
    SemanticDocumentExportStrategy,
)
from clientis.schemas import schemas
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
)
from dossier.dossier_work_status import (
    DOSSIER_STATE_MACHINE_NAME,
    PATH_DOSSIER_STATE_MACHINE_EXPORT,
)
from statemgmt.export import update_state_machine
from statemgmt.models import StateMachine

logger = structlog.get_logger()


FRONTEND_THEME_FINNOVA: str = "FINNOVA"


def load_clientis_custom_prop_document_categories(account_key):
    """
    Add custom proprietary document categories for clientis banks
    @param account_key:
    @return:
    """
    if account_key in [AccountName.dcb.value, AccountName.dcbtest.value]:
        # TODO: Add custom document CLIENTIS_DCB_ARBEITSHILFE_FIN
        account = Account.objects.get(key=account_key)
        doccat, _ = DocumentCategory.objects.update_or_create(
            defaults={
                "id": "700-DCB50",
                "id_external": "201360",
                "de": "Arbeitshilfe Auszahlungs- und Schlusskontrolle",
                "en": "Arbeitshilfe Auszahlungs- und Schlusskontrolle",
                "fr": "Arbeitshilfe Auszahlungs- und Schlusskontrolle",
                "it": "Arbeitshilfe Auszahlungs- und Schlusskontrolle",
                "description_de": "Dokumentation über die Auszahlungs- und Schlusskontrolle",
                "de_external": "3.06.18_Auszahlungs-/Schlusskontrolle",
                "en_external": "3.06.18_Auszahlungs-/Schlusskontrolle",
                "fr_external": "3.06.18_Contrôle des versements/du bouclement",
                "it_external": "3.06.18_Auszahlungs-/Schlusskontrolle",
            },
            name="CLIENTIS_DCB_ARBEITSHILFE_FIN",
            account=account,
        )
        assert (
            doccat
        ), "Could not create and did not find document category CLIENTIS_DCB_ARBEITSHILFE_FIN"

        return 1

    return 0


def load_clientis_document_categories(account_key) -> Tuple[bool, List, List]:

    # This is the latest doc cat definition that is verified for bcge
    document_categories_json_path = (
        ASSETS_PATH / "document_category/default/DocumentCategory-2024-11-13.json"
    )
    account = Account.objects.get(key=account_key)
    load_initial_document_categories(
        account, document_categories_json_path=document_categories_json_path
    )

    # TODO: If we want to add custom document categories that apply to all clientis banks uncomment this
    # document_categories_json_path_custom = (
    #     ASSETS_PATH / "document_category/clientis/DocumentCategory-2024-09-25.json"
    # )
    # document_categories_custom, _, _, _ = load_initial_document_categories(
    #     account, document_categories_json_path_custom
    # )

    # Now add the external id based on a local file for all document categories
    # File format CSV with heading: Translation
    # Doc ID: Document ID
    # Kategorie: Category
    # Name: Name
    # Titel DE: Title (German)
    # Titel FR: Title (French)
    # Titel EN: Title (English)
    # Titel IT: Title (Italian)
    # Beschreibung: Description
    # Misc Type: Miscellaneous Type
    # CLI_Dokttyp: CLI Document Type
    # CLI_Doktyp_Bezeichnung_d: CLI Document Type Designation (German)
    # CLI_Doktyp_Bezeichnung_f: CLI Document Type Designation (French)
    # Bemerkung Martin: Martin's Note/Comment
    doccat_mapping_csv_path = (
        ASSETS_PATH
        / "document_category/clientis/document_category_mapping/20250127_Mapping_Hypodossier_Spezifikation_Aktenplan_DEFAULT.csv"
    )
    success_mapping, updates, not_found = (
        update_external_id_to_document_categories_clientis(
            account, doccat_mapping_csv_path
        )
    )
    if success_mapping:
        logger.info(
            "All HD document categories were successfully mapped to clientis external_id",
            success_mapping=success_mapping,
            updates=updates,
            not_found=not_found,
        )
    else:
        logger.info(
            "Could not find external id mapping for all document categories. This might be ok doc_cat_key is returned as external_id as fallback",
            num_not_found=len(not_found),
            num_updates=len(updates),
        )
        if not_found:
            logger.info("not_found_in_mapping", not_found=not_found)

    for doccat_name in ["CORRESPONDENCE_NOTARY", "LETTER_COMMITMENT_NOTARY"]:
        dc, created = DocumentCategory.objects.update_or_create(
            defaults={"exclude_for_recommendation": True},
            name=doccat_name,
            account=account,
        )
        assert dc, f"Cound not find doccat {doccat_name}"
        assert (
            not created
        ), f"Doc cat must exist before: {dc.name} {dc.id} {dc.id_external}"

    # Temporarily disabled
    # num_doccats_added: int = load_clientis_custom_prop_document_categories(account_key)
    # assert num_doccats_added == 1

    assert len(updates) == 264, f"Expected 264 updates, got {len(updates)}"

    return success_mapping, updates, not_found


def update_or_create_semantic_document_workflow(account: Account) -> Account:
    """
    Configure the semantic document workflow for the account.

    @param account: The account to configure
    @return: The updated account
    """
    # Configure document export
    account.enable_semantic_document_export = True
    created_objects = create_semantic_document_state_machine()
    state_machine = created_objects["machine"]
    # Associate state machine with account
    account.active_semantic_document_work_status_state_machine = state_machine

    # This is needed to customize the "Übermittlung an Archiv" button
    account.frontend_theme = FRONTEND_THEME_FINNOVA
    account.enable_semantic_document_export_unknown_documents = True
    account.dossier_close_strategy = (
        DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE
    )
    account.dossier_close_expiry_days = 30

    return account


def update_or_create_clientis_account(
    account_key: str,
    update_statemgmt: bool = False,
    default_bucket_name: Optional[str] = None,
) -> Account:
    assert is_valid_account_key(
        account_key
    ), f"Invalid account key: '{account_key}'. Valid keys are {[a.value for a in schemas.AccountName]}"

    account, _ = Account.objects.update_or_create(key=account_key)

    state_machine_name = DOSSIER_STATE_MACHINE_NAME
    state_machine = StateMachine.objects.filter(name=state_machine_name).first()
    if update_statemgmt or state_machine is None:
        p = PATH_DOSSIER_STATE_MACHINE_EXPORT
        assert p.exists()
        state_machine = update_state_machine(p, state_machine_name)
        account.active_work_status_state_machine = state_machine
    assert state_machine is not None

    account.semantic_document_export_strategy = (
        SemanticDocumentExportStrategy.SWISSCOM_EDOSSIER_XML_ZIP
    )

    clientis_dmf_endpoint = "https://clientis.hypodossier.ch"

    if account_key == schemas.AccountName.bbr.value:
        account.name = "Biene Bank im Rheintal Genossenschaft Production"
        account.default_bucket_name = "production-v2-bbr-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.boa.value:
        account.name = "Bank Oberaargau AG Production"
        account.default_bucket_name = "production-v2-boa-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.cba.value:
        account.name = "Clientis Bank Aareland AG Production"
        account.default_bucket_name = "production-v2-cba-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.cbo.value:
        account.name = "Clientis Bank Oberuzwil AG Production"
        account.default_bucket_name = "production-v2-cbo-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.cbt.value:
        account.name = "Clientis Bank Toggenburg AG Production"
        account.default_bucket_name = "production-v2-cbt-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.cco.value:
        account.name = "Caisse d'Epargne de Cossonay société coopérative Production"
        account.default_bucket_name = "production-v2-cco-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.cec.value:
        account.name = "Caisse d'Epargne Courtelary SA Production"
        account.default_bucket_name = "production-v2-cec-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.dcb.value:
        account.name = "DC Bank Production"
        account.default_bucket_name = "production-v2-dcb-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.ebs.value:
        account.name = "Clientis EB Entlebucher Bank AG Production"
        account.default_bucket_name = "production-v2-ebs-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.eks.value:
        account.name = "Ersparniskasse Schauffhausen AG Production"
        account.default_bucket_name = "production-v2-eks-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.spc.value:
        account.name = "Sparcassa 1816 Genossenschaft Production"
        account.default_bucket_name = "production-v2-spc-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.zlb.value:
        account.name = "Zürcher Landbank AG Production"
        account.default_bucket_name = "production-v2-zlb-dms"
        account.dmf_endpoint = clientis_dmf_endpoint

    # test accounts
    elif account_key == schemas.AccountName.bbrtest.value:
        account.name = "Biene Bank im Rheintal Genossenschaft Test"
        account.default_bucket_name = "production-v2-test-bbrtest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.boatest.value:
        account.name = "Bank Oberaargau AG Test"
        account.default_bucket_name = "production-v2-test-boatest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.cbatest.value:
        account.name = "Clientis Bank Aareland AG Test"
        account.default_bucket_name = "production-v2-test-cbatest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.cbotest.value:
        account.name = "Clientis Bank Oberuzwil AG Test"
        account.default_bucket_name = "production-v2-test-cbotest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.cbttest.value:
        account.name = "Clientis Bank Toggenburg AG Test"
        account.default_bucket_name = "production-v2-test-cbttest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.ccotest.value:
        account.name = "Caisse d'Epargne de Cossonay société coopérative Test"
        account.default_bucket_name = "production-v2-test-ccotest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.cectest.value:
        account.name = "Caisse d'Epargne Courtelary SA Test"
        account.default_bucket_name = "production-v2-test-cectest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.dcbtest.value:
        account.name = "DC Bank Test"
        account.default_bucket_name = "production-v2-test-dcbtest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.ebstest.value:
        account.name = "Clientis EB Entlebucher Bank AG Test"
        account.default_bucket_name = "production-v2-test-ebstest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.ekstest.value:
        account.name = "Ersparniskasse Schauffhausen AG Test"
        account.default_bucket_name = "production-v2-test-ekstest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.spctest.value:
        account.name = "Sparcassa 1816 Genossenschaft Test"
        account.default_bucket_name = "production-v2-test-spctest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.zlbtest.value:
        account.name = "Zürcher Landbank AG Test"
        account.default_bucket_name = "production-v2-test-zlbtest"
        account.dmf_endpoint = clientis_dmf_endpoint

    elif account_key == schemas.AccountName.clientistest.value:
        account.name = "Clientis Test"
        account.default_bucket_name = "production-v2-test-clientistest"
        account.dmf_endpoint = "https://clientistest.test.hypodossier.ch"

        account.navigation_strategy = NavigationStrategy.NO_DOSSIER_LIST_BRANDED
        account.enable_button_create = True
        account.enable_feedback_form = False
        account.enable_uploading_files = True
        account.enable_document_upload = True
        account.allow_dossier_listing = True
        account.show_document_category_external = True

    else:
        raise ValueError(f"Invalid account key '{account_key}'")

    if default_bucket_name:
        account.default_bucket_name = default_bucket_name

    set_new_account_defaults(account)

    # Standard config for clientis
    account.max_dossier_expiry_duration_days = 500

    # To allow 20 days for final archiving process
    account.default_dossier_expiry_duration_days = 520
    account.valid_dossier_languages = ["De", "Fr", "En"]
    account.valid_ui_languages = ["de", "fr", "en"]
    account.show_document_category_external = True
    account.show_business_case_type = False
    account.enable_virus_scan = False
    account.enable_rendering_hurdles_tab = False
    account.enable_dossier_permission = False
    account.enable_dossier_assignment = False

    # Download will most likely happen via API. That will provide the document_category.
    # No need to put it into the attachment for now
    account.enable_download_metadata_json = False

    # Configure semantic document workflow
    account = update_or_create_semantic_document_workflow(account)

    return account


# Function to validate if the string is part of the enum
def is_valid_account_key(account_key: str) -> bool:
    try:
        # Attempt to match the account_key with an enum member
        schemas.AccountName(account_key)
        return True
    except ValueError:
        # If account_key is not found in the enum, return False
        return False


class ClientisAccountFactoryFaker(DefaultAccountFactoryFaker):
    def __init__(
        self,
        # Note account_key is account_name on the model
        account_key: Optional[str] = None,
        default_bucket_name: Optional[str] = None,
    ):
        is_valid_account_key(account_key)
        account: Account = update_or_create_clientis_account(
            account_key=account_key, default_bucket_name=default_bucket_name
        )

        super().__init__(account, "de_CH")

    def load_initial_document_categories(self) -> tuple[list[Any], bool, list, list]:

        success_mapping, updates, not_found = load_clientis_document_categories(
            self.account.key
        )

        self.document_categories = list(
            DocumentCategory.objects.filter(account=self.account).all()
        )

        return self.document_categories, success_mapping, updates, not_found

    def create_dossier(
        self,
        current_user: DossierUser = None,
    ) -> Dossier:

        dossier: Dossier = super().create_dossier(current_user)

        dossier.external_id = generate_random_clientis_external_id()

        dossier.save()

        return dossier
