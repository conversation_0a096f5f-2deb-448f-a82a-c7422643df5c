# How to set up backend for development (in container)

## Basics

1. Open folder 'backend' in Pycharm
2. Configure poetry interpreter
3. Create auth.toml from gitlab and copy to dossier-backend
4. Build container locally (repeat after new libraries are added)
5. Run this before committing: ruff . --fix; black .; pylama
```
# Optionally: cd dossier-backend
bash build.sh
```
5. Only after rebuilding the container, sync needs to be triggered once. Afterwards sync works automatically 
   1. click on dossier-backend
   2. Tools -> Deployment -> Upload to root@localhost:22002

## Local setup of dms for local debugging of dms
```
# Optionally remove old stack
docker stack rm dms
# Deploy all 4 services in stack
TAG=v1.49.0 docker stack deploy -c docker-compose.stack.yml dms --with-registry-auth --prune
# Disable dms because we want to run it from Pycharm
docker service scale dms_dms=0
# Start dms container so we can connect to it from Pycharm
```
docker-compose -f docker-compose.dev-mt.yml up --remove-orphans
```
- scale down container so we can run the local one: docker service scale dms_dms=0
- mark top level 'dossier-backend' dir, select from menu 'Tools' -> 'Deployment' ->  'Upload to root@localhost...'
- run dossier-backend from Pycharm (with Remote SSH Interpreter)
```

### Doing migrations in dev environment

- Version A:
  - python shell
  - python manage.py migrate

- Version B:
  - log into locally started container: docker exec -it dossier-backend_dms_1 bash (see available containers via docker ps)
  - somehow .env seems to be missing here... therefore error....

## Local setup for debugging dossier zipper
```
docker service scale dms_dzw=0

```

6. Log into container 
```
docker-compose -f docker-compose.dev.yml run dms bash
```

7. If needed run migrations (should be done in container but would also work locally)
```
docker-compose -f docker-compose.dev.yml run dms bash
```


## Start up backend after restart
```
docker-compose -f docker-compose.dev.yml up --remove-orphans

TAG=c19c1d4dcd348774c4e2d6b952377ff194240815 docker stack deploy -c docker-compose.stack.yml dms --with-registry-auth --prune

```
- trigger sync once (as described above)
- run "dossier-backend" from PyCharm configuration (via ssh into container)

## Configure interpreter for Pycharm via SSH into container
Project -> Add Interpreter -> SSHInterpreter

Host:  localhost
Port: 22002 (as mapped in docker-compose.dev.yml)
User: root
Interpreter: /usr/local/bin/python3 (this should result in a Python 3.8.x interpreter version)

This syncs all local files to a folder inside the container indicated on the last setup screen, e.g. /tmp/pycharm_project_930

## Start / Stop backend container
```
 docker-compose -f docker-compose.dev.yml up dms 
 docker-compose -f docker-compose.dev.yml down dms 
 
 (do this if you get "Listen failure: Couldn't listen on 0.0.0.0:8000: [Errno 98] Address already in use.", then re-sync, then run again
 
```

## Run frontend container based on tag (no building needed)
Inside dmf project: 
```
TAG=v1.18.2 docker stack deploy -c docker-compose.stack.yml dms --with-registry-auth --prune
```

## Create run configuration
1. Edit configurations
2. '+' -> Django Server
   1. Name 'dossier-backend'
   2. Host 0.0.0.0, Port 8000, Url http://0.0.0.0:8000/

Now django server can be used in run / debug

## Change Django datamodel
1. Add fields to a model in models.py, use default if possible
2. poetry shell
3. python manage.py makemigrations
4. python manage.py migrate

## Logs
Event consumer Queue
1. Check in rabbitmq that queue is in mode 'quorum'. If not, delete queue. It will be recreated after some seconds: https://rabbitmq-mgmt.hypo.duckdns.org/#/queues/dossier-processor/DossierEvents.dev.DossierEventV1
```
docker service logs -f dms_dec
```

## Run dossier_event_consumer_v2 locally

Create a PyCharm run configuration of type "Python"
- select "manage.py" from local dir as script
- param is dossier_event_consumer_v2
- Python is remote python via ssh as above

## Network stuff

Locally db is reachable on 127.0.0.1 
```
ping pgcluster1.hypo.duckdns.org
```

Inside db is reachable via core services (e.g. on ********), thanks to docker-compose.dev.yml
```
docker-compose -f docker-compose.dev.yml run dms bash
ping pgcluster1.hypo.duckdns.org
```

docker-compose -f docker-compose.test.yml up

## DB Stuff
Log into db (works locally or in container) 
```
psql -h pgcluster1.hypo.duckdns.org -U dms
```


-------------------------
Steps for new business cases deployment BEKB


