from collections import Counter
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path
from typing import List, Union, Optional
from uuid import UUID

import httpx
import structlog
from asgiref.sync import sync_to_async
from django.conf import settings
from django.db import transaction
from django.db.models import Max
from django.http import HttpResponseRedirect, Http404, FileResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from httpx._types import URLTypes
from ninja import NinjaAPI, UploadedFile, File, Form
from ninja.errors import ValidationError
from ninja.openapi.schema import OpenAPISchema
from ninja.operation import Operation
from ninja.responses import Response
from ninja.security import HttpBearer
from ninja.types import DictStrAny
from pydantic import HttpUrl

from core.publisher import publish
from core.schema import Message, Error
from dossier import models as dossier_models, jwt_extract
from dossier import schemas as dossier_schema
from dossier import services_external as dossier_services_external
from dossier.helpers_api import handle_api_validation_error
from dossier.models import (
    Account,
    RealestateProperty,
    AssignedRealestatePropertyOriginalFile,
    OriginalFile,
    Dossier,
)
from dossier.schemas import EntityTypes
from dossier.services_external import (
    update_or_create_real_estate_property,
    serialize_semantic_document,
    create_dossier_api,
    update_dossier_api,
    get_dossier_with_access_check_api,
)
from processed_file.schemas import PageObjectSchema
from projectconfig.authentication import (
    authenticate_from_account,
    async_auth,
)
from projectconfig.settings import (
    ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
)
from semantic_document.models import SemanticDocument
from semantic_document.services import (
    export_semantic_document_wrapper_with_access_check,
)
from processed_file.services import (
    export_aggregate_page_objects_from_semantic_document_with_title,
)
from semantic_document import (
    helpers as semantic_document_helpers,
    models as semantic_document_models,
    schemas as semantic_document_schemas,
)
from workers.models import SemanticDocumentExport
from zkb.schemas import schemas

from zkb.schemas.schemas import (
    SemanticDocumentPDFExportRequest,
    ExportDossierExport,
)
from semantic_document.services import map_confidence

logger = structlog.get_logger()


class ZKBJWTAuth(HttpBearer):
    def authenticate(self, request, token, *args, **kw):
        jwt = authenticate_from_account(token)
        if settings.API_ROLE not in jwt.user_roles:
            return
        return jwt


# Used only for un-authenticated show_dossier endpoint, the rest use account key from JWAuth
ACCOUNT_KEY = settings.API_ACCOUNT_KEY_ZKB

api = NinjaAPI(
    title="Hypodossier - ZKB API",
    csrf=False,
    auth=ZKBJWTAuth(),
    urls_namespace="zkb-api",
    version="1.13.0",
    servers=[],
)


@api.exception_handler(ValidationError)
def custom_validation_errors(request, exc):
    return handle_api_validation_error(api, logger, exc, request)


class OpenAPISchemaWithoutAuth(OpenAPISchema):
    def __init__(self, api: "NinjaAPI", path_prefix: str) -> None:
        super().__init__(api, path_prefix)

    def operation_security(self, operation: Operation) -> Optional[List[DictStrAny]]:
        return None


def openapi_without_auth(request):
    return Response(OpenAPISchemaWithoutAuth(api, str(Path(request.path).parent)))


# @api.get("/dossier", response={200: List[schemas.Dossier]})
# def get_dossiers(request):
#     return [
#         map_dossier(dossier)
#         for dossier in dossier_models.Dossier.objects.filter(account__key=ACCOUNT_KEY, expiry_date__gt=now()).all()
#     ]


def map_dossier(dossier: Dossier):
    # Same as SWISSFEX, but keep duplicate code, as dossier schema might diverge
    return schemas.Dossier(
        uuid=dossier.uuid,
        external_dossier_id=str(dossier.external_id),
        updated_at=dossier.updated_at,
        created_at=dossier.created_at,
        name=dossier.name,
    )


@api.get("/ping", response={200: Message}, url_name="ping", exclude_none=True)
def ping(request):
    return Message(detail="pong")


@api.post(
    "/dossier",
    response={201: schemas.Dossier, 409: Error},
    url_name="create-dossier",
    exclude_none=True,
)
def create_dossier(request, dossier_create: schemas.CreateDossier):

    return create_dossier_api(
        request=request, dossier_create=dossier_create, language="de"
    )


@api.patch(
    "/dossier/{external_dossier_id}",
    response={201: schemas.Dossier, 409: Error, 404: Message},
    url_name="update-dossier",
    exclude_none=True,
)
def update_dossier(
    request, external_dossier_id: str, dossier_change: schemas.ChangeDossier
):
    """Updates a new Dossier based on the provided parameters

    We provide a external_dossier_id as part of the URL and allow the client to change it
    as part of ChangeDossier
    """

    return update_dossier_api(
        request=request,
        external_dossier_id=external_dossier_id,
        dossier_change=dossier_change,
    )


@api.put(
    "/dossier/{external_dossier_id}/entities/realestateproperty",
    response={
        200: schemas.RealEstatePropertyResponse,
        201: schemas.RealEstatePropertyResponse,
        404: Message,
    },
    url_name="create-update-real-estate-property",
    exclude_none=True,
)
def create_or_update_real_estate_property(
    request, external_dossier_id, property: schemas.RealEstatePropertyCreate
):
    """Create or update a new RealEstateProperty for a Dossier"""

    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    # Interface around dossier_models.RealEstateProperty
    property_object = dossier_schema.RealEstateProperty(**property.model_dump())

    return_value, real_estate_property = update_or_create_real_estate_property(
        dossier=dossier, property=property_object
    )

    return return_value, schemas.RealEstatePropertyResponse.to_schema(
        real_estate_property
    )


@api.get(
    "/dossier/{external_dossier_id}/entities/realestateproperties/",
    response={200: List[schemas.RealEstatePropertyResponse], 404: Message},
    url_name="get-real-estate-properties",
    exclude_none=True,
)
def get_real_estate_properties(request, external_dossier_id: str):
    """Get all real estate properties assigned to a dossier"""
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    return [
        schemas.RealEstatePropertyResponse.to_schema(x)
        for x in RealestateProperty.objects.filter(dossier=dossier).all()
    ]


@api.post(
    "/dossier/{external_dossier_id}/original-files",
    response={201: dossier_schema.CreatedObjectReference, 409: Message, 404: Message},
    url_name="add-original-file",
    exclude_none=True,
)
@transaction.atomic
def add_original_file(
    request,
    external_dossier_id: str,
    file: UploadedFile = File(...),
    entity_type: Union[
        None,
        EntityTypes,
    ] = Form(
        None
    ),  # Realestateproperty, always optional, add ENUM contain
    # RealestateProperty
    entity_key: Optional[str] = Form(None),  # Realestateproperty.key
    allow_duplicate_and_rename: bool = Form(False),
):
    """Add an original file to a dossier and optionally link it to a real estate property
    If the estate property does not exist, it will be created with the entity_key provided
    but will be missing additional information such as title, floor, street, street_nr, zipcode, city
    """
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    response_code, original_file = dossier_services_external.add_original_file(
        dossier=dossier,
        file=file,
        allow_duplicate_and_rename=allow_duplicate_and_rename,
    )

    if entity_type == EntityTypes.REALESTATEPROPERTY and entity_key is not None:
        real_estate_property, _ = RealestateProperty.objects.get_or_create(
            dossier=dossier, key=entity_key
        )

        AssignedRealestatePropertyOriginalFile.objects.filter(
            originalfile=original_file
        ).all().delete()

        AssignedRealestatePropertyOriginalFile.objects.create(
            originalfile=original_file, realestate_property=real_estate_property
        )

        original_file.refresh_from_db()

    return response_code, original_file


@api.get(
    "/dossier/{external_dossier_id}/filename/{original_filename}",
    response={200: bool},
    url_name="check-original-filename",
    exclude_none=True,
    description="Check if a file with the same name already exists in the dossier. Return True if file already exists, else False",
)
def check_original_filename(
    request,
    external_dossier_id: str,
    original_filename: str,
):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    original_files = OriginalFile.objects.filter(dossier=dossier)

    if original_files.exists():
        for original_file in original_files:
            if original_file.file.name == original_filename:
                return 200, True

    return 200, False


@api.get(
    "/dossier/{external_dossier_id}/file-status",
    response={200: schemas.DossierProcessingStatus, 404: Message},
    url_name="file-status",
    exclude_none=True,
)
def get_file_status(request, external_dossier_id: str):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    return dossier_services_external.get_file_status(
        dossier=dossier,
        file_status_serialiser=schemas.FileStatus,
        extracted_file_serialiser=schemas.ExtractedFile,
        dossier_processing_status_serialiser=schemas.DossierProcessingStatus,
        original_file_serializer=schemas.OriginalFile,
    )


@api.get(
    "/dossier/{external_dossier_id}/details",
    response={200: schemas.Dossier, 404: Message},
    url_name="dossier-details",
    exclude_none=True,
)
def get_dossier(request, external_dossier_id: str):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    if dossier.expiry_date is not None and dossier.expiry_date < timezone.now():
        return {
            404: f"Dossier {dossier.uuid} with external id {dossier.external_id} has been deleted"
        }

    return map_dossier(dossier)


@api.get(
    "/dossier/{external_dossier_id}/semantic-documents",
    response={200: List[schemas.SemanticDocument], 404: Message},
    url_name="semantic-documents",
    exclude_none=True,
)
def get_semantic_documents(request, external_dossier_id: str, show_pages: bool = False):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_documents = dossier_services_external.get_semantic_documents(
        dossier=dossier,
        semantic_document_serializer=schemas.SemanticDocument,
        page_serializer=schemas.SemanticPage,
        map_confidence=map_confidence,
        show_pages=show_pages,
    )

    return semantic_documents


@api.get(
    "/semantic-document/{semantic_document_uuid}/external_dossier_id",
    response={303: HttpUrl, 200: ExportDossierExport},
    url_name="get-external-dossier-id",
    exclude_none=True,
)
def get_external_dossier_id_from_semantic_document(
    request, semantic_document_uuid: str
):
    """Get a dossier external id from a semantic document uuid"""
    semantic_document = get_object_or_404(
        SemanticDocument,
        uuid=semantic_document_uuid,
        dossier__account__key=request.auth.account_key,
    )

    return ExportDossierExport(
        external_dossier_id=semantic_document.dossier.external_id
    )


@api.patch(
    "/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}",
    response={200: schemas.SemanticDocument, 404: Message},
    url_name="update-semantic-document",
    exclude_none=True,
)
def update_semantic_document(
    request,
    external_dossier_id: str,
    semantic_document_uuid: UUID,
    update: schemas.SemanticDocumentUpdate,
):
    """Update the external_semantic_document_id and/or
    access_mode for a semantic document using a semantic_document_uuid"""
    dossier_user = request.auth.get_user_or_create()
    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document = get_object_or_404(
        SemanticDocument, uuid=semantic_document_uuid, dossier=dossier
    )

    for attr, value in update.model_dump().items():
        if value:
            setattr(semantic_document, attr, value)

    semantic_document.save()

    return serialize_semantic_document(
        semantic_document=semantic_document,
        semantic_document_serializer=schemas.SemanticDocument,
        page_serializer=schemas.SemanticPage,
        map_confidence=map_confidence,
    )


@api.get(
    "/document-categories",
    response={200: schemas.DocumentCategories},
    url_name="document-categories",
    exclude_none=True,
)
def get_document_categories(request):
    dossier_user = request.auth.get_user_or_create()
    account = dossier_user.account
    return schemas.DocumentCategories(
        root={
            category.name: schemas.DocumentCategory(
                key=category.name,
                id=category.id,
                title_de=category.de,
                title_en=category.en or "",
                title_fr=category.fr or "",
                title_it=category.it or "",
                description_de=category.description_de,
                description_en=category.description_en,
                description_fr=category.description_fr,
                description_it=category.description_it,
            )
            for category in dossier_models.DocumentCategory.objects.filter(
                account__key=account.key
            ).all()
        }
    )


@api.post(
    "/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}",
    response={200: schemas.SemanticDocumentPDFExportRequest, 404: Message},
    url_name="export-dossier-semantic-document-pdf",
)
def export_semantic_document_pdf(
    request,
    external_dossier_id,
    semantic_document_uuid,
):
    request = export_semantic_document_wrapper_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        external_dossier_id=external_dossier_id,
        semantic_document_uuid=semantic_document_uuid,
    )

    publish(
        message=request.model_dump_json().encode(),
        routing_key=ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
    )

    return SemanticDocumentPDFExportRequest(
        uuid=request.semantic_document_pdf_request_uuid,
    )


@api.get(
    "/export/{semantic_document_export_request_uuid}/status",
    response=schemas.ExportStatus,
    url_name="dossier-semantic-document-export-status",
    exclude_none=True,
)
def get_available_export(request, semantic_document_export_request_uuid: str):
    # Check the status of an individual document

    dossier_user = request.auth.get_user_or_create()
    account = dossier_user.account

    export_semantic_document: SemanticDocumentExport = get_object_or_404(
        SemanticDocumentExport, uuid=semantic_document_export_request_uuid
    )

    # Check that user has access to an associated account
    if export_semantic_document.semantic_document.dossier.account != account:
        raise Http404("No permission to access this dossier")

    status = schemas.ExportProcessingStatus.PROCESSING
    dossier_url = None
    dossier_file_uuid = None
    if export_semantic_document.done:
        status = schemas.ExportProcessingStatus.PROCESSED
        dossier_url = export_semantic_document.file.get_fast_url()
        dossier_file_uuid = export_semantic_document.file.uuid

    return schemas.ExportStatus(
        semantic_document_export_request_uuid=semantic_document_export_request_uuid,
        status=status,
        dossier_url=dossier_url,
        dossier_file_uuid=dossier_file_uuid,
        updated_at=export_semantic_document.done,
    )


@api.delete(
    "/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}",
    response={200: semantic_document_schemas.SavingResultWithMessage},
    url_name="semantic-document-soft-delete",
)
def soft_delete_semantic_document(
    request, external_dossier_id: str, semantic_document_uuid: UUID
):
    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document_helpers.semantic_restore_or_delete(
        dossier,
        semantic_document_models.SemanticDocument.all_objects,
        semantic_document_uuid,
        True,
    )

    return semantic_document_schemas.SavingResultWithMessage(message="deleted")


@api.put(
    "/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}/restore",
    response={200: semantic_document_schemas.SavingResultWithMessage},
    url_name="semantic-document-restore",
)
def undelete_semantic_document(
    request, external_dossier_id: str, semantic_document_uuid: UUID
):
    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document_helpers.semantic_restore_or_delete(
        dossier,
        semantic_document_models.SemanticDocument.all_objects,
        semantic_document_uuid,
        False,
    )

    return semantic_document_schemas.SavingResultWithMessage(message="restored")


@api.delete(
    "/dossier/{external_dossier_id}",
    response={202: Message, 404: Message},
    url_name="dossier-delete",
    exclude_none=True,
)
def delete_dossier(request, external_dossier_id):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    dossier.expiry_date = timezone.now() - timedelta(days=1)
    dossier.save()

    return 202, {"detail": "Dossier Scheduled for deletion"}


@api.get(
    "/dossier/{external_dossier_id}/show-dossier",
    response={303: HttpUrl},
    auth=None,
    url_name="show-dossier",
    exclude_none=True,
)
def show_dossier(request, external_dossier_id: str):
    account = get_object_or_404(Account, key=settings.API_ACCOUNT_KEY_ZKB)

    dossier = dossier_models.Dossier.objects.filter(
        external_id=external_dossier_id, account=account
    ).first()
    if dossier is None:
        response_url = f"{account.dmf_endpoint}/dossier/not-found?external-dossier-id={external_dossier_id}"
        return HttpResponseRedirect(redirect_to=response_url)
    else:
        response_url = (
            f"{account.dmf_endpoint}/dossier/{dossier.uuid}/view/page?lang=de"
        )
        return HttpResponseRedirect(redirect_to=response_url)


client = httpx.AsyncClient()


@api.get(
    "/dossier-file/{dossier_file_uuid}",
    auth=async_auth,
    url_name="get-dossier-file",
    openapi_extra={
        "responses": {
            200: {
                "content": {
                    "application/octet-stream": {
                        "schema": {"type": "string", "format": "binary"}
                    }
                },
                "description": "File",
            }
        }
    },
)
async def get_dossier_file(request, dossier_file_uuid: UUID):
    def get_url():
        # limit access to the dossier file on the same account (should only be used this way for the zkb api call
        dossier_file = get_object_or_404(
            dossier_models.DossierFile,
            uuid=dossier_file_uuid,
            dossier__account__key=jwt_extract.get_account_key(request.jwt),
        )
        return dossier_file.fast_url, dossier_file.name

    url, name = await sync_to_async(get_url)()

    async def download(url: URLTypes):
        async with client.stream("GET", url) as res:
            res.raise_for_status()
            async for chunk in res.aiter_raw():
                yield chunk

    return FileResponse(
        download(url),
        as_attachment=True,
        filename=name,
        content_type="application/octet-stream",
        headers={"Content-Disposition": f"attachment; filename={name}"},
    )


@api.get(
    "/dossiers-unchanged/{num_days_documents_unchanged}",
    response={200: List[schemas.Dossier], 404: Message},
    url_name="dossiers-unchanged",
    exclude_none=True,
    description="Show dossiers which have remained unchanged for >= num_days_documents_unchanged."
    "Logic has been changed in October 2024 so 'unchanged' now means 'no new original files have been uploaded'.",
)
def get_unchanged_dossiers(request, num_days_documents_unchanged: float):
    dossier_user = request.auth.get_user_or_create()

    date_threshold = timezone.now() - timedelta(days=num_days_documents_unchanged)

    # This is the updated logic as of October 2024. The dossier could be changed (e.g. pages moved in semantic documents
    # but this will not be reflected here.
    # Only if a new original file is uploaded, the dossier is considered "changed".
    unchanged_dossiers = (
        dossier_models.Dossier.objects.annotate(
            newest_originalfile_created_at=Max("original_files__created_at")
        ).filter(
            account=dossier_user.account,
            newest_originalfile_created_at__lte=date_threshold,
            expiry_date__gt=timezone.now(),
        )
        # Sort by youngest original file ascending so the dossier where the youngest file is "very old" goes first in the
        # list (approximately "old dossiers are listed first").
        .order_by("newest_originalfile_created_at")  # Ensures uniqueness
    )

    # Make sure that unique dossiers are returned. Should be based on above query but let's verify that.
    dossier_uuids = [str(dossier.uuid) for dossier in unchanged_dossiers]
    # Check for duplicates using Counter
    duplicate_uuids = [
        uuid for uuid, count in Counter(dossier_uuids).items() if count > 1
    ]
    if duplicate_uuids:
        raise ValueError(f"Duplicate dossiers found: {duplicate_uuids}")

    ret = [map_dossier(dossier) for dossier in unchanged_dossiers]
    return ret


@api.get(
    "/semanticdocument/{semantic_document_uuid}/unique-page-objects",
    response={200: List[PageObjectSchema], 404: Message},
    url_name="semantic-document-unique-page-objects",
    exclude_none=True,
    description="Return page objects for a specific semantic document - for a semantic page, "
    "if multiple page objects of the same type exist (same category key), only return the first one. "
    "This way the page objects are unique per semantic page",
)
def get_semantic_document_unique_page_objects(request, semantic_document_uuid):
    dossier_user = request.auth.get_user_or_create()

    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_document_uuid)

    # Perform access check
    get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=semantic_document.dossier.external_id,
    )

    return export_aggregate_page_objects_from_semantic_document_with_title(
        semantic_document
    )


@api.get(
    "/dossier/{external_dossier_id}/unique-page-objects",
    response={200: List[PageObjectSchema], 404: Message},
    url_name="dossier-unique-page-objects",
    exclude_none=True,
    description="Return page objects for a dossier - for a semantic page, "
    "if multiple page objects of the same type exist (same category key), only return the first one. "
    "This way the page objects are unique per semantic page",
)
def dossier_page_objects(request, external_dossier_id):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_documents = dossier.semantic_documents.all().order_by(
        "document_category__id", "title_suffix", "uuid"
    )

    page_objects = []

    # The reason we loop over semantic documents instead of passing in a query set with all docs
    # (more efficient), is that for aggregate page objects page_object.key is unique per semantic document
    # and not across dossier
    for semantic_document in semantic_documents:
        page_objects = (
            page_objects
            + export_aggregate_page_objects_from_semantic_document_with_title(
                semantic_document=semantic_document,
            )
        )

    return page_objects
