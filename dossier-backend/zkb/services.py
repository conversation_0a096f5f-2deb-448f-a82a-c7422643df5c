import datetime
from typing import Callable, List, Union, Optional

import httpx
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction

import structlog
from pydantic import BaseModel

from uuid import uuid4

from django.utils import timezone


import dossier.models as dossier_models
from workers.models import SemanticDocumentExport

User = get_user_model()

logger = structlog.get_logger(__name__)


class ContractException(BaseModel):
    _class: str


class Msg(BaseModel):
    key: str
    message: str
    args: List[str] = []


class ServiceException(ContractException):
    msg: Msg


class PropertyFailure(BaseModel):
    path: str
    value: str
    proposal: Optional[str] = None


class ValidationFailure(BaseModel):
    msg: Msg
    properties: List[PropertyFailure]


class ValidationException(ServiceException):
    failures: List[ValidationFailure]


async def external_service_check(
    partner_uuid: str, logon_id: str
) -> Union[bool, ServiceException, ValidationException]:
    # We call this service to check if a user is allowed access to a dossier

    # partner_uuid is external_dossier_id
    # logon_id a user_id
    # base_url as an env setting

    base_url = settings.USER_AUTHORIZATION_ENDPOINT_ZKB

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/partnerExtern/hasDatenraumrecht",
                params={
                    "partnerUuid": partner_uuid,
                    "logonId": logon_id,
                },
            )
            response.raise_for_status()
            if response.headers["content-type"] == "application/json":
                json_data = response.json()
                if isinstance(json_data, bool):
                    return json_data
                elif "_class" in json_data:
                    if json_data["_class"] == "slx.global.ServiceException":
                        logger.error(
                            "ServiceException encountered",
                            details=ServiceException(**json_data).model_dump(),
                        )
                    elif json_data["_class"] == "slx.global.ValidationException":
                        logger.error(
                            "ValidationException encountered",
                            details=ValidationException(**json_data).model_dump(),
                        )
                    return False
            logger.error("Unknown response format")
            return False

    except httpx.HTTPError as exc:
        logger.error("HTTP error encountered", error=str(exc))
        return False
    except Exception as e:
        logger.error("Unexpected error", error=str(e))
        return False


def check_dossier_access_external_service(
    user: User,
    user_id: str,
    dossier: dossier_models.Dossier,
    external_service_check_callable: Callable[[str, str, str], bool],
    url: str,
    approved_ttl_minutes: int = settings.ZKB_AUTHORIZATION_ENDPOINT_APPROVED_TTL_MINUTES,
    rejected_ttl_minutes: int = settings.ZKB_AUTHORIZATION_ENDPOINT_DENIED_TTL_MINUTES,
):
    """Check against an external service if a user has access to a dossier and cache the result.
    We have two auth times, one for approved and one for rejected. This is as we also want to cache
    rejected access requests, but not for as long as approved ones.

    If we use this for more than ZKB, we should move this to a shared service.
    """
    now = timezone.now()

    access_grant = dossier_models.DossierAccessGrant.objects.filter(
        dossier=dossier, user=user, expires_at__gt=now
    ).first()

    if access_grant is not None:
        return access_grant.has_access

    try:
        # user_id should be passed to external auth
        has_access = external_service_check_callable(user_id, dossier.external_id, url)
    except Exception as e:
        logger.error("Error while checking access", error=str(e))
        # Don't cache result if error
        return False

    # Cache result if access granted
    with transaction.atomic():
        if has_access:
            expiration = now + datetime.timedelta(minutes=approved_ttl_minutes)

            dossier_access_grant, _ = (
                dossier_models.DossierAccessGrant.objects.update_or_create(
                    user=user,
                    dossier=dossier,
                    defaults={
                        "expires_at": expiration,
                        "has_access": has_access,
                    },
                )
            )
        else:
            expiration = now + datetime.timedelta(minutes=rejected_ttl_minutes)

            access_grant = dossier_models.DossierAccessGrant.objects.filter(
                dossier=dossier, user=user
            ).first()

            if access_grant:
                access_grant.has_access = False
                access_grant.expires_at = expiration
                access_grant.save()

    return has_access


def set_semantic_document_export_done(export_request_uuid: uuid4):
    """Set the done field of the SemanticDocumentExport model to the current time"""
    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=export_request_uuid
    )
    semantic_document_export.done = timezone.now()
    semantic_document_export.save()
