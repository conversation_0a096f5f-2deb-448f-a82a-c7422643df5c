import random
import uuid
from typing import Optional

from faker import Faker
from faker.providers import address, date_time, internet, lorem, person

import dossier.schemas as dossier_schemas
from assets import ASSETS_PATH
from dossier.fakes import (
    add_some_fake_semantic_documents,
    load_initial_document_categories,
)
from dossier.helpers import is_document_topic_property
from dossier.helpers_timezone import create_faker_past_datetime_with_timezone
from dossier.models import Account, Dossier<PERSON>ser, Dossier, RealestateProperty
from dossier.services_external import create_or_update_dossier_from_external_id
from dossier.services import create_expiration_date
from projectconfig.authentication import get_user_or_create
from semantic_document.models import AssignedRealEstatePropertySemanticDocument
from zkb.schemas import schemas


class ZKBAccountFactoryFaker:
    def __init__(
        self,
        account: Optional[Account] = None,
        account_key: Optional[str] = None,
        default_bucket_name: str = None,
    ):
        self.document_categories = None

        if account is None:

            if default_bucket_name is None:
                default_bucket_name = "dms-default-bucket"
            if account_key is None:
                account_key = "zkbd"
            account, _ = Account.objects.update_or_create(
                defaults=dict(
                    name="zkb dev",
                    default_bucket_name=default_bucket_name,
                ),
                dmf_endpoint="https://www.localhost",
                key=account_key,
            )

        self.account = account

        faker = Faker(locale="de_CH")
        faker.add_provider(person)
        faker.add_provider(address)
        faker.add_provider(internet)
        faker.add_provider(date_time)
        faker.add_provider(lorem)
        self.faker = faker

        # This user is also setup in keycloak
        user = get_user_or_create(
            account=self.account,
            username="<EMAIL>",
            email="<EMAIL>",
            fname="JC",
            lname="Denton",
        )

        user.is_staff = True
        user.save()

    def load_initial_document_categories(self):
        document_categories, _, _, _ = load_initial_document_categories(self.account)

        document_categories_json_path = (
            ASSETS_PATH / "document_category/zkb/DocumentCategory-2024-05-22zkb.json"
        )
        document_categories_custom, _, _, _ = load_initial_document_categories(
            self.account, document_categories_json_path
        )
        document_categories += document_categories_custom
        document_categories.sort(key=lambda x: x.id)
        self.document_categories = document_categories
        return document_categories

    def create_dossier(
        self,
        current_user: DossierUser = None,
    ) -> Dossier:
        if current_user is None:
            # current_user = get_user_or_create(
            #     account=self.account,
            #     username="<EMAIL>",
            #     email="<EMAIL>",
            #     fname="service-zkb-first",
            #     lname="service-zkb-last",
            # )

            current_user = get_user_or_create(
                account=self.account,
                username="<EMAIL>",
                email="<EMAIL>",
                fname="JC",
                lname="Denton",
            )

        dossier = create_or_update_dossier_from_external_id(
            self.account,
            external_dossier_id="test",
            dossier_name=schemas.DossierName(self.faker.sentence()),
            user=current_user,
            language=random.choice(list(dossier_schemas.Language)),
        )

        dossier.created_at = create_faker_past_datetime_with_timezone(self.faker)
        dossier.save()

        return dossier

    # I don't think we need this
    def create_token_data(self, minutes=10080):
        return {
            "aud": "account",
            "exp": create_expiration_date(minutes=10080),
            "name": "service-zkb-first service-zkb-last",
            "given_name": "service-zkb-first",
            "family_name": "service-zkb-last",
            "preferred_username": "<EMAIL>",
            "email": "<EMAIL>",
            "external_dossier_id": schemas.DossierName(
                self.faker.sentence()
            ),  # Not currently used, as we directly provide this
            # during dossier creation via API parameter
            "user_roles": ["api_role"],
            "account_key": "zkbd",
            "account_name": "zkb development account",
            "jti": str(uuid.uuid4()),  # unique identifier for the token
        }

    def create_ready_for_export_dossier(
        self, min_num_documents: int = 0, max_num_documents: int = 10
    ):
        zkb_dossier = self.create_dossier()

        num_docs = random.randint(min_num_documents, max_num_documents)

        semantic_documents = add_some_fake_semantic_documents(
            zkb_dossier, num_docs=num_docs
        )

        for semantic_document in semantic_documents:
            # If a document category is a property, then create a property assignment
            if is_document_topic_property(semantic_document.document_category):
                realestate_property, _ = RealestateProperty.objects.update_or_create(
                    defaults=dict(
                        title=self.faker.sentence(nb_words=3),
                        floor=(
                            random.randint(1, 20) if random.random() > 0.8 else None
                        ),  # Generating random floor number
                        street=self.faker.street_name(),
                        street_nr=(
                            self.faker.building_number()
                            if random.random() > 0.8
                            else None
                        ),
                        zipcode=self.faker.postcode(),
                        city=self.faker.city(),
                    ),
                    dossier=zkb_dossier,
                    key=self.faker.bothify("PrN######"),
                )

                AssignedRealEstatePropertySemanticDocument.objects.create(
                    semantic_document=semantic_document,
                    realestate_property=realestate_property,
                )

        zkb_dossier.save()

        return zkb_dossier
