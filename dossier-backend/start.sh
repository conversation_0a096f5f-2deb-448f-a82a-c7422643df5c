#!/bin/bash

# Collect static files
#echo "Collect static files"
python manage.py collectstatic --noinput

# Apply database migrations
echo "Apply database migrations"
python manage.py migrate
#python manage.py loaddata fixtures
#python manage.py load_s3_data

# Start server
echo "Starting server"

# No reload since 240618 because this could cause high cpu load
# python -m uvicorn --lifespan auto projectconfig.asgi:application --host 0.0.0.0 --reload
python -m uvicorn --lifespan auto projectconfig.asgi:application --host 0.0.0.0