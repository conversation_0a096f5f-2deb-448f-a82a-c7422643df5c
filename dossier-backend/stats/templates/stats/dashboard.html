{% extends 'stats/base.html' %}

{% block title %}Statistics Dashboard{% endblock %}

{% block content %}
<!-- Time Range Filter -->
{% include 'stats/time_filter.html' %}

<!-- Date Range Info -->
{% if start_date and end_date %}
<div class="bg-white shadow rounded-lg mb-6 p-4">
    <h3 class="text-lg font-medium text-gray-900">
        {% if start_date == end_date %}
            Statistics for {{ start_date|date:"F j, Y" }}
        {% else %}
            Statistics from {{ start_date|date:"F j, Y" }} to {{ end_date|date:"F j, Y" }}
        {% endif %}

        {% if compare and prev_start_date and prev_end_date %}
            <span class="text-sm text-gray-500 ml-2">
                (compared to {{ prev_start_date|date:"F j, Y" }} - {{ prev_end_date|date:"F j, Y" }})
            </span>
        {% endif %}
    </h3>
</div>
{% endif %}

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h2 class="text-lg leading-6 font-medium text-gray-900">
            System Overview
        </h2>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Key statistics about the Dossier system.
        </p>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-4 p-4">
        <!-- Dossier Count -->
        <div class="bg-indigo-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Dossiers
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-indigo-600">
                    {{ dossier_count }}
                </dd>
                {% if dossier_count_change is not None and compare %}
                <dd class="mt-2 text-sm {% if dossier_count_change > 0 %}text-green-600{% elif dossier_count_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if dossier_count_change > 0 %}+{% endif %}{{ dossier_count_change }}% from previous period ({{ dossier_count_previous }})
                </dd>
                {% else %}
                <dd class="mt-2 text-sm text-gray-500">
                    {{ recent_dossiers }} new in last 30 days
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- Account Count -->
        <div class="bg-green-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Accounts
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-green-600">
                    {{ account_count }}
                </dd>
            </div>
        </div>

        <!-- Document Count -->
        <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Documents
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-blue-600">
                    {{ document_count }}
                </dd>
                {% if document_count_change is not None and compare %}
                <dd class="mt-2 text-sm {% if document_count_change > 0 %}text-green-600{% elif document_count_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if document_count_change > 0 %}+{% endif %}{{ document_count_change }}% from previous period ({{ document_count_previous }})
                </dd>
                {% else %}
                <dd class="mt-2 text-sm text-gray-500">
                    {{ recent_documents }} new in last 30 days
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- File Count -->
        <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Files
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-purple-600">
                    {{ file_count }}
                </dd>
                {% if processed_file_count_change is not None and compare %}
                <dd class="mt-2 text-sm {% if processed_file_count_change > 0 %}text-green-600{% elif processed_file_count_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if processed_file_count_change > 0 %}+{% endif %}{{ processed_file_count_change }}% from previous period ({{ processed_file_count_previous }})
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- User Count -->
        <div class="bg-yellow-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Users
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-yellow-600">
                    {{ user_count }}
                </dd>
            </div>
        </div>

        <!-- Original File Count -->
        <div class="bg-orange-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Original Files
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-orange-600">
                    {{ original_file_count }}
                </dd>
                {% if original_file_count_change is not None and compare %}
                <dd class="mt-2 text-sm {% if original_file_count_change > 0 %}text-green-600{% elif original_file_count_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if original_file_count_change > 0 %}+{% endif %}{{ original_file_count_change }}% from previous period ({{ original_file_count_previous }})
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- Exception Count -->
        <div class="bg-red-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    File Exceptions
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-red-600">
                    {{ total_exception_count }}
                </dd>
                {% if total_exception_count_change is not None and compare %}
                <dd class="mt-2 text-sm {% if total_exception_count_change > 0 %}text-red-600{% elif total_exception_count_change < 0 %}text-green-600{% else %}text-gray-500{% endif %}">
                    {% if total_exception_count_change > 0 %}+{% endif %}{{ total_exception_count_change }}% from previous period ({{ total_exception_count_previous }})
                </dd>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Top Accounts -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Top Accounts by Dossier Count
        </h3>
    </div>
    <div class="px-4 py-3">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for account in top_accounts %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-indigo-600 truncate">
                                {{ account.name }}
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ account.dossier_count }} dossiers
                                </p>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No accounts found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Document Categories -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <div class="flex justify-between items-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                Top Document Categories
            </h3>
            <div class="flex items-center text-xs">
                <div class="flex items-center mr-4">
                    <div class="w-3 h-3 bg-indigo-600 rounded-full mr-1"></div>
                    <span>% of all documents</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-600 rounded-full mr-1"></div>
                    <span>% of dossiers with this category</span>
                </div>
            </div>
        </div>
    </div>
    <div class="px-4 py-3 pb-6">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for category in document_categories %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex flex-col w-full">
                                <a href="{% url 'stats:category_documents' category.document_category__id %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="text-sm font-medium text-indigo-600 truncate hover:underline">
                                    {{ category.document_category__name|default:"No Category" }}
                                </a>
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>Documents: {{ category.percentage }}%</span>
                                    <span>Dossiers: {{ category.dossier_percentage }}%</span>
                                </div>
                                <div class="w-full flex mt-1">
                                    <div class="w-1/2 pr-1">
                                        <div class="bg-gray-200 rounded-full h-2.5">
                                            <div class="bg-indigo-600 h-2.5 rounded-full" style="width: {{ category.percentage }}%"></div>
                                        </div>
                                    </div>
                                    <div class="w-1/2 pl-1">
                                        <div class="bg-gray-200 rounded-full h-2.5">
                                            <div class="bg-green-600 h-2.5 rounded-full" style="width: {{ category.dossier_percentage }}%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ml-2 flex-shrink-0 flex items-center">
                                <div class="flex flex-col items-end">
                                    <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800">
                                        {{ category.count }} documents
                                    </p>
                                    <p class="px-2 mt-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        {{ category.dossier_count }} dossiers
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No document categories found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endblock %}
