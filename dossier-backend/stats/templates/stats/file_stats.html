{% extends 'stats/base.html' %}

{% block title %}File Statistics{% endblock %}

{% block content %}
<!-- Time Range Filter -->
{% include 'stats/time_filter.html' %}

<!-- Date Range Info -->
{% if start_date and end_date %}
<div class="bg-white shadow rounded-lg mb-6 p-4">
    <h3 class="text-lg font-medium text-gray-900">
        {% if start_date == end_date %}
            Statistics for {{ start_date|date:"F j, Y" }}
        {% else %}
            Statistics from {{ start_date|date:"F j, Y" }} to {{ end_date|date:"F j, Y" }}
        {% endif %}

        {% if compare and prev_start_date and prev_end_date %}
            <span class="text-sm text-gray-500 ml-2">
                (compared to {{ prev_start_date|date:"F j, Y" }} - {{ prev_end_date|date:"F j, Y" }})
            </span>
        {% endif %}
    </h3>
</div>
{% endif %}

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h2 class="text-lg leading-6 font-medium text-gray-900">
            File Statistics
        </h2>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Detailed statistics about original files and extracted files in the system.
        </p>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4">
        <!-- Original Files Count -->
        <div class="bg-indigo-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Original Files
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-indigo-600">
                    {{ original_file_count }}
                </dd>
                {% if original_file_count_change is not None and compare %}
                <dd class="mt-2 text-sm {% if original_file_count_change > 0 %}text-green-600{% elif original_file_count_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if original_file_count_change > 0 %}+{% endif %}{{ original_file_count_change }}% from previous period ({{ original_file_count_previous }})
                </dd>
                {% else %}
                <dd class="mt-2 text-sm text-gray-500">
                    {{ recent_original_files }} new in last 30 days
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- Extracted Files Count -->
        <div class="bg-green-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Extracted Files
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-green-600">
                    {{ extracted_file_count }}
                </dd>
                {% if extracted_file_count_change is not None and compare %}
                <dd class="mt-2 text-sm {% if extracted_file_count_change > 0 %}text-green-600{% elif extracted_file_count_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if extracted_file_count_change > 0 %}+{% endif %}{{ extracted_file_count_change }}% from previous period ({{ extracted_file_count_previous }})
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- Processing Status -->
        <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Processing Success Rate
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-blue-600">
                    {{ processing_success_rate }}%
                </dd>
                {% if processing_success_rate_change is not None and compare %}
                <dd class="mt-2 text-sm {% if processing_success_rate_change > 0 %}text-green-600{% elif processing_success_rate_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if processing_success_rate_change > 0 %}+{% endif %}{{ processing_success_rate_change }}% from previous period ({{ processing_success_rate_previous }}%)
                </dd>
                {% endif %}
                <dd class="mt-2 text-sm text-gray-500">
                    {{ error_count }} files with errors
                    {% if error_count_change is not None and compare %}
                    <span class="{% if error_count_change < 0 %}text-green-600{% elif error_count_change > 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                        ({% if error_count_change > 0 %}+{% endif %}{{ error_count_change }}%)
                    </span>
                    {% endif %}
                </dd>
            </div>
        </div>

        <!-- Average Extracted Files -->
        <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Avg. Extracted Files per Original
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-purple-600">
                    {{ avg_extracted_files|floatformat:1 }}
                </dd>
                {% if avg_extracted_files_change is not None and compare %}
                <dd class="mt-2 text-sm {% if avg_extracted_files_change > 0 %}text-green-600{% elif avg_extracted_files_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if avg_extracted_files_change > 0 %}+{% endif %}{{ avg_extracted_files_change }}% from previous period ({{ avg_extracted_files_previous|floatformat:1 }})
                </dd>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Files by Status -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Original Files by Status
        </h3>
    </div>
    <div class="px-4 py-3">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for status in files_by_status %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-indigo-600 truncate">
                                {{ status.status }}
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if status.status == 'Error' %}bg-red-100 text-red-800{% elif status.status == 'Processing' %}bg-yellow-100 text-yellow-800{% else %}bg-green-100 text-green-800{% endif %}">
                                    {{ status.count }} files
                                </p>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No file status data found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Files by Source -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Original Files by Source
        </h3>
    </div>
    <div class="px-4 py-3">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for source in files_by_source %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-indigo-600 truncate">
                                {{ source.source|default:"Unknown" }}
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ source.count }} files
                                </p>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No file source data found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Top File Uploaders -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Top File Uploaders
        </h3>
    </div>
    <div class="px-4 py-3 pb-6">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for user in top_uploaders %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-indigo-600 truncate">
                                {{ user.create_user__username|default:"System" }}
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ user.count }} files
                                </p>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No uploader data found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endblock %}
