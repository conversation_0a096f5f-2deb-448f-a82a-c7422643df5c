from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import render
from django.db.models import Count, Avg, <PERSON>, Min, Q
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from dossier.models import (
    Dossier,
    Account,
    OriginalFile,
    ExtractedFile,
    FileStatus,
    FileException,
    ExceptionDetails,
)
from processed_file.models import ProcessedFile
from semantic_document.models import SemanticDocument
from events.models import Event

from .forms import TimeRangeForm
from .utils import filter_queryset_by_date, get_comparison_data


@staff_member_required
def dashboard(request):
    """
    Main dashboard view showing key statistics
    """
    # Process the time range form
    form = TimeRangeForm(request.GET or None)
    if form.is_valid():
        start_date, end_date = form.get_date_range()
        compare = form.cleaned_data.get("compare", False)
    else:
        # Default to last 30 days if form is not valid
        today = timezone.now().date()
        start_date = today - timedelta(days=30)
        end_date = today
        compare = False

    # Get previous period date range if comparison is enabled
    prev_start_date, prev_end_date = (
        form.get_previous_date_range() if compare else (None, None)
    )

    # Filter querysets by date range
    dossier_qs = Dossier.objects.all()
    document_qs = SemanticDocument.objects.all()
    original_file_qs = OriginalFile.objects.all()
    processed_file_qs = ProcessedFile.objects.all()

    # Current period data
    current_dossier_qs = filter_queryset_by_date(dossier_qs, start_date, end_date)
    current_document_qs = filter_queryset_by_date(document_qs, start_date, end_date)
    current_original_file_qs = filter_queryset_by_date(
        original_file_qs, start_date, end_date
    )
    current_processed_file_qs = filter_queryset_by_date(
        processed_file_qs, start_date, end_date
    )

    # Get counts for current period
    dossier_count = current_dossier_qs.count()
    account_count = Account.objects.count()  # Accounts don't have a created_at field
    document_count = current_document_qs.count()
    processed_file_count = current_processed_file_qs.count()
    original_file_count = current_original_file_qs.count()
    user_count = (
        User.objects.count()
    )  # Users don't have a created_at field we can access

    # Get exception counts
    file_exception_qs = FileException.objects.all()
    original_file_error_qs = OriginalFile.objects.filter(status=FileStatus.ERROR)
    current_file_exception_qs = filter_queryset_by_date(
        file_exception_qs, start_date, end_date
    )
    current_original_file_error_qs = filter_queryset_by_date(
        original_file_error_qs, start_date, end_date
    )
    file_exception_count = current_file_exception_qs.count()
    original_file_error_count = current_original_file_error_qs.count()
    total_exception_count = file_exception_count + original_file_error_count

    # Get recent activity within the selected period
    if start_date and end_date:
        recent_period_start = max(end_date - timedelta(days=30), start_date)
        recent_dossiers = filter_queryset_by_date(
            dossier_qs, recent_period_start, end_date
        ).count()
        recent_documents = filter_queryset_by_date(
            document_qs, recent_period_start, end_date
        ).count()
    else:
        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        recent_dossiers = filter_queryset_by_date(
            dossier_qs, thirty_days_ago, timezone.now().date()
        ).count()
        recent_documents = filter_queryset_by_date(
            document_qs, thirty_days_ago, timezone.now().date()
        ).count()

    # Get top accounts by dossier count for the current period
    top_accounts = Account.objects.annotate(
        dossier_count=Count(
            "dossier",
            filter=(
                Q(
                    dossier__created_at__date__gte=start_date,
                    dossier__created_at__date__lte=end_date,
                )
                if start_date and end_date
                else None
            ),
        )
    ).order_by("-dossier_count")[:5]

    # Get document statistics by category for the current period
    total_docs_dashboard = current_document_qs.count()

    # Get total dossiers count for the current period
    total_dossiers_dashboard = (
        Dossier.objects.filter(semantic_documents__in=current_document_qs)
        .distinct()
        .count()
    )

    document_categories = (
        current_document_qs.values("document_category__name", "document_category__id")
        .annotate(count=Count("uuid"))
        .order_by("-count")[:10]
    )

    # Calculate percentage for each category
    if total_docs_dashboard > 0:
        for category in document_categories:
            category["percentage"] = round(
                (category["count"] / total_docs_dashboard) * 100, 1
            )

            # Calculate percentage of dossiers that have at least one document of this category
            if category["document_category__id"]:
                dossiers_with_category = (
                    Dossier.objects.filter(
                        semantic_documents__document_category__id=category[
                            "document_category__id"
                        ],
                        semantic_documents__created_at__date__gte=(
                            start_date
                            if start_date
                            else timezone.now().date() - timedelta(days=3650)
                        ),
                        semantic_documents__created_at__date__lte=(
                            end_date if end_date else timezone.now().date()
                        ),
                    )
                    .distinct()
                    .count()
                )

                category["dossier_count"] = dossiers_with_category
                category["dossier_percentage"] = (
                    round((dossiers_with_category / total_dossiers_dashboard * 100), 1)
                    if total_dossiers_dashboard > 0
                    else 0
                )
            else:
                category["dossier_count"] = 0
                category["dossier_percentage"] = 0

    # Prepare current period data
    current_data = {
        "dossier_count": dossier_count,
        "document_count": document_count,
        "processed_file_count": processed_file_count,
        "original_file_count": original_file_count,
        "total_exception_count": total_exception_count,
    }

    # Get previous period data if comparison is enabled
    comparison_data = {}
    if compare and prev_start_date and prev_end_date:
        # Previous period data
        prev_dossier_qs = filter_queryset_by_date(
            dossier_qs, prev_start_date, prev_end_date
        )
        prev_document_qs = filter_queryset_by_date(
            document_qs, prev_start_date, prev_end_date
        )
        prev_original_file_qs = filter_queryset_by_date(
            original_file_qs, prev_start_date, prev_end_date
        )
        prev_processed_file_qs = filter_queryset_by_date(
            processed_file_qs, prev_start_date, prev_end_date
        )

        # Get counts for previous period
        prev_dossier_count = prev_dossier_qs.count()
        prev_document_count = prev_document_qs.count()
        prev_processed_file_count = prev_processed_file_qs.count()
        prev_original_file_count = prev_original_file_qs.count()

        # Get exception counts for previous period
        prev_file_exception_qs = filter_queryset_by_date(
            file_exception_qs, prev_start_date, prev_end_date
        )
        prev_original_file_error_qs = filter_queryset_by_date(
            original_file_error_qs, prev_start_date, prev_end_date
        )
        prev_file_exception_count = prev_file_exception_qs.count()
        prev_original_file_error_count = prev_original_file_error_qs.count()
        prev_total_exception_count = (
            prev_file_exception_count + prev_original_file_error_count
        )

        # Prepare previous period data
        previous_data = {
            "dossier_count": prev_dossier_count,
            "document_count": prev_document_count,
            "processed_file_count": prev_processed_file_count,
            "original_file_count": prev_original_file_count,
            "total_exception_count": prev_total_exception_count,
        }

        # Calculate comparison data
        comparison_data = get_comparison_data(current_data, previous_data)

    # Combine all data for the template
    context = {
        "form": form,
        "dossier_count": dossier_count,
        "account_count": account_count,
        "document_count": document_count,
        "file_count": processed_file_count,
        "original_file_count": original_file_count,
        "user_count": user_count,
        "total_exception_count": total_exception_count,
        "recent_dossiers": recent_dossiers,
        "recent_documents": recent_documents,
        "top_accounts": top_accounts,
        "document_categories": document_categories,
        "start_date": start_date,
        "end_date": end_date,
        "compare": compare,
        "prev_start_date": prev_start_date,
        "prev_end_date": prev_end_date,
    }

    # Add comparison data to context
    context.update(comparison_data)

    return render(request, "stats/dashboard.html", context)


@staff_member_required
def dossier_stats(request):
    """
    Detailed statistics about dossiers
    """
    # Process the time range form
    form = TimeRangeForm(request.GET or None)
    if form.is_valid():
        start_date, end_date = form.get_date_range()
        compare = form.cleaned_data.get("compare", False)
    else:
        # Default to last 30 days if form is not valid
        today = timezone.now().date()
        start_date = today - timedelta(days=30)
        end_date = today
        compare = False

    # Get previous period date range if comparison is enabled
    prev_start_date, prev_end_date = (
        form.get_previous_date_range() if compare else (None, None)
    )

    # Filter querysets by date range
    dossier_qs = Dossier.objects.all()

    # Current period data
    current_dossier_qs = filter_queryset_by_date(dossier_qs, start_date, end_date)

    # Get dossier counts by status for the current period
    total_dossiers = current_dossier_qs.count()
    dossier_by_status = (
        current_dossier_qs.values("work_status__name_en")
        .annotate(count=Count("uuid"))
        .order_by("-count")
    )

    # Calculate percentage for each status
    if total_dossiers > 0:
        for status in dossier_by_status:
            status["percentage"] = round((status["count"] / total_dossiers) * 100, 1)

    # Get dossier counts by account for the current period
    dossier_by_account = Account.objects.annotate(
        dossier_count=Count(
            "dossier",
            filter=(
                Q(
                    dossier__created_at__date__gte=start_date,
                    dossier__created_at__date__lte=end_date,
                )
                if start_date and end_date
                else None
            ),
        )
    ).order_by("-dossier_count")

    # Calculate percentage for each account
    if total_dossiers > 0:
        for account in dossier_by_account:
            account.percentage = (
                round((account.dossier_count / total_dossiers) * 100, 1)
                if account.dossier_count > 0
                else 0
            )

    # Get average documents per dossier for the current period
    avg_documents = current_dossier_qs.annotate(
        doc_count=Count("semantic_documents")
    ).aggregate(avg=Avg("doc_count"))

    # Prepare current period data
    current_data = {
        "avg_documents": avg_documents["avg"] if avg_documents["avg"] else 0,
    }

    # Get previous period data if comparison is enabled
    comparison_data = {}
    if compare and prev_start_date and prev_end_date:
        # Previous period data
        prev_dossier_qs = filter_queryset_by_date(
            dossier_qs, prev_start_date, prev_end_date
        )

        # Get average documents per dossier for the previous period
        prev_avg_documents = prev_dossier_qs.annotate(
            doc_count=Count("semantic_documents")
        ).aggregate(avg=Avg("doc_count"))

        # Prepare previous period data
        previous_data = {
            "avg_documents": (
                prev_avg_documents["avg"] if prev_avg_documents["avg"] else 0
            ),
        }

        # Calculate comparison data
        comparison_data = get_comparison_data(current_data, previous_data)

    # Combine all data for the template
    context = {
        "form": form,
        "dossier_by_status": dossier_by_status,
        "dossier_by_account": dossier_by_account,
        "avg_documents": avg_documents,
        "start_date": start_date,
        "end_date": end_date,
        "compare": compare,
        "prev_start_date": prev_start_date,
        "prev_end_date": prev_end_date,
    }

    # Add comparison data to context
    context.update(comparison_data)

    return render(request, "stats/dossier_stats.html", context)


@staff_member_required
def document_stats(request):
    """
    Detailed statistics about documents
    """
    # Process the time range form
    form = TimeRangeForm(request.GET or None)
    if form.is_valid():
        start_date, end_date = form.get_date_range()
        compare = form.cleaned_data.get("compare", False)
    else:
        # Default to last 30 days if form is not valid
        today = timezone.now().date()
        start_date = today - timedelta(days=30)
        end_date = today
        compare = False

    # Get previous period date range if comparison is enabled
    prev_start_date, prev_end_date = (
        form.get_previous_date_range() if compare else (None, None)
    )

    # Filter querysets by date range
    document_qs = SemanticDocument.objects.all()

    # Current period data
    current_document_qs = filter_queryset_by_date(document_qs, start_date, end_date)

    # Get document counts by category for the current period
    total_docs = current_document_qs.count()

    # Get total dossiers count for the current period
    total_dossiers_docs = (
        Dossier.objects.filter(semantic_documents__in=current_document_qs)
        .distinct()
        .count()
    )

    docs_by_category = (
        current_document_qs.values("document_category__name", "document_category__id")
        .annotate(count=Count("uuid"))
        .order_by("-count")
    )

    # Calculate percentage for each category
    if total_docs > 0:
        for category in docs_by_category:
            category["percentage"] = round((category["count"] / total_docs) * 100, 1)

            # Calculate percentage of dossiers that have at least one document of this category
            if category["document_category__id"]:
                dossiers_with_category = (
                    Dossier.objects.filter(
                        semantic_documents__document_category__id=category[
                            "document_category__id"
                        ],
                        semantic_documents__created_at__date__gte=(
                            start_date
                            if start_date
                            else timezone.now().date() - timedelta(days=3650)
                        ),
                        semantic_documents__created_at__date__lte=(
                            end_date if end_date else timezone.now().date()
                        ),
                    )
                    .distinct()
                    .count()
                )

                category["dossier_count"] = dossiers_with_category
                category["dossier_percentage"] = (
                    round((dossiers_with_category / total_dossiers_docs * 100), 1)
                    if total_dossiers_docs > 0
                    else 0
                )
            else:
                category["dossier_count"] = 0
                category["dossier_percentage"] = 0

    # Get document counts by status for the current period
    docs_by_status = (
        current_document_qs.values("work_status__name_en")
        .annotate(count=Count("uuid"))
        .order_by("-count")
    )

    # Calculate percentage for each status
    if total_docs > 0:
        for status in docs_by_status:
            status["percentage"] = round((status["count"] / total_docs) * 100, 1)

    # Get page count statistics for the current period
    documents_with_page_counts = list(
        current_document_qs.annotate(page_count=Count("semantic_pages")).values(
            "page_count"
        )
    )

    # Calculate average pages per document
    avg_pages = {"avg": 0}
    if documents_with_page_counts:
        page_counts = [doc["page_count"] for doc in documents_with_page_counts]
        avg_pages = {"avg": sum(page_counts) / len(page_counts)}

        # Calculate maximum pages
        max_pages = max(page_counts)

        # Calculate 95th percentile
        sorted_page_counts = sorted(page_counts)
        index_95 = int(len(sorted_page_counts) * 0.95)
        percentile_95 = (
            sorted_page_counts[index_95]
            if index_95 < len(sorted_page_counts)
            else sorted_page_counts[-1]
        )
    else:
        max_pages = 0
        percentile_95 = 0

    # Prepare current period data
    current_data = {
        "avg_pages": avg_pages["avg"] if avg_pages["avg"] else 0,
        "max_pages": max_pages,
        "percentile_95_pages": percentile_95,
    }

    # Get previous period data if comparison is enabled
    comparison_data = {}
    if compare and prev_start_date and prev_end_date:
        # Previous period data
        prev_document_qs = filter_queryset_by_date(
            document_qs, prev_start_date, prev_end_date
        )

        # Get page count statistics for the previous period
        prev_documents_with_page_counts = list(
            prev_document_qs.annotate(page_count=Count("semantic_pages")).values(
                "page_count"
            )
        )

        # Calculate average pages per document for previous period
        prev_avg_pages = {"avg": 0}
        prev_max_pages = 0
        prev_percentile_95 = 0

        if prev_documents_with_page_counts:
            prev_page_counts = [
                doc["page_count"] for doc in prev_documents_with_page_counts
            ]
            prev_avg_pages = {"avg": sum(prev_page_counts) / len(prev_page_counts)}

            # Calculate maximum pages for previous period
            prev_max_pages = max(prev_page_counts)

            # Calculate 95th percentile for previous period
            prev_sorted_page_counts = sorted(prev_page_counts)
            prev_index_95 = int(len(prev_sorted_page_counts) * 0.95)
            prev_percentile_95 = (
                prev_sorted_page_counts[prev_index_95]
                if prev_index_95 < len(prev_sorted_page_counts)
                else prev_sorted_page_counts[-1]
            )

        # Prepare previous period data
        previous_data = {
            "avg_pages": prev_avg_pages["avg"] if prev_avg_pages["avg"] else 0,
            "max_pages": prev_max_pages,
            "percentile_95_pages": prev_percentile_95,
        }

        # Calculate comparison data
        comparison_data = get_comparison_data(current_data, previous_data)

    # Combine all data for the template
    context = {
        "form": form,
        "docs_by_category": docs_by_category,
        "docs_by_status": docs_by_status,
        "avg_pages": avg_pages,
        "max_pages": max_pages,
        "percentile_95_pages": percentile_95,
        "start_date": start_date,
        "end_date": end_date,
        "compare": compare,
        "prev_start_date": prev_start_date,
        "prev_end_date": prev_end_date,
    }

    # Add comparison data to context
    context.update(comparison_data)

    return render(request, "stats/document_stats.html", context)


@staff_member_required
def user_stats(request):
    """
    Detailed statistics about users
    """
    # Process the time range form
    form = TimeRangeForm(request.GET or None)
    if form.is_valid():
        start_date, end_date = form.get_date_range()
        compare = form.cleaned_data.get("compare", False)
    else:
        # Default to last 30 days if form is not valid
        today = timezone.now().date()
        start_date = today - timedelta(days=30)
        end_date = today
        compare = False

    # Get previous period date range if comparison is enabled
    prev_start_date, prev_end_date = (
        form.get_previous_date_range() if compare else (None, None)
    )

    # Get total users count (this doesn't change with time range)
    total_users = User.objects.count()

    # Get active users (users who have logged in within the selected period)
    if start_date and end_date:
        active_users = User.objects.filter(
            last_login__date__gte=start_date, last_login__date__lte=end_date
        ).count()
    else:
        active_users = User.objects.filter(last_login__isnull=False).count()

    # Get staff users count (this doesn't change with time range)
    staff_users = User.objects.filter(is_staff=True).count()

    # Filter event queryset by date range
    event_qs = Event.objects.all()
    current_event_qs = filter_queryset_by_date(event_qs, start_date, end_date)

    # Get top users by dossier count (owners) for the current period
    top_users_by_dossier = (
        User.objects.annotate(
            dossier_count=Count(
                "dossier",
                filter=(
                    Q(
                        dossier__created_at__date__gte=start_date,
                        dossier__created_at__date__lte=end_date,
                    )
                    if start_date and end_date
                    else None
                ),
            )
        )
        .filter(dossier_count__gt=0)
        .order_by("-dossier_count")[:10]
    )

    # Get recent user activity from events for the current period
    recent_events = current_event_qs.order_by("-created_at")[:20]

    # Prepare current period data
    current_data = {
        "active_users": active_users,
    }

    # Get previous period data if comparison is enabled
    comparison_data = {}
    if compare and prev_start_date and prev_end_date:
        # Get active users for the previous period
        prev_active_users = User.objects.filter(
            last_login__date__gte=prev_start_date, last_login__date__lte=prev_end_date
        ).count()

        # Prepare previous period data
        previous_data = {
            "active_users": prev_active_users,
        }

        # Calculate comparison data
        comparison_data = get_comparison_data(current_data, previous_data)

    # Combine all data for the template
    context = {
        "form": form,
        "total_users": total_users,
        "active_users": active_users,
        "staff_users": staff_users,
        "top_users_by_dossier": top_users_by_dossier,
        "recent_events": recent_events,
        "start_date": start_date,
        "end_date": end_date,
        "compare": compare,
        "prev_start_date": prev_start_date,
        "prev_end_date": prev_end_date,
    }

    # Add comparison data to context
    context.update(comparison_data)

    return render(request, "stats/user_stats.html", context)


@staff_member_required
def exception_stats(request):
    """
    Detailed statistics about file extraction exceptions
    """
    # Process the time range form
    form = TimeRangeForm(request.GET or None)
    if form.is_valid():
        start_date, end_date = form.get_date_range()
        compare = form.cleaned_data.get("compare", False)
    else:
        # Default to last 30 days if form is not valid
        today = timezone.now().date()
        start_date = today - timedelta(days=30)
        end_date = today
        compare = False

    # Get previous period date range if comparison is enabled
    prev_start_date, prev_end_date = (
        form.get_previous_date_range() if compare else (None, None)
    )

    # Filter querysets by date range
    file_exception_qs = FileException.objects.all()
    original_file_qs = OriginalFile.objects.filter(status=FileStatus.ERROR)

    # Current period data
    current_file_exception_qs = filter_queryset_by_date(
        file_exception_qs, start_date, end_date
    )
    current_original_file_qs = filter_queryset_by_date(
        original_file_qs, start_date, end_date
    )

    # Get total counts
    total_exceptions = (
        current_file_exception_qs.count() + current_original_file_qs.count()
    )
    original_file_exceptions = current_original_file_qs.count()
    extracted_file_exceptions = current_file_exception_qs.count()

    # Get total files for exception rate calculation
    total_original_files = OriginalFile.objects.filter(
        created_at__date__gte=(
            start_date if start_date else timezone.now().date() - timedelta(days=3650)
        ),
        created_at__date__lte=end_date if end_date else timezone.now().date(),
    ).count()

    # Calculate exception rate
    exception_rate = round((total_exceptions / max(total_original_files, 1)) * 100, 1)

    # Get exceptions by type
    exceptions_by_type = []

    # Get FileException types
    file_exception_types = (
        current_file_exception_qs.values("exception_type")
        .annotate(count=Count("uuid"))
        .order_by("-count")
    )

    # Get OriginalFile exception types
    # Since OriginalFile doesn't have a direct exception_type field, we'll need to analyze the exception messages
    # or use a default type for now

    # Combine the exception types and calculate percentages
    exception_type_mapping = dict(ExceptionDetails.HypoDossierException.choices)

    # Track unsupported file types
    unsupported_file_types = {}

    # Get all unsupported filetype exceptions to analyze file types
    unsupported_filetype_exceptions = current_file_exception_qs.filter(
        exception_type=ExceptionDetails.HypoDossierException.UNSUPPORTED_FILETYPE
    ).values("en", "uuid")

    # Extract file types from exception messages
    import re

    file_type_pattern = re.compile(r"File type '(\.[\w]+)' not supported")

    for exception in unsupported_filetype_exceptions:
        message = exception.get("en", "")
        if not message:
            continue

        match = file_type_pattern.search(message)

        if match:
            file_type = match.group(
                1
            ).lower()  # Extract the file extension and normalize to lowercase

            if file_type in unsupported_file_types:
                unsupported_file_types[file_type] += 1
            else:
                unsupported_file_types[file_type] = 1

    # Convert to a list of dictionaries for the template
    unsupported_file_types_list = [
        {"extension": ext, "count": count}
        for ext, count in unsupported_file_types.items()
    ]

    print(unsupported_file_types_list)

    # Sort by count in descending order
    unsupported_file_types_list = sorted(
        unsupported_file_types_list, key=lambda x: x["count"], reverse=True
    )

    # Calculate total unsupported files
    total_unsupported = sum(item["count"] for item in unsupported_file_types_list)

    # Add percentage to each file type
    for file_type in unsupported_file_types_list:
        file_type["percentage"] = round(
            (file_type["count"] / max(total_unsupported, 1)) * 100, 1
        )

    for exception_type in file_exception_types:
        exception_id = exception_type["exception_type"]
        exception_name = exception_type_mapping.get(exception_id, "Unknown")
        count = exception_type["count"]
        percentage = round((count / max(total_exceptions, 1)) * 100, 1)

        exceptions_by_type.append(
            {
                "exception_id": exception_id,
                "exception_name": exception_name,
                "count": count,
                "percentage": percentage,
            }
        )

    # Add a generic entry for original file exceptions if there are any
    if original_file_exceptions > 0:
        percentage = round(
            (original_file_exceptions / max(total_exceptions, 1)) * 100, 1
        )
        exceptions_by_type.append(
            {
                "exception_id": 999,  # Using UNMAPPED_EXCEPTION as a placeholder
                "exception_name": "Original File Processing Error",
                "count": original_file_exceptions,
                "percentage": percentage,
            }
        )

    # Sort by count in descending order
    exceptions_by_type = sorted(
        exceptions_by_type, key=lambda x: x["count"], reverse=True
    )

    # Get exceptions by account
    exceptions_by_account = []

    # Get accounts with file exceptions
    accounts_with_exceptions = Account.objects.filter(
        Q(
            dossier__fileexception__created_at__date__gte=(
                start_date
                if start_date
                else timezone.now().date() - timedelta(days=3650)
            ),
            dossier__fileexception__created_at__date__lte=(
                end_date if end_date else timezone.now().date()
            ),
        )
        | Q(
            dossier__original_files__status=FileStatus.ERROR,
            dossier__original_files__created_at__date__gte=(
                start_date
                if start_date
                else timezone.now().date() - timedelta(days=3650)
            ),
            dossier__original_files__created_at__date__lte=(
                end_date if end_date else timezone.now().date()
            ),
        )
    ).distinct()

    for account in accounts_with_exceptions:
        # Count FileExceptions for this account
        file_exception_count = FileException.objects.filter(
            dossier__account=account,
            created_at__date__gte=(
                start_date
                if start_date
                else timezone.now().date() - timedelta(days=3650)
            ),
            created_at__date__lte=end_date if end_date else timezone.now().date(),
        ).count()

        # Count OriginalFile errors for this account
        original_file_error_count = OriginalFile.objects.filter(
            dossier__account=account,
            status=FileStatus.ERROR,
            created_at__date__gte=(
                start_date
                if start_date
                else timezone.now().date() - timedelta(days=3650)
            ),
            created_at__date__lte=end_date if end_date else timezone.now().date(),
        ).count()

        total_account_exceptions = file_exception_count + original_file_error_count
        percentage = round(
            (total_account_exceptions / max(total_exceptions, 1)) * 100, 1
        )

        exceptions_by_account.append(
            {
                "name": account.name,
                "count": total_account_exceptions,
                "percentage": percentage,
            }
        )

    # Sort by count in descending order
    exceptions_by_account = sorted(
        exceptions_by_account, key=lambda x: x["count"], reverse=True
    )

    # Prepare current period data
    current_data = {
        "total_exceptions": total_exceptions,
        "original_file_exceptions": original_file_exceptions,
        "extracted_file_exceptions": extracted_file_exceptions,
        "exception_rate": exception_rate,
    }

    # Get previous period data if comparison is enabled
    comparison_data = {}
    if compare and prev_start_date and prev_end_date:
        # Previous period data
        prev_file_exception_qs = filter_queryset_by_date(
            file_exception_qs, prev_start_date, prev_end_date
        )
        prev_original_file_qs = filter_queryset_by_date(
            original_file_qs, prev_start_date, prev_end_date
        )

        # Get total counts for previous period
        prev_total_exceptions = (
            prev_file_exception_qs.count() + prev_original_file_qs.count()
        )
        prev_original_file_exceptions = prev_original_file_qs.count()
        prev_extracted_file_exceptions = prev_file_exception_qs.count()

        # Get total files for previous period exception rate calculation
        prev_total_original_files = OriginalFile.objects.filter(
            created_at__date__gte=prev_start_date, created_at__date__lte=prev_end_date
        ).count()

        # Calculate exception rate for previous period
        prev_exception_rate = round(
            (prev_total_exceptions / max(prev_total_original_files, 1)) * 100, 1
        )

        # Prepare previous period data
        previous_data = {
            "total_exceptions": prev_total_exceptions,
            "original_file_exceptions": prev_original_file_exceptions,
            "extracted_file_exceptions": prev_extracted_file_exceptions,
            "exception_rate": prev_exception_rate,
        }

        # Calculate comparison data
        comparison_data = get_comparison_data(current_data, previous_data)

    # Combine all data for the template
    context = {
        "form": form,
        "total_exceptions": total_exceptions,
        "original_file_exceptions": original_file_exceptions,
        "extracted_file_exceptions": extracted_file_exceptions,
        "exception_rate": exception_rate,
        "exceptions_by_type": exceptions_by_type,
        "exceptions_by_account": exceptions_by_account,
        "unsupported_file_types": unsupported_file_types_list,
        "total_unsupported": total_unsupported,
        "start_date": start_date,
        "end_date": end_date,
        "compare": compare,
        "prev_start_date": prev_start_date,
        "prev_end_date": prev_end_date,
    }

    # Add comparison data to context
    context.update(comparison_data)

    return render(request, "stats/exception_stats.html", context)


@staff_member_required
def category_documents(request, category_id):
    """
    Display documents of a specific category sorted by confidence
    """
    # Process the time range form
    form = TimeRangeForm(request.GET or None)
    if form.is_valid():
        start_date, end_date = form.get_date_range()
    else:
        # Default to last 30 days if form is not valid
        today = timezone.now().date()
        start_date = today - timedelta(days=30)
        end_date = today

    # Get the categories with this ID
    from dossier.models import DocumentCategory

    try:
        # Get all categories with this ID (might be multiple due to different accounts)
        categories = DocumentCategory.objects.filter(id=category_id)
        if not categories.exists():
            raise DocumentCategory.DoesNotExist

        # Use the first category for display purposes
        category = categories.first()
        # Get all category IDs to use in the document filter
        category_ids = list(categories.values_list("id", flat=True))
    except (DocumentCategory.DoesNotExist, ValueError):
        # Handle case where category doesn't exist
        return render(
            request,
            "stats/category_documents.html",
            {
                "error": "Category not found",
                "form": form,
                "start_date": start_date,
                "end_date": end_date,
            },
        )

    # Get documents of these categories, filtered by date range
    document_list = SemanticDocument.objects.filter(
        document_category__id__in=category_ids,
        created_at__date__gte=(
            start_date if start_date else timezone.now().date() - timedelta(days=3650)
        ),
        created_at__date__lte=end_date if end_date else timezone.now().date(),
    ).order_by(
        "confidence_value"
    )  # Sort by confidence in ascending order (least confident first)

    # Prefetch related pages for each document with proper ordering
    from django.db.models import Prefetch
    from semantic_document.models import SemanticPage

    document_list = document_list.prefetch_related(
        Prefetch("semantic_pages", queryset=SemanticPage.objects.order_by("number"))
    )

    # Set up pagination
    paginator = Paginator(document_list, 10)  # Show 10 documents per page
    page = request.GET.get("page")
    try:
        documents = paginator.page(page)
    except PageNotAnInteger:
        # If page is not an integer, deliver first page
        documents = paginator.page(1)
    except EmptyPage:
        # If page is out of range, deliver last page of results
        documents = paginator.page(paginator.num_pages)

    # Get confidence statistics from the full document list, not just the current page
    if document_list.exists():
        confidence_stats = document_list.aggregate(
            avg=Avg("confidence_value"),
            max=Max("confidence_value"),
            min=Min("confidence_value"),
        )
        avg_confidence = confidence_stats["avg"]
        max_confidence = confidence_stats["max"]
        min_confidence = confidence_stats["min"]
    else:
        avg_confidence = 0
        max_confidence = 0
        min_confidence = 0

    # Get the current query parameters for pagination links
    query_params = request.GET.copy()
    if "page" in query_params:
        del query_params["page"]

    # Check if this is the special category that should be wider
    is_wide_layout = True

    # Prepare data for the template
    context = {
        "form": form,
        "category": category,
        "documents": documents,
        "document_count": document_list.count(),
        "avg_confidence": avg_confidence,
        "max_confidence": max_confidence,
        "min_confidence": min_confidence,
        "start_date": start_date,
        "end_date": end_date,
        "is_paginated": True,
        "paginator": paginator,
        "query_params": query_params.urlencode(),
        "is_wide_layout": is_wide_layout,
    }

    return render(request, "stats/category_documents.html", context)


@staff_member_required
def document_pages_api(request, document_uuid):
    """
    API endpoint to get all pages for a document
    """
    try:
        # Get the document
        document = SemanticDocument.objects.get(uuid=document_uuid)

        # Get all pages for the document, sorted by page number
        pages = document.semantic_pages.all().order_by("number")

        # Prepare the response data
        page_data = []
        for page in pages:
            try:
                image_url = page.processed_page.image.fast_url
            except (AttributeError, ValueError):
                image_url = None

            page_data.append(
                {
                    "number": page.number,
                    "image_url": image_url,
                }
            )

        return JsonResponse(
            {
                "success": True,
                "document_uuid": str(document.uuid),
                "total_pages": len(page_data),
                "pages": page_data,
            }
        )
    except SemanticDocument.DoesNotExist:
        return JsonResponse(
            {
                "success": False,
                "error": "Document not found",
            },
            status=404,
        )
    except Exception as e:
        return JsonResponse(
            {
                "success": False,
                "error": str(e),
            },
            status=500,
        )


@staff_member_required
def file_stats(request):
    """
    Detailed statistics about original files and extracted files
    """
    # Process the time range form
    form = TimeRangeForm(request.GET or None)
    if form.is_valid():
        start_date, end_date = form.get_date_range()
        compare = form.cleaned_data.get("compare", False)
    else:
        # Default to last 30 days if form is not valid
        today = timezone.now().date()
        start_date = today - timedelta(days=30)
        end_date = today
        compare = False

    # Get previous period date range if comparison is enabled
    prev_start_date, prev_end_date = (
        form.get_previous_date_range() if compare else (None, None)
    )

    # Filter querysets by date range
    original_file_qs = OriginalFile.objects.all()
    extracted_file_qs = ExtractedFile.objects.all()

    # Current period data
    current_original_file_qs = filter_queryset_by_date(
        original_file_qs, start_date, end_date
    )
    current_extracted_file_qs = filter_queryset_by_date(
        extracted_file_qs, start_date, end_date
    )

    # Get counts for current period
    original_file_count = current_original_file_qs.count()
    extracted_file_count = current_extracted_file_qs.count()

    # Get recent original files within the selected period
    if start_date and end_date:
        recent_period_start = max(end_date - timedelta(days=30), start_date)
        recent_original_files = filter_queryset_by_date(
            original_file_qs, recent_period_start, end_date
        ).count()
    else:
        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        recent_original_files = filter_queryset_by_date(
            original_file_qs, thirty_days_ago, timezone.now().date()
        ).count()

    # Get error count for the current period
    error_count = current_original_file_qs.filter(status=FileStatus.ERROR).count()

    # Calculate processing success rate for the current period
    processed_count = current_original_file_qs.filter(
        status=FileStatus.PROCESSED
    ).count()
    if original_file_count > 0:
        processing_success_rate = round((processed_count / original_file_count) * 100)
    else:
        processing_success_rate = 0

    # Get average extracted files per original file for the current period
    if original_file_count > 0:
        avg_extracted_files = extracted_file_count / original_file_count
    else:
        avg_extracted_files = 0

    # Get files by status for the current period
    files_by_status = (
        current_original_file_qs.values("status")
        .annotate(count=Count("uuid"))
        .order_by("-count")
    )

    # Get files by source for the current period
    files_by_source = (
        current_original_file_qs.values("source")
        .annotate(count=Count("uuid"))
        .order_by("-count")
    )

    # Get top uploaders for the current period
    top_uploaders = (
        current_original_file_qs.values("create_user__username")
        .annotate(count=Count("uuid"))
        .order_by("-count")[:10]
    )

    # Prepare current period data
    current_data = {
        "original_file_count": original_file_count,
        "extracted_file_count": extracted_file_count,
        "error_count": error_count,
        "processing_success_rate": processing_success_rate,
        "avg_extracted_files": avg_extracted_files,
    }

    # Get previous period data if comparison is enabled
    comparison_data = {}
    if compare and prev_start_date and prev_end_date:
        # Previous period data
        prev_original_file_qs = filter_queryset_by_date(
            original_file_qs, prev_start_date, prev_end_date
        )
        prev_extracted_file_qs = filter_queryset_by_date(
            extracted_file_qs, prev_start_date, prev_end_date
        )

        # Get counts for previous period
        prev_original_file_count = prev_original_file_qs.count()
        prev_extracted_file_count = prev_extracted_file_qs.count()

        # Get error count for the previous period
        prev_error_count = prev_original_file_qs.filter(status=FileStatus.ERROR).count()

        # Calculate processing success rate for the previous period
        prev_processed_count = prev_original_file_qs.filter(
            status=FileStatus.PROCESSED
        ).count()
        if prev_original_file_count > 0:
            prev_processing_success_rate = round(
                (prev_processed_count / prev_original_file_count) * 100
            )
        else:
            prev_processing_success_rate = 0

        # Get average extracted files per original file for the previous period
        if prev_original_file_count > 0:
            prev_avg_extracted_files = (
                prev_extracted_file_count / prev_original_file_count
            )
        else:
            prev_avg_extracted_files = 0

        # Prepare previous period data
        previous_data = {
            "original_file_count": prev_original_file_count,
            "extracted_file_count": prev_extracted_file_count,
            "error_count": prev_error_count,
            "processing_success_rate": prev_processing_success_rate,
            "avg_extracted_files": prev_avg_extracted_files,
        }

        # Calculate comparison data
        comparison_data = get_comparison_data(current_data, previous_data)

    # Combine all data for the template
    context = {
        "form": form,
        "original_file_count": original_file_count,
        "extracted_file_count": extracted_file_count,
        "recent_original_files": recent_original_files,
        "error_count": error_count,
        "processing_success_rate": processing_success_rate,
        "avg_extracted_files": avg_extracted_files,
        "files_by_status": files_by_status,
        "files_by_source": files_by_source,
        "top_uploaders": top_uploaders,
        "start_date": start_date,
        "end_date": end_date,
        "compare": compare,
        "prev_start_date": prev_start_date,
        "prev_end_date": prev_end_date,
    }

    # Add comparison data to context
    context.update(comparison_data)

    return render(request, "stats/file_stats.html", context)
