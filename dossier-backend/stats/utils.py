def filter_queryset_by_date(
    queryset, start_date=None, end_date=None, date_field="created_at"
):
    """
    Filter a queryset by date range

    Args:
        queryset: The queryset to filter
        start_date: The start date (inclusive)
        end_date: The end date (inclusive)
        date_field: The field to filter on

    Returns:
        Filtered queryset
    """
    filters = {}

    if start_date:
        filters[f"{date_field}__date__gte"] = start_date

    if end_date:
        filters[f"{date_field}__date__lte"] = end_date

    if filters:
        return queryset.filter(**filters)

    return queryset


def calculate_percentage_change(current_value, previous_value):
    """
    Calculate the percentage change between two values

    Args:
        current_value: The current value
        previous_value: The previous value

    Returns:
        Percentage change as a float, or None if previous_value is 0
    """
    if previous_value is None or previous_value == 0:
        return None

    if current_value is None:
        current_value = 0

    change = ((current_value - previous_value) / previous_value) * 100
    return round(change, 1)


def get_comparison_data(current_data, previous_data):
    """
    Calculate comparison data between current and previous periods

    Args:
        current_data: Dictionary of current period data
        previous_data: Dictionary of previous period data

    Returns:
        Dictionary with comparison data
    """
    comparison = {}

    for key in current_data:
        if key in previous_data:
            current_value = current_data[key]
            previous_value = previous_data[key]

            # Skip non-numeric values
            if not isinstance(current_value, (int, float)) or not isinstance(
                previous_value, (int, float)
            ):
                continue

            change = calculate_percentage_change(current_value, previous_value)
            comparison[f"{key}_change"] = change
            comparison[f"{key}_previous"] = previous_value

    return comparison
