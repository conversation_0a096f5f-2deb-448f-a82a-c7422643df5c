import pytest
from pathlib import Path

from dossier_zipper.packer import get_target_filename_from_semantic_document


def test_basic_filename_without_uuid():
    result = get_target_filename_from_semantic_document(
        Path("/tmp"), "document.pdf", False, "test-uuid"
    )
    assert result == Path("/tmp/document.pdf")
    assert isinstance(result, Path)


def test_filename_with_uuid():
    result = get_target_filename_from_semantic_document(
        Path("/tmp"), "document.pdf", True, "test-uuid"
    )
    assert result == Path("/tmp/document test-uuid.pdf")
    assert isinstance(result, Path)


def test_filename_with_slashes():
    result = get_target_filename_from_semantic_document(
        Path("/tmp"), "folder/document.pdf", False, "test-uuid"
    )
    assert result == Path("/tmp/folder_document.pdf")


def test_filename_with_multiple_dots():
    result = get_target_filename_from_semantic_document(
        Path("/tmp"), "my.document.pdf", True, "test-uuid"
    )
    assert result == Path("/tmp/my.document test-uuid.pdf")


@pytest.mark.parametrize(
    "dest_path,filename,expected",
    [
        (Path("/tmp"), "doc.pdf", "doc.pdf"),
        (Path("/tmp/test"), "doc.txt", "doc.txt"),
        (Path("/"), "doc.docx", "doc.docx"),
    ],
)
def test_various_paths_without_uuid(dest_path, filename, expected):
    result = get_target_filename_from_semantic_document(
        dest_path, filename, False, "test-uuid"
    )
    assert result == dest_path / expected


def test_filename_without_extension():
    result = get_target_filename_from_semantic_document(
        Path("/tmp"), "document", True, "test-uuid"
    )
    assert result == Path("/tmp/document")


@pytest.fixture
def temp_dir(tmp_path):
    return tmp_path


def test_with_real_filesystem(temp_dir):
    result = get_target_filename_from_semantic_document(
        temp_dir, "test.pdf", True, "test-uuid"
    )
    assert result.parent == temp_dir
    assert result.name == "test test-uuid.pdf"
    # Verify path is writable
    result.parent.mkdir(parents=True, exist_ok=True)
    result.touch()
    assert result.exists()


def test_long_filename_with_uuid():
    result = get_target_filename_from_semantic_document(
        Path("/tmp"),
        "document_615_Photos_immeuble_615_Photos_immeuble_Grasso_photos_immeuble_Grand_Montfleury_6_Grasso_photos_immeuble_Grand_Montfleury_6 615_Photos_immeuble_Grasso_photos_immeuble_Grand_Montfleury_6 Grasso_photos_immeuble_Grand_Montfleury_6.pdf",
        True,
        "5130dea7-ddde-4ad2-8274-b40371975055",
    )

    assert isinstance(result, Path)
    assert result == Path(
        "/tmp/document_615_Photos_immeuble_615_Photos_immeuble_Grasso_photos_immeuble_Grand_Montfleury_6_Grasso_photos_immeuble_Grand_Montfleury_6 615_Photos_immeuble_Grasso_photos_immeuble_Grand_Montfleury_6 Grass 5130dea7-ddde-4ad2-8274-b40371975055.pdf"
    )

    str_result = str(result)
    len_result = len(str_result)
    assert len_result == 246  # Max length for a filename in linux is 255
