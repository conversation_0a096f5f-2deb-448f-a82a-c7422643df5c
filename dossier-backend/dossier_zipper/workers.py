from datetime import datetime
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Union, Optional

import requests
import structlog
from channels.db import database_sync_to_async
from django.utils import timezone
from structlog.contextvars import bound_contextvars

from core.helpers import remove_invalid_chars
from dossier.helpers import create_dossier_file_without_saving
from dossier.helpers_filename import create_filenames_unique
from dossier.helpers_v2 import prepare_semantic_dossier
from dossier.models import Dossier, DossierExport
from dossier.schemas import DateRange, SemanticDossier
from dossier_zipper.packer import package_offline_version
from dossier_zipper.schemas import DossierZipRequestV1, DossierZipResponseV1

logger = structlog.getLogger(__name__)


# TODO: Check what uses this, as looks like it violates DRY
def generate_dossier_zip_request(
    dossier: Dossier,
    add_uuid_suffix: Optional[bool] = False,
    add_metadata_json: Optional[bool] = True,
    from_date: datetime = None,
    to_date: datetime = None,
) -> DossierZipRequestV1:
    semantic_dossier: SemanticDossier = prepare_semantic_dossier(
        dossier=dossier,
        include_annotations=True,
        hide_empty_semantic_documents=True,
        date_range=(
            DateRange(
                from_date=(
                    from_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ") if from_date else None
                ),
                to_date=to_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ") if to_date else None,
            )
            if from_date or to_date
            else None
        ),
    )

    data = semantic_dossier.model_dump()

    data["uuid"] = str(data["uuid"])

    dossier_file = create_dossier_file_without_saving(
        dossier, f"{remove_invalid_chars(dossier.name)}.zip"
    )

    dossier_file.save()

    dossier_export = DossierExport.objects.create(dossier=dossier, file=dossier_file)

    data["semantic_documents"] = create_filenames_unique(data["semantic_documents"])

    for i in range(len(data["semantic_documents"])):
        data["semantic_documents"][i]["uuid"] = str(
            data["semantic_documents"][i]["uuid"]
        )

    request_dossier = SemanticDossier(**data)

    return DossierZipRequestV1(
        zip_request_uuid=dossier_export.uuid,
        semantic_dossier=request_dossier,
        put_upload_url=dossier_file.put_url,
        add_uuid_suffix=add_uuid_suffix,
        add_metadata_json=add_metadata_json,
        enable_pdfa_conversion_for_export=dossier.account.enable_pdfa_conversion_for_export,
    )


def process_dossier_zip_request(
    *, dossier_zip_request: Union[str, bytes, DossierZipRequestV1]
) -> str:
    try:
        start = timezone.now()
        if isinstance(dossier_zip_request, str) or isinstance(
            dossier_zip_request, bytes
        ):
            dossier_zip_request = DossierZipRequestV1.model_validate_json(
                dossier_zip_request
            )
        else:
            dossier_zip_request = dossier_zip_request
        with bound_contextvars(
            zip_request_uuid=str(dossier_zip_request.zip_request_uuid)
        ):
            logger.info("processing dossier zip request")
            try:
                with TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)

                    status_with_document_browser_viewer = (
                        True
                        if dossier_zip_request.with_document_browser_viewer is None
                        else dossier_zip_request.with_document_browser_viewer
                    )

                    package_offline_version(
                        dossier_zip_request.semantic_dossier,
                        temp_path,
                        status_with_document_browser_viewer,
                        add_uuid_suffix=dossier_zip_request.add_uuid_suffix,
                        add_metadata_json=dossier_zip_request.add_metadata_json,
                        enable_pdfa_conversion_for_export=dossier_zip_request.enable_pdfa_conversion_for_export,
                    )
                    with (temp_path / "package.zip").open("rb") as fp:
                        response = requests.put(
                            dossier_zip_request.put_upload_url, data=fp.read()
                        )
                        response.raise_for_status()
            except Exception as e:
                logger.exception(
                    "could not create offline package for dossier_zip_request",
                )
                raise e

            logger.info(
                "successfully finished processing dossier zip request",
                duration_seconds=(timezone.now() - start).total_seconds(),
            )
            return DossierZipResponseV1(
                zip_request_uuid=dossier_zip_request.zip_request_uuid
            ).model_dump_json()
    except Exception:
        logger.exception("could not process message", exc_info=True)


async def async_process_dossier_zip_request(
    *, dossier_zip_request: Union[str, bytes, DossierZipRequestV1]
) -> str:
    return await database_sync_to_async(process_dossier_zip_request)(
        dossier_zip_request=dossier_zip_request
    )
