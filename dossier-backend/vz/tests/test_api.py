# Create your tests here.
import json
import uuid

# from datetime import timed<PERSON>ta, datetime
from pathlib import Path

# from typing import List

import pytest
import structlog
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse

# from django.utils import timezone
# from freezegun import freeze_time
import jwt
from jwcrypto import jwk

# from pydantic import TypeAdapter
from pytest_mock import MockerFixture

import dossier.schemas as dossier_schemas
from vz.schemas.schemas import AccountName
from core.authentication import AuthenticatedClient
from dossier.fakes import (
    add_some_fake_semantic_documents,
)
from dossier.models import (
    Dossier,
    OriginalFile,
    # DocumentCategory,
)
from dossier.schemas import Language, Message
from dossier_zipper.conftest import mocked_get_dossier  # noqa: F401

# from semantic_document.models import (
#     SemanticDocument,
# )
from workers.models import SemanticDocumentExport
from workers.schemas import SemanticDocumentPDFRequestV1
from workers.workers import process_semantic_dossier_pdf_request
from vz.schemas import schemas


User: AbstractUser = get_user_model()


logger = structlog.get_logger(__name__)


pytestmark = pytest.mark.django_db


def test_ping(vz_authenticated_client, vz_account, set_vz_JWK):
    res = vz_authenticated_client.get(reverse("vz-api:ping"))
    assert res.status_code == 200
    assert Message.model_validate_json(res.content) == Message(detail="pong")


def test_create_dossier_api_success(vz_authenticated_client, vz_account, set_vz_JWK):
    # Base case to check whether we can create a dossier
    url = reverse("vz-api:create-dossier")

    external_dossier_id = str(uuid.uuid4())

    result = vz_authenticated_client.post(
        path=url,
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, lang="fr"
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(
        external_id=external_dossier_id, account__key=AccountName.vz.value
    )
    parsed = schemas.Dossier.model_validate_json(result.content)
    assert parsed.external_dossier_id == external_dossier_id
    assert parsed.uuid == dossier.uuid

    # Test for conflict when dossier already exists
    result = vz_authenticated_client.post(
        path=url,
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, lang="fr"
        ).model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 409


def test_update_dossier_api_success(vz_authenticated_client, vz_account, set_vz_JWK):
    # Base case to check whether we can create a dossier
    external_dossier_id = str(uuid.uuid4())

    result = vz_authenticated_client.post(
        path=reverse("vz-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, lang="fr"
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    # Test changing two params
    result = vz_authenticated_client.patch(
        path=reverse(
            "vz-api:update-dossier",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data=schemas.ChangeDossier(
            name="Test Dossier 2",
            external_dossier_id=external_dossier_id,
            lang=Language.fr,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    assert dossier.name == "Test Dossier 2"
    assert dossier.lang == "fr"

    # Test changing one param
    result = vz_authenticated_client.patch(
        path=reverse(
            "vz-api:update-dossier",
            kwargs=dict(external_dossier_id=external_dossier_id),
        ),
        data=schemas.ChangeDossier(
            name="Test Dossier 3",
            external_dossier_id=external_dossier_id,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    assert dossier.name == "Test Dossier 3"
    # lang should not be changed
    assert dossier.lang == "fr"


def test_create_to_delete_dossier_api_success(
    vz_authenticated_client, vz_account, set_vz_JWK
):
    # Test creating and deleting a dossier via API
    url = reverse("vz-api:create-dossier")

    external_dossier_id = str(uuid.uuid4())

    result = vz_authenticated_client.post(
        path=url,
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, lang="fr"
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    assert Dossier.objects.get(
        external_id=external_dossier_id,
        account__key=vz_account.key,
    )

    result = vz_authenticated_client.delete(
        path=reverse(
            "vz-api:dossier-delete",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
    )

    assert result.status_code == 202

    # Setting expiry date to past causes object to not be visible
    # as default dossier model manager has been overwritten
    assert (
        Dossier.objects.filter(
            external_id=external_dossier_id,
            account__key=vz_account.key,
        ).count()
        == 0
    )


def test_delete_dossier_api_failure(vz_authenticated_client, set_vz_JWK):
    # Expect failure when trying to delete a dossier that does not exist
    result = vz_authenticated_client.delete(
        path=reverse(
            "vz-api:dossier-delete",
            kwargs={"external_dossier_id": str(uuid.uuid4())},
        )
    )

    assert result.status_code == 404


def test_add_original_file(
    vz_authenticated_client,
    vz_account,
    mocker: MockerFixture,
    set_vz_JWK,
):
    """
    Test the behavior of the 'add-original-file' API endpoint for adding original files to a dossier.

    Specifically, it verifies:

    Successful file upload and its subsequent processing.
    Default prevention of duplicate file uploads.
    Option to allow and rename duplicate files.
    """
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    vz_authenticated_client.post(
        path=reverse("vz-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, lang="fr"
        ).model_dump_json(),
        content_type="application/json",
    )

    file = SimpleUploadedFile(
        "test_page1.jpg", b"file_content1", content_type="image/jpeg"
    )

    process_original_file_mock = mocker.patch(
        "dossier.services_external.process_original_file"
    )
    response = vz_authenticated_client.post(
        reverse(
            "vz-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == 201
    original_file = dossier_schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    # Check correct association with dossier
    assert original_file.dossier.external_id == external_dossier_id

    # # Test checking if file exists by filename, these should be refactored into separate test
    # assert vz_authenticated_client.get(
    #     path=reverse(
    #         "vz-api:check-original-filename",
    #         kwargs={
    #             "external_dossier_id": str(external_dossier_id),
    #             "original_filename": original_file.file.name,
    #         },
    #     ),
    # ).json()

    # assert (
    #     vz_authenticated_client.get(
    #         path=reverse(
    #             "vz-api:check-original-filename",
    #             kwargs={
    #                 "external_dossier_id": str(external_dossier_id),
    #                 "original_filename": "wrong_filename.pdf",
    #             },
    #         ),
    #     ).json()
    #     is False
    # )

    # same file should not be allowed by default
    response = vz_authenticated_client.post(
        reverse(
            "vz-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == 409

    assert (
        "The file test_page1.jpg already exists in the dossier"
        in response.json()["detail"]
    )

    # Use parameter to allow duplicate files
    response = vz_authenticated_client.post(
        reverse(
            "vz-api:add-original-file",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
        data={"file": file, "allow_duplicate_and_rename": True},
        format="multipart",
    )

    assert response.status_code == 201
    duplicate_and_rename_original_file_response = (
        dossier_schemas.CreatedObjectReference(**response.json())
    )

    duplicate_and_rename_original_file = OriginalFile.objects.get(
        uuid=duplicate_and_rename_original_file_response.uuid
    )

    # Check that file model objects are different
    assert original_file.uuid != duplicate_and_rename_original_file.uuid

    # Check that new duplicate file has new name
    assert original_file.file.name != duplicate_and_rename_original_file.file.name

    # Check that they belong to the same dossier
    assert original_file.dossier == duplicate_and_rename_original_file.dossier


# def test_get_semantic_documents_api_success(
#     vz_authenticated_client, vz_account, document_categories, set_vz_JWK
# ):
#     external_dossier_id = str(uuid.uuid4())

#     # Create a dossier
#     assert (
#         vz_authenticated_client.post(
#             path=reverse("vz-api:create-dossier"),
#             data=schemas.CreateDossier(
#                 name="Test Dossier",
#                 external_dossier_id=external_dossier_id,
#                 lang="fr",
#             ).model_dump_json(),
#             content_type="application/json",
#         ).status_code
#         == 201
#     )

#     dossier = Dossier.objects.get(external_id=external_dossier_id)

#     semantic_documents = add_some_fake_semantic_documents(
#         dossier=dossier, allow_empty_docs=False
#     )

#     response = vz_authenticated_client.get(
#         path=reverse(
#             "vz-api:semantic-documents",
#             kwargs={"external_dossier_id": external_dossier_id},
#         )
#     )

#     assert response.status_code == 200

#     assert ("null" in str(response.content)) is False

#     parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_python(
#         response.json()
#     )

#     assert len(semantic_documents) == len(parsed)

#     documents_set = set([doc.uuid for doc in parsed])
#     for semantic_document in semantic_documents:
#         assert semantic_document.uuid in documents_set

#     response = vz_authenticated_client.get(
#         path=f"{reverse('vz-api:semantic-documents', kwargs={'external_dossier_id': external_dossier_id})}?show_pages=true",
#     )

#     parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(response.content)
#     documents_set = set([doc.uuid for doc in parsed])
#     pages_set = set([page.uuid for doc in parsed for page in doc.semantic_pages])

#     for semantic_document in semantic_documents:
#         assert semantic_document.uuid in documents_set
#         assert semantic_document.access_mode.name == "READ_WRITE"
#         for page in semantic_document.semantic_pages.all():
#             assert page.uuid in pages_set


# # This test is periodically flaky and I'm not sure why. If it gets annoying disable it
# def test_get_semantic_documents_api_success_deep_last_update(
#     vz_authenticated_client, vz_account, document_categories, set_vz_JWK
# ):
#     """Test that the last_update field is correctly set on the semantic documents and pages"""
#     external_dossier_id = str(uuid.uuid4())

#     current_time = timezone.now()

#     # Step 1: 10 min ago we create a dossier with some documents

#     current_minus_10 = current_time - timedelta(minutes=10)
#     with freeze_time(current_minus_10):
#         fixed_time = current_minus_10.replace(tzinfo=timezone.utc)
#         # Create a dossier
#         assert (
#             vz_authenticated_client.post(
#                 path=reverse("vz-api:create-dossier"),
#                 data=schemas.CreateDossier(
#                     name="Test Dossier",
#                     external_dossier_id=external_dossier_id,
#                     lang="fr",
#                 ).model_dump_json(),
#                 content_type="application/json",
#             ).status_code
#             == 201
#         )

#         dossier = Dossier.objects.get(external_id=external_dossier_id)

#         add_some_fake_semantic_documents(dossier=dossier, allow_empty_docs=False)

#         response = vz_authenticated_client.get(
#             path=reverse(
#                 "vz-api:semantic-documents",
#                 kwargs={"external_dossier_id": external_dossier_id},
#             )
#         )

#         assert response.status_code == 200

#         parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(
#             response.content
#         )

#         # difference in timestamps might be a few milliseconds negative due to rounding
#         assert (parsed[0].last_change - fixed_time) < timedelta(seconds=2)

#         # Last change to the document was by creating it which sets the updated_at
#         assert parsed[0].last_change == parsed[0].updated_at

#     # Step 2: 5 min ago: soft delete a page which will change last_change but not updated_at

#     current_minus_5 = current_time - timedelta(minutes=5)
#     with freeze_time(current_minus_5):
#         fixed_time = current_minus_5.replace(tzinfo=timezone.utc)

#         semantic_document = SemanticDocument.objects.get(uuid=parsed[0].uuid)

#         # Soft delete a page
#         first_page = semantic_document.semantic_pages.first()
#         first_page.deleted_at = current_minus_5
#         first_page.save()

#         response = vz_authenticated_client.get(
#             path=reverse(
#                 "vz-api:semantic-documents",
#                 kwargs={"external_dossier_id": external_dossier_id},
#             )
#         )

#         assert response.status_code == 200

#         parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(
#             response.content
#         )

#         # The updated_at did not change since creation of the document but the last_change
#         # changed due to the soft delete of a page

#         # TODO: Figure out why this does work locally but "sometimes" not on staging (fails in 50% of tries)
#         # assert (parsed[0].last_change - parsed[0].updated_at) > timedelta(minutes=4)

#         # Soft delete happened in less than 2 seconds
#         # difference in timestamps might be a few milliseconds negative due to rounding
#         assert (parsed[0].last_change - fixed_time) < timedelta(seconds=2)

#         # Last change happened around 5 minutes before 'now'
#         # TODO: Figure out why this does work locally but "sometimes" not on staging (fails in 50% of tries)
#         # assert (current_time - parsed[0].last_change) > timedelta(minutes=4)


# def test_update_semantic_documents_api_success(
#     vz_authenticated_client, vz_account, document_categories, set_vz_JWK
# ):
#     external_dossier_id = str(uuid.uuid4())

#     # Create a dossier
#     assert (
#         vz_authenticated_client.post(
#             path=reverse("vz-api:create-dossier"),
#             data=schemas.CreateDossier(
#                 name="Test Dossier",
#                 external_dossier_id=external_dossier_id,
#                 lang="fr",
#             ).model_dump_json(),
#             content_type="application/json",
#         ).status_code
#         == 201
#     )

#     dossier = Dossier.objects.get(external_id=external_dossier_id)

#     semantic_documents = add_some_fake_semantic_documents(
#         dossier=dossier, allow_empty_docs=False
#     )

#     semantic_document = semantic_documents[0]

#     assert semantic_document.access_mode.name == "READ_WRITE"

#     assert semantic_document.external_semantic_document_id is None

#     response = vz_authenticated_client.patch(
#         path=reverse(
#             "vz-api:update-semantic-document",
#             kwargs={
#                 "external_dossier_id": external_dossier_id,
#                 "semantic_document_uuid": semantic_document.uuid,
#             },
#         ),
#         data=schemas.SemanticDocumentUpdate(
#             external_semantic_document_id="Test external document",
#             access_mode="read_only",
#         ).model_dump_json(),
#         content_type="application/json",
#     )

#     assert response.status_code == 200

#     parsed = schemas.SemanticDocument.model_validate_json(response.content)

#     assert parsed.access_mode == "read_only"
#     assert parsed.external_semantic_document_id == "Test external document"


# def test_get_semantic_documents_api_failure(
#     vz_authenticated_client, vz_account, document_categories, set_vz_JWK
# ):
#     # Test the case where the dossier does not exist
#     response = vz_authenticated_client.get(
#         path=reverse(
#             "vz-api:semantic-documents",
#             kwargs={"external_dossier_id": str(uuid.uuid4())},
#         )
#     )

#     assert response.status_code == 404

#     external_dossier_id = str(uuid.uuid4())

#     # Create a dossier, but don't add any semantic documents
#     assert (
#         vz_authenticated_client.post(
#             path=reverse("vz-api:create-dossier"),
#             data=schemas.CreateDossier(
#                 name="Test Dossier",
#                 external_dossier_id=external_dossier_id,
#                 lang="fr",
#             ).model_dump_json(),
#             content_type="application/json",
#         ).status_code
#         == 201
#     )

#     response = vz_authenticated_client.get(
#         path=f"{reverse('vz-api:semantic-documents', kwargs={'external_dossier_id': external_dossier_id})}?show_pages=true",
#     )

#     assert response.status_code == 200
#     assert response.json() == []


# def test_get_document_categories_api_success(
#     vz_authenticated_client, vz_account, document_categories, set_vz_JWK
# ):
#     response = vz_authenticated_client.get(
#         path=reverse("vz-api:document-categories"),
#     )

#     assert response.status_code == 200

#     # Check we can parse the response
#     schemas.DocumentCategories.model_validate_json(response.content)

#     for document_category in document_categories:
#         if document_category.account.key == "vz":
#             assert document_category.name in response.json().keys()


# def test_get_document_categories_api_empty(
#     vz_authenticated_client, vz_account, set_vz_JWK
# ):
#     # Test the case where there are no document categories
#     DocumentCategory.objects.all().delete()
#     response = vz_authenticated_client.get(
#         path=reverse("vz-api:document-categories"),
#     )

#     assert response.status_code == 200

#     assert response.json() == {}


def test_export_dossier_semantic_document_pdf_success(
    mocked_get_dossier,
    vz_authenticated_client,
    vz_account,
    document_categories,
    set_vz_JWK,
    mocker: MockerFixture,
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        vz_authenticated_client.post(
            path=reverse("vz-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                lang="fr",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]

    def mock_publish_side_effect(*args, **kwargs):
        request = SemanticDocumentPDFRequestV1.model_validate_json(kwargs["message"])

        process_semantic_dossier_pdf_request(semantic_document_pdf_request=request)

    mock_dispatch_publish_request = mocker.patch(
        "vz.api.publish",
        side_effect=mock_publish_side_effect,
    )

    # Request to generate an export
    response = vz_authenticated_client.post(
        path=reverse(
            "vz-api:export-dossier-semantic-document-pdf",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    assert response.status_code == 200

    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=schemas.SemanticDocumentPDFExportRequest.model_validate_json(
            response.content
        ).uuid
    )

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    # Check we can poll for dossier status
    response = vz_authenticated_client.get(
        path=reverse(
            "vz-api:dossier-semantic-document-export-status",
            kwargs={
                "semantic_document_export_request_uuid": str(
                    semantic_document_export.uuid
                )
            },
        ),
    )

    assert response.status_code == 200

    parse = schemas.ExportStatus.model_validate_json(response.content)
    assert parse.semantic_document_export_request_uuid == semantic_document_export.uuid
    assert parse.status == "PROCESSING"
    # These are set to none as export_semantic_document.done is None
    assert parse.dossier_url is None
    assert parse.dossier_file_uuid is None


def test_export_dossier_semantic_document_pdf_auth_failure(
    mocked_get_dossier,
    vz_authenticated_client,
    vz_miss_signed_authenticated_client,
    vz_account,
    document_categories,
    set_vz_JWK,
    mocker: MockerFixture,
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        vz_authenticated_client.post(
            path=reverse("vz-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                lang="fr",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]

    def mock_publish_side_effect(*args, **kwargs):
        request = SemanticDocumentPDFRequestV1.model_validate_json(kwargs["message"])

        process_semantic_dossier_pdf_request(semantic_document_pdf_request=request)

    mock_dispatch_publish_request = mocker.patch(
        "vz.api.publish",
        side_effect=mock_publish_side_effect,
    )

    # Request to generate an export
    response = vz_miss_signed_authenticated_client.post(
        path=reverse(
            "vz-api:export-dossier-semantic-document-pdf",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 401
    mock_dispatch_publish_request.assert_not_called()


def test_dossier_end_to_end(
    mocked_get_dossier,
    vz_authenticated_client,
    vz_account,
    document_categories,
    mocker: MockerFixture,
    set_vz_JWK,
):
    # Test the whole process of creating a dossier, adding documents, exporting and checking status
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    vz_authenticated_client.post(
        path=reverse("vz-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, lang="fr"
        ).model_dump_json(),
        content_type="application/json",
    )

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    process_original_file_mock = mocker.patch(
        "dossier.services_external.process_original_file"
    )
    response = vz_authenticated_client.post(
        reverse(
            "vz-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == 201
    original_file = dossier_schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]

    def mock_publish_side_effect(*args, **kwargs):
        request = SemanticDocumentPDFRequestV1.model_validate_json(kwargs["message"])

        process_semantic_dossier_pdf_request(semantic_document_pdf_request=request)

    mock_dispatch_publish_request = mocker.patch(
        "vz.api.publish",
        side_effect=mock_publish_side_effect,
    )

    response = vz_authenticated_client.post(
        path=reverse(
            "vz-api:export-dossier-semantic-document-pdf",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    mock_dispatch_publish_request.assert_called_once()

    assert response.status_code == 200

    assert SemanticDocumentExport.objects.get(
        uuid=schemas.SemanticDocumentPDFExportRequest.model_validate_json(
            response.content
        ).uuid
    )


@pytest.mark.parametrize(
    ("token_overwrite", "expected"),
    [
        ({}, 201),  # Base case
        ({"user_roles": ""}, 401),  # No user roles
        ({"account_key": "wrong"}, 401),  # Wrong account key
        ({"exp": 1}, 401),  # Expired token
        ({"aud": ""}, 401),  # Aud not set
    ],
)
def test_create_dossier_api_authentication(
    token_overwrite,
    expected,
    vz_account,
    mock_jwks_public_private,
    token_data,
    set_vz_JWK,
):
    # Test various cases for JWT authentication
    url = reverse("vz-api:create-dossier")

    external_dossier_id = str(uuid.uuid4())

    dossier_data = schemas.CreateDossier(
        name="Test Dossier", external_dossier_id=external_dossier_id, lang="fr"
    ).model_dump_json()

    # Use the token_overwrite to overwrite certain fields in the token to ensure JWT authentication is working
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][1]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    authenticated_client = AuthenticatedClient(
        jwt.encode(
            {
                "aud": "account",
                "user_roles": [settings.API_ROLE],
                **token_data,
                **token_overwrite,
            },
            key=pem,
            algorithm="RS256",
        )
    )

    result = authenticated_client.post(
        path=url,
        data=dossier_data,
        content_type="application/json",
    )

    assert result.status_code == expected
