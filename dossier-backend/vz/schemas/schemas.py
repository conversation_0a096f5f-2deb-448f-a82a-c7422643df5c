from enum import Enum
from typing import List, Dict, Optional, Annotated
from uuid import UUID

from dateutil import tz
from datetime import datetime
from pydantic import Field, AnyHttpUrl, BaseModel, StringConstraints, RootModel

from core.generics import AnyHttpUrlStr
from dossier.schemas import Language, EntityTypes, AccessMode
from semantic_document.schemas import Confidence

ExternalDossierID = Annotated[
    str, StringConstraints(max_length=255, pattern="[A-Za-z0-9-]{1,255}")
]
PrincipalID = Annotated[str, StringConstraints(max_length=255)]
DossierName = Annotated[str, StringConstraints(max_length=255)]


class AccountName(str, Enum):
    # These strings are used as account keys
    vz = "vz"
    vztest = "vztest"


class CreateDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: DossierName
    lang: Language


class ExportDossierExport(BaseModel):
    external_dossier_id: ExternalDossierID


class SemanticPage(BaseModel):
    uuid: UUID
    number: int = Field(
        description="Page number is zero based. First page has page number 0"
    )
    image_url: Optional[str] = None

    updated_at: datetime
    last_change: datetime = datetime.now(tz=tz.tzutc())


class SemanticDocument(BaseModel):
    uuid: UUID

    external_semantic_document_id: Optional[
        Annotated[str, StringConstraints(max_length=255)]
    ] = None

    title: str

    document_category_confidence: Confidence

    document_category_key: str

    semantic_pages: List[SemanticPage]

    entity_type: Optional[EntityTypes] = None
    entity_key: Optional[str] = None

    access_mode: AccessMode = AccessMode.READ_WRITE

    updated_at: datetime
    last_change: datetime = datetime.now(tz=tz.tzutc())


class Dossier(BaseModel):
    uuid: UUID
    external_dossier_id: ExternalDossierID

    updated_at: datetime
    created_at: datetime


class ExportProcessingStatus(str, Enum):
    PROCESSING = "PROCESSING"
    ERROR = "ERROR"
    PROCESSED = "PROCESSED"


class ExportStatus(BaseModel):
    semantic_document_export_request_uuid: UUID
    status: ExportProcessingStatus
    # We use AnyHttpUrl instead of HttpUrl as CI uses ports as part of the URL
    dossier_url: Optional[AnyHttpUrlStr] = None
    dossier_file_uuid: Optional[UUID] = None

    updated_at: Optional[datetime] = None


class SemanticDocumentPDFExportRequest(BaseModel):
    uuid: UUID


class DocumentCategory(BaseModel):
    key: str
    id: str
    title_de: str
    description_de: Optional[str] = None


DocumentCategories = RootModel[Dict[str, DocumentCategory]]


class DossierAccessRequest(BaseModel):
    principalID: PrincipalID
    external_dossier_id: ExternalDossierID


class DossierAuthorization(BaseModel):
    principalID: PrincipalID
    external_dossier_id: ExternalDossierID


class FileStatus(str, Enum):
    PROCESSING = "processing"
    ERROR = "error"
    PROCESSED = "processed"


class ExtractedFile(BaseModel):
    uuid: UUID
    path_from_original: str
    file_name: str
    status: FileStatus
    file_url: AnyHttpUrl
    created_at: datetime
    updated_at: datetime


class OriginalFile(BaseModel):
    uuid: UUID
    name: str
    status: FileStatus
    extracted_files: List[ExtractedFile]
    file_url: AnyHttpUrl
    created_at: datetime
    updated_at: datetime


class DossierProcessingStatus(BaseModel):
    dossier_uuid: UUID
    external_id: str
    progress: int
    original_files: List[OriginalFile]


class ChangeDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: Optional[DossierName] = None
    lang: Optional[Language] = None


class SemanticDocumentUpdate(BaseModel):
    external_semantic_document_id: Optional[
        Annotated[str, StringConstraints(max_length=255)]
    ] = None
    access_mode: Optional[AccessMode] = None


# BBox, PageObjectTitles and PageObjectFullApiData are duplicates of
# dossier schemas. We can't change ZKB API schemas without a contractual agreement
# hence duplicate them here, to make sure a change in dossier/schemas does not accidentally
# break one of theirs


class BBox(BaseModel):
    ref_width: int
    ref_height: int
    top: int
    left: int
    right: int
    bottom: int


class MultilingualTitles(BaseModel):
    de: str
    en: str
    fr: str
    it: str
