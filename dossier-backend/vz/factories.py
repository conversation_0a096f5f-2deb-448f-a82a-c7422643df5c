from typing import <PERSON><PERSON>, <PERSON><PERSON>, List

import structlog

from assets import ASSETS_PATH
from dossier.factories import (
    set_new_account_defaults,
    DefaultAccountFactoryFaker,
)
from dossier.fakes import load_initial_document_categories
from dossier.models import Account, NavigationStrategy, DocumentCategory
from vz.schemas import schemas

logger = structlog.get_logger()


def update_or_create_vz_account(account_key: str) -> Account:
    assert is_valid_account_key(
        account_key
    ), f"Invalid account key: '{account_key}'. Valid keys are {[a.value for a in schemas.AccountName]}"

    account, _ = Account.objects.update_or_create(key=account_key)

    if account_key == schemas.AccountName.vz.value:
        account.name = "VZ Production"
        account.default_bucket_name = "production-v2-vz-dms"
        account.dmf_endpoint = "https://vz.hypodossier.ch"
    elif account_key == schemas.AccountName.vztest.value:
        account.name = "VZ Test"
        account.default_bucket_name = "production-v2-test-vz"
        account.dmf_endpoint = "https://vz.test.hypodossier.ch"
    else:
        raise ValueError(f"Invalid account key '{account_key}'")

    set_new_account_defaults(account)

    account.navigation_strategy = NavigationStrategy.DEFAULT
    account.enable_uploading_files = True
    account.enable_document_upload = True
    account.enable_feedback_form = False
    account.allow_dossier_listing = True

    # Standard config for vz
    account.max_dossier_expiry_duration_days = 500

    # To allow 20 days for final archiving process
    account.default_dossier_expiry_duration_days = 520
    account.valid_dossier_languages = ["De", "En"]
    account.valid_ui_languages = ["de", "en"]
    account.show_document_category_external = False
    account.show_business_case_type = False
    account.enable_virus_scan = False
    account.enable_rendering_hurdles_tab = False
    account.enable_dossier_permission = False
    account.enable_dossier_assignment = False

    # Download will most likely happen via API. That will provide the document_category.
    # No need to put it into the attachment for now
    account.enable_download_metadata_json = False
    account.enable_semantic_document_export = False

    return account


# Function to validate if the string is part of the enum
def is_valid_account_key(account_key: str) -> bool:
    try:
        # Attempt to match the account_key with an enum member
        schemas.AccountName(account_key)
        return True
    except ValueError:
        # If account_key is not found in the enum, return False
        return False


class VZAccountFactoryFaker(DefaultAccountFactoryFaker):
    def __init__(
        self,
        account: Optional[Account] = None,
        account_key: Optional[str] = None,
    ):
        if account is None:
            if account_key is None:
                account_key = schemas.AccountName.vz.value
        else:
            account_key = account.key

        account = update_or_create_vz_account(account_key)

        super().__init__(account, "de_CH")


def load_vz_document_categories(account_key) -> Tuple[bool, List, List]:

    # This is the latest doc cat definition that is verified for vz
    document_categories_json_path = (
        ASSETS_PATH / "document_category/default/DocumentCategory-2025-03-10.json"
    )
    account = Account.objects.get(key=account_key)
    load_initial_document_categories(
        account, document_categories_json_path=document_categories_json_path
    )

    document_categories_json_path_custom = (
        ASSETS_PATH / "document_category/vz/DocumentCategory-2025-01-27.json"
    )
    document_categories_custom, _, _, _ = load_initial_document_categories(
        account, document_categories_json_path_custom
    )

    doccats = list(DocumentCategory.objects.filter(account=account).all())

    # 250129: 264 default + 1 custom (GREY_CASE)
    assert (
        len(doccats) == 265
    ), f"Expected 264 doccats (default + custom), got {len(doccats)}"

    logger.info(f"In total {len(doccats)} document categories were loaded")
