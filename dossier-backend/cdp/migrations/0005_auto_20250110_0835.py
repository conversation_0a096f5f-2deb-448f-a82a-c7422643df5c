# Generated by Django 4.2.17 on 2025-01-10 07:35
from django.db import migrations


def migrate_priority_mapping_to_generic(apps, schema_editor):
    # Get the models from the migration state
    oldPriorityMapping = apps.get_model("cdp", "PriorityMapping")
    NewGenericPriorityMapping = apps.get_model("cdp", "GenericPriorityMapping")
    RelevantPageObject = apps.get_model("cdp", "RelevantPageObject")
    ContentType = apps.get_model("contenttypes", "ContentType")

    # Get the ContentType for RelevantPageObject
    relevant_pageobject_ct = ContentType.objects.get_for_model(RelevantPageObject)

    # Migrate data from PriorityMapping to GenericPriorityMapping
    for old_mapping in oldPriorityMapping.objects.all():
        try:
            if old_mapping.relevant_pageobject:
                print(f"Migrating: {old_mapping.uuid}")
                NewGenericPriorityMapping.objects.create(
                    uuid=old_mapping.uuid,
                    document_category=old_mapping.document_category,
                    priority=old_mapping.priority,
                    content_type=relevant_pageobject_ct,
                    object_id=old_mapping.relevant_pageobject.uuid,
                    created_at=old_mapping.created_at,
                    updated_at=old_mapping.updated_at,
                )
        except Exception as e:
            print(f"Error migrating {old_mapping.uuid}: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ("cdp", "0004_alter_prioritymapping_options_and_more"),
    ]

    operations = [migrations.RunPython(migrate_priority_mapping_to_generic)]
