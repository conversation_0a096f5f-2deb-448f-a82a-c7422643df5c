# Generated by Django 4.2.17 on 2025-01-09 08:20

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("cdp", "0003_alter_assignedfield_unique_together_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="prioritymapping",
            options={"ordering": ["priority"]},
        ),
        migrations.AlterField(
            model_name="prioritymapping",
            name="priority",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.CreateModel(
            name="RelevantSemanticPage",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "relevant_object_type",
                    models.Char<PERSON>ield(
                        choices=[("SEMANTIC_PAGE", "Semantic Page")], max_length=255
                    ),
                ),
                (
                    "field_definition",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cdp.fielddefinition",
                    ),
                ),
            ],
            options={
                "unique_together": {("field_definition", "relevant_object_type")},
            },
        ),
        migrations.CreateModel(
            name="RelevantSemanticDocument",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "relevant_object_type",
                    models.CharField(
                        choices=[("SEMANTIC_DOCUMENT", "Semantic Document")],
                        max_length=255,
                    ),
                ),
                (
                    "field_definition",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cdp.fielddefinition",
                    ),
                ),
            ],
            options={
                "unique_together": {("field_definition", "relevant_object_type")},
            },
        ),
        migrations.CreateModel(
            name="GenericPriorityMapping",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("priority", models.PositiveIntegerField(default=0)),
                ("object_id", models.UUIDField()),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "document_category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cdp.documentcategory",
                    ),
                ),
            ],
            options={
                "ordering": ["priority"],
                "unique_together": {("document_category", "content_type", "object_id")},
            },
        ),
    ]
