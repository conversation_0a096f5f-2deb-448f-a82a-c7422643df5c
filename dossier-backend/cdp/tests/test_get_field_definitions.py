from typing import List

import pytest
from django.urls import reverse

from cdp.models import FieldSet, AssignedField, FieldDefinition
from cdp.schemas import FieldDefinitionResponse
from cdp.services import get_field_definitions_for_field_set
from core.authentication import AuthenticatedClient


@pytest.fixture
def field_set_data():
    field_set = FieldSet.objects.create(key="Test Field Set")
    field_definition1 = FieldDefinition.objects.create(key="key1", flavour="flavour1")
    field_definition2 = FieldDefinition.objects.create(key="key2", flavour="flavour2")
    AssignedField.objects.create(
        field_set=field_set, field_definition=field_definition1
    )
    AssignedField.objects.create(
        field_set=field_set, field_definition=field_definition2
    )
    return field_set


@pytest.mark.django_db
def test_returns_field_definitions_for_valid_field_set(field_set_data):
    field_set = field_set_data
    result = get_field_definitions_for_field_set(field_set)
    assert len(result) == 2


@pytest.mark.django_db
def test_returns_empty_list_for_field_set_with_no_definitions():
    field_set = FieldSet.objects.create(key="Empty Field Set")
    result = get_field_definitions_for_field_set(field_set)
    assert result == []


@pytest.mark.django_db
def test_handles_field_set_with_single_definition():
    field_set = FieldSet.objects.create(key="Single Definition Field Set")
    field_definition = FieldDefinition.objects.create(
        key="single_key", flavour="single_flavour"
    )
    AssignedField.objects.create(field_set=field_set, field_definition=field_definition)
    result = get_field_definitions_for_field_set(field_set)
    assert isinstance(result, List)
    assert len(result) == 1
    assert isinstance(result[0], FieldDefinitionResponse)
    assert result[0].field_definition_key == "single_key"
    assert result[0].field_definition_flavour == "single_flavour"
    assert result[0].field_definition_return_type == "string"


@pytest.mark.django_db
def test_get_field_definition_api_returns_200(
    cdp_client: AuthenticatedClient,
    dossier_access_token,
    dossier_for_default_account,
    field_set_data,
):
    response = cdp_client.get(reverse("cdp-api:field_definitions-get"))
    assert response.status_code == 200
    assert response.json() == []


@pytest.mark.django_db
def test_get_field_definition_api_returns_field_definitions(
    cdp_client: AuthenticatedClient,
    dossier_access_token,
    dossier_for_default_account,
    field_set_data,
):
    field_definition1, _ = FieldDefinition.objects.get_or_create(key="key1")
    field_definition2, _ = FieldDefinition.objects.get_or_create(key="key2")
    field_set, _ = FieldSet.objects.get_or_create(key="DefaultFieldSet")
    AssignedField.objects.create(
        field_set=field_set, field_definition=field_definition1
    )
    AssignedField.objects.create(
        field_set=field_set, field_definition=field_definition2
    )
    response = cdp_client.get(reverse("cdp-api:field_definitions-get"))
    assert response.status_code == 200
    assert response.json() == [
        FieldDefinitionResponse(
            field_definition_key="key1",
            field_definition_flavour="flavour1",
            field_definition_return_type="string",
        ).model_dump(),
        FieldDefinitionResponse(
            field_definition_key="key2",
            field_definition_flavour="flavour2",
            field_definition_return_type="string",
        ).model_dump(),
    ]
    print(response.json())


@pytest.mark.django_db
def test_response_401_when_no_token(testuser1_client: AuthenticatedClient):
    response = testuser1_client.get(reverse("cdp-api:field_definitions-get"))
    assert response.status_code == 401
