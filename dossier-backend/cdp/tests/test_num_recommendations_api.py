import pytest
from django.urls import reverse

from cdp.schemas import (
    NumRecommendationRequest,
    NumRecommendations,
)


@pytest.mark.django_db
def test_num_recommendations_post_valid_request(cdp_client, sample_dossier_cdp):
    num_recommendations_request = NumRecommendationRequest(
        field_definitions=["field_0", "field_1"],
    )
    num_recommendations_request_dict = num_recommendations_request.model_dump()
    num_recommendations_response = cdp_client.post(
        reverse("cdp-api:num-recommendations-post"),
        data=num_recommendations_request_dict,
        content_type="application/json",
    )

    assert num_recommendations_response.status_code == 200
    assert (
        num_recommendations_response.json()["field_0"]
        == NumRecommendations(
            total_recommendation_count=2,
            grouped_sppo_recommendation_count=2,
            hint_recommendation_count=0,
            is_single_recommendation=False,
            single_recommendation=None,
        ).model_dump()
    )
    assert (
        num_recommendations_response.json()["field_1"]
        == NumRecommendations(
            total_recommendation_count=3,
            grouped_sppo_recommendation_count=3,
            hint_recommendation_count=0,
            is_single_recommendation=False,
            single_recommendation=None,
        ).model_dump()
    )


@pytest.mark.django_db
def test_num_recommendations_post_valid_and_invalid_request(
    cdp_client, sample_dossier_cdp
):
    num_recommendations_request = NumRecommendationRequest(
        field_definitions=["field_0", "field_2"],
    )
    num_recommendations_request_dict = num_recommendations_request.model_dump()
    num_recommendations_response = cdp_client.post(
        reverse("cdp-api:num-recommendations-post"),
        data=num_recommendations_request_dict,
        content_type="application/json",
    )

    assert num_recommendations_response.status_code == 200
    assert (
        num_recommendations_response.json()["field_0"]
        == NumRecommendations(
            total_recommendation_count=2,
            grouped_sppo_recommendation_count=2,
            hint_recommendation_count=0,
            is_single_recommendation=False,
            single_recommendation=None,
        ).model_dump()
    )
    assert (
        num_recommendations_response.json()["field_2"]
        == NumRecommendations(
            total_recommendation_count=0,
            grouped_sppo_recommendation_count=0,
            hint_recommendation_count=0,
            is_single_recommendation=False,
            single_recommendation=None,
        ).model_dump()
    )
    assert len(num_recommendations_response.json()) == 2


@pytest.mark.django_db
def test_num_recommendations_post_invalid_request(cdp_client, sample_dossier_cdp):
    num_recommendations_request = NumRecommendationRequest(
        field_definitions=["field_2"],
    )
    num_recommendations_request_dict = num_recommendations_request.model_dump()
    num_recommendations_response = cdp_client.post(
        reverse("cdp-api:num-recommendations-post"),
        data=num_recommendations_request_dict,
        content_type="application/json",
    )

    assert num_recommendations_response.status_code == 200
    assert len(num_recommendations_response.json()) == 1
    assert (
        num_recommendations_response.json()["field_2"]
        == NumRecommendations(
            total_recommendation_count=0,
            grouped_sppo_recommendation_count=0,
            hint_recommendation_count=0,
            is_single_recommendation=False,
            single_recommendation=None,
        ).model_dump()
    )


@pytest.mark.django_db
def test_num_recommendations_with_group_recommendations_enabled(
    cdp_client, sample_dossier_cdp
):
    num_recommendations_request = NumRecommendationRequest(
        field_definitions=["field_0", "field_1"],
    )
    num_recommendations_request_dict = num_recommendations_request.model_dump()
    num_recommendations_response = cdp_client.post(
        reverse("cdp-api:num-recommendations-post"),
        data=num_recommendations_request_dict,
        content_type="application/json",
    )

    assert num_recommendations_response.status_code == 200
    assert (
        num_recommendations_response.json()["field_0"]
        == NumRecommendations(
            total_recommendation_count=2,
            grouped_sppo_recommendation_count=2,
            hint_recommendation_count=0,
            is_single_recommendation=False,
            single_recommendation=None,
        ).model_dump()
    )
    assert (
        num_recommendations_response.json()["field_1"]
        == NumRecommendations(
            total_recommendation_count=3,
            grouped_sppo_recommendation_count=3,
            hint_recommendation_count=0,
            is_single_recommendation=False,
            single_recommendation=None,
        ).model_dump()
    )


@pytest.mark.django_db
def test_num_recommendations_with_hint_dossier(
    cdp_client, sample_dossier_cdp_with_hints
):
    num_recommendations_request = NumRecommendationRequest(
        field_definitions=["field_0", "field_1"],
    )
    num_recommendations_request_dict = num_recommendations_request.model_dump()
    num_recommendations_response = cdp_client.post(
        reverse("cdp-api:num-recommendations-post"),
        data=num_recommendations_request_dict,
        content_type="application/json",
    )
    assert num_recommendations_response.status_code == 200
    assert (
        num_recommendations_response.json()["field_0"]
        == NumRecommendations(
            total_recommendation_count=4,
            grouped_sppo_recommendation_count=4,
            hint_recommendation_count=4,
            is_single_recommendation=False,
            single_recommendation=None,
            field_context={},
        ).model_dump()
    )
    assert (
        num_recommendations_response.json()["field_1"]
        == NumRecommendations(
            total_recommendation_count=6,
            grouped_sppo_recommendation_count=6,
            hint_recommendation_count=6,
            is_single_recommendation=False,
            single_recommendation=None,
            field_context={},
        ).model_dump()
    )
