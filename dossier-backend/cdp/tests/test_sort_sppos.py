import pytest
from cdp.services import sort_sppos
from processed_file.models import PageObject, PageObjectTitle
from semantic_document.models import SemanticPagePageObject


@pytest.mark.django_db
def test_sort_sppos(monkeypatch):
    sppos = [
        SemanticPagePageObject(
            page_object=PageObject(
                key=PageObjectTitle(key="low_priority"), confidence_value=0.5
            ),
        ),
        SemanticPagePageObject(
            page_object=PageObject(
                key=PageObjectTitle(key="medium_priority"), confidence_value=0.94
            ),
        ),
        SemanticPagePageObject(
            page_object=PageObject(
                key=PageObjectTitle(key="high_priority"), confidence_value=0.9
            ),
        ),
        SemanticPagePageObject(
            page_object=PageObject(
                key=PageObjectTitle(key="high_priority"), confidence_value=0.93
            ),
        ),
    ]

    # Mock the get_priority_mapping function to return specific priorities
    def mock_get_priority_mapping(sppo_object):
        sppo_key = sppo_object.page_object.key.key
        mock_priority_mapping = {
            "low_priority": 3,
            "medium_priority": 2,
            "high_priority": 1,
        }
        return mock_priority_mapping.get(sppo_key, 999)

    def mock_get_sort_key(sppo_object, field_definition_key, field_definition_flavour):
        return (
            mock_get_priority_mapping(sppo_object),
            (-1)
            * sppo_object.page_object.confidence_value,  # Sort in descending order of confidence_value
        )

    # Patch the get_sort_key to use the mock get_priority_mapping
    with monkeypatch.context() as m:
        m.setattr("cdp.services.get_priority_mapping", mock_get_priority_mapping)
        m.setattr("cdp.services.get_sort_key", mock_get_sort_key)
        sorted_sppos = sort_sppos(sppos, "some_field_key", "some_flavour")

    print(sorted_sppos)
    # Check if the objects are sorted as expected
    assert sorted_sppos[0].page_object.key.key == "high_priority"
    assert sorted_sppos[0].page_object.confidence_value == 0.93

    assert sorted_sppos[1].page_object.key.key == "high_priority"
    assert sorted_sppos[1].page_object.confidence_value == 0.9

    assert sorted_sppos[2].page_object.key.key == "medium_priority"
    assert sorted_sppos[2].page_object.confidence_value == 0.94
