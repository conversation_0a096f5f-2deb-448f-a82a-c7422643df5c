import json
import uuid
from collections import defaultdict
from functools import lru_cache
from typing import List, Dict

import structlog
from dateutil.parser import parse
from django.contrib.contenttypes.models import ContentType
from jwcrypto.jwt import JWT
from ninja.errors import HttpError
from django.core.cache import caches

from cdp.field_context import (
    FieldContextHandler,
    FieldCtxInRecommendationSourceStrategy,
    CDP_FIELD_CONTEXT_STRATEGIES,
)
from cdp.models import (
    FieldSet,
    AssignedField,
    PageObjectType,
    DocumentCategory,
    RelevantSemanticPage,
    RelevantSemanticDocument,
    GenericPriorityMapping,
    RelevantFieldContext,
)
from cdp.schemas import (
    PageObjectTitle,
    BoundingBox,
    RecommendationSource,
    Confidence,
    RecommendationResponse,
    FieldDefinitionResponse,
    NumRecommendations,
    GroupedRecommendationResponse,
    FieldValue,
    HintRecommendationResponse,
    Hint,
    SemanticPageImageData,
    UnifiedRecommendationResponse,
    UnifiedRecommendationSource,
    SourceDocumentDetails,
    SourceSemanticPageDetails,
    PageObjectDetails,
    NumRecommendationRequest,
)
from cdp.value_parser import parse_value
from dossier.models import Dossier
from processed_file.models import PageObject
from django.conf import settings
from semantic_document.models import (
    SemanticPagePageObject,
    SemanticPage,
    SemanticDocument,
)
from cdp.models import RelevantPageObject, FieldDefinition

logger = structlog.get_logger()


def get_implicitly_assigned_document_categories(page_object_keys, field_key):
    matching_rpos = RelevantPageObject.objects.filter(
        page_object__key__in=page_object_keys,
        field_definition__key=field_key,
    ).values_list("uuid", flat=True)

    rpo_content_type = ContentType.objects.get_for_model(RelevantPageObject)
    implicitly_assigned_document_categories = (
        DocumentCategory.objects.filter(
            genericprioritymapping__content_type=rpo_content_type,
            genericprioritymapping__object_id__in=matching_rpos,
        )
        .distinct()  # in case multiple GPM rows reference the same category
        .values("key")
    )
    return implicitly_assigned_document_categories


def get_semantic_page_page_objects_with_key(
    dossier_uuid: uuid.UUID, page_object_keys: List[str], field_definition_key: str
) -> List[SemanticPagePageObject]:
    implicitly_assigned_document_categories = (
        get_implicitly_assigned_document_categories(
            page_object_keys, field_definition_key
        )
    )
    semantic_page_page_objects = (
        SemanticPagePageObject.objects.select_related(
            "page_object",
            "page_object__key",
            "page_object__processed_page",
            "semantic_page",
            "semantic_page__dossier",
            "semantic_page__document_category",
            "semantic_page__semantic_document",
            "semantic_page__semantic_document__dossier",
            "semantic_page__semantic_document__document_category",
        )
        .prefetch_related("semantic_page__semantic_document__semantic_pages")
        .filter(
            semantic_page__deleted_at__isnull=True,
            semantic_page__semantic_document__deleted_at__isnull=True,
            semantic_page__dossier_id=dossier_uuid,
            semantic_page__document_category__name__in=implicitly_assigned_document_categories,
            page_object__key__key__in=page_object_keys,
        )
    )
    return list(semantic_page_page_objects)


def get_assigned_field_for_field_definition_key_in_field_set(
    field_definition_key: str, field_set: str
) -> bool | AssignedField:
    try:
        assigned_field = AssignedField.objects.get(
            field_definition__key=field_definition_key,
            field_set__key=field_set,
        )
        return assigned_field
    except AssignedField.DoesNotExist:
        logger.error(
            "Requested field definition does not exist on the field set",
            field_definition_key=field_definition_key,
            field_set=field_set,
        )
        return False
    except AssignedField.MultipleObjectsReturned:
        logger.error(
            "Requested field definition is not unique on the field set",
            num_assigned_fields=len(assigned_field),
            field_definition_key=field_definition_key,
            field_set=field_set,
        )
        return False


def get_page_object_types(
    assigned_field: AssignedField,
) -> List[PageObjectType]:
    relevant_page_objects = RelevantPageObject.objects.filter(
        field_definition=assigned_field.field_definition
    ).select_related("page_object")
    page_object_types = [obj.page_object for obj in relevant_page_objects]

    return page_object_types


@lru_cache
def get_priority_mapping(
    field_definition_key: str,
    field_definition_flavour: str | None,
    document_category: str | None,
    page_object_key: str | None,
) -> int:
    try:
        field_def = FieldDefinition.objects.get(
            key=field_definition_key,
            flavour=field_definition_flavour,
        )
    except FieldDefinition.DoesNotExist:
        logger.warning(
            "FieldDefinition not found; returning default priority=999",
            field_definition_key=field_definition_key,
            field_definition_flavour=field_definition_flavour,
        )
        return 999

        # Look up PageObjectType
    try:
        page_object_type = PageObjectType.objects.get(key=page_object_key)
    except PageObjectType.DoesNotExist:
        logger.warning(
            "PageObjectType not found; returning default priority=999",
            page_object_key=page_object_key,
        )
        return 999

        # Look up RelevantPageObject
    try:
        relevant_page_object = RelevantPageObject.objects.get(
            field_definition=field_def,
            page_object=page_object_type,
        )
    except RelevantPageObject.DoesNotExist:
        logger.warning(
            "RelevantPageObject not found; returning default priority=999",
            field_definition_key=field_definition_key,
            field_definition_flavour=field_definition_flavour,
            page_object_key=page_object_key,
        )
        return 999

        # Look up DocumentCategory if provided
    doc_cat_obj = None
    if document_category:
        try:
            doc_cat_obj = DocumentCategory.objects.get(key=document_category)
        except DocumentCategory.DoesNotExist:
            logger.warning(
                "DocumentCategory not found; returning default priority=999",
                document_category=document_category,
            )
            return 999

    # With the relevant_page_object in hand, filter GenericPriorityMapping:
    #    - content_type = CT for RelevantPageObject
    #    - object_id = relevant_page_object.uuid
    #    - (optional) document_category = doc_cat_obj
    ctype = ContentType.objects.get_for_model(RelevantPageObject)
    filter_kwargs = {
        "content_type": ctype,
        "object_id": relevant_page_object.uuid,
    }
    if doc_cat_obj:
        filter_kwargs["document_category"] = doc_cat_obj

    priority_mappings = GenericPriorityMapping.objects.filter(**filter_kwargs)

    count = priority_mappings.count()
    if count == 0:
        logger.warning(
            "Requested GenericPriorityMapping does not exist; returning default=999",
            field_definition_key=field_definition_key,
            field_definition_flavour=field_definition_flavour,
            document_category=document_category,
            page_object_key=page_object_key,
        )
        return 999
    elif count > 1:
        logger.warning(
            "Requested GenericPriorityMapping is not unique; returning default=999",
            field_definition_key=field_definition_key,
            field_definition_flavour=field_definition_flavour,
            document_category=document_category,
            page_object_key=page_object_key,
        )
        return 999

    return priority_mappings.first().priority


def get_sort_key(
    sppo_object: SemanticPagePageObject,
    field_definition_key: str,
    field_definition_flavour: str,
) -> tuple:
    priority_mapping = get_priority_mapping(
        field_definition_key,
        field_definition_flavour,
        sppo_object.semantic_page.document_category.name,
        sppo_object.page_object.key.key,
    )
    confidence_value = sppo_object.page_object.confidence_value

    return (
        priority_mapping,
        (-1) * confidence_value,
    )
    # sorting based on first : priority_mapping ; second : confidence_value
    # Sort in descending order of confidence_value


def sort_sppos(
    sppos: List[SemanticPagePageObject],
    field_definition_key: str,
    field_definition_flavour: str,
) -> List[SemanticPagePageObject]:
    """
    Sorts the list of SemanticPagePageObject instances based on priority mapping and confidence value.
    """
    return sorted(
        sppos,
        key=lambda x: get_sort_key(x, field_definition_key, field_definition_flavour),
    )


def get_recommended_sppos(
    sppos: List[SemanticPagePageObject],
    assigned_field: AssignedField,
) -> List[SemanticPagePageObject]:
    """
    Apply the confidence threshold and sort the sppos based on the priority mapping and confidence value
    """
    sppos = apply_confidence_threshold(sppos)

    # Filter out sppos with a default priority of 999 or no priority mapping defined
    filtered_sppos = [
        sppo
        for sppo in sppos
        if get_priority_mapping(
            assigned_field.field_definition.key,
            assigned_field.field_definition.flavour,
            sppo.semantic_page.document_category.name,
            sppo.page_object.key.key,
        )
        != 999
    ]

    can_parse_sppos = [
        sppo
        for sppo in filtered_sppos
        if parse_value(sppo.page_object.value, assigned_field)
    ]
    recommended_sppos = sort_sppos(
        can_parse_sppos,
        assigned_field.field_definition.key,
        assigned_field.field_definition.flavour,
    )
    return recommended_sppos


def prepare_recommendation_response(
    sppo_recommendations: List[SemanticPagePageObject],
    assigned_field: AssignedField,
):
    response = [
        RecommendationResponse(
            confidence=Confidence(
                confidence_value=sppo.page_object.confidence_value,
                confidence_level=sppo.page_object.confidence_level,
                confidence_formatted=sppo.page_object.confidence_formatted,
            ),
            source=prepare_recommendation_source(sppo),
            field_value=parse_value(sppo.page_object.value, assigned_field),
            priority=get_priority_mapping(
                assigned_field.field_definition.key,
                assigned_field.field_definition.flavour,
                sppo.semantic_page.document_category.name,
                sppo.page_object.key.key,
            ),
        )
        for sppo in sppo_recommendations
    ]
    return response


def get_page_images_for_sppo(sppo: SemanticPagePageObject):
    preferred_page_uuid = sppo.semantic_page.uuid
    semantic_document = sppo.semantic_page.semantic_document
    semantic_pages = (
        SemanticPage.objects.filter(semantic_document=semantic_document)
        .all()
        .order_by("number")
    )
    sem_page_data: List[SemanticPageImageData] = []
    page_index_preferred = None
    for semantic_page in semantic_pages:
        if semantic_page.uuid == preferred_page_uuid:
            page_index_preferred = len(sem_page_data)
        if semantic_page.processed_page.image:
            sem_page_data.append(
                SemanticPageImageData(
                    image_url=semantic_page.processed_page.image.fast_url,
                    semantic_page_uuid=semantic_page.uuid,
                    rotation_angle=semantic_page.rotation_angle,
                )
            )

    return sem_page_data, page_index_preferred


def prepare_recommendation_source(sppo: SemanticPagePageObject):
    language = sppo.semantic_page.semantic_document.dossier.lang.lower()
    page_images, page_index_preferred = get_page_images_for_sppo(sppo)
    return RecommendationSource(
        image_url=sppo.page_object.processed_page.image.fast_url,
        bbox=BoundingBox(
            ref_height=sppo.page_object.ref_height,
            ref_width=sppo.page_object.ref_width,
            top=sppo.page_object.top,
            left=sppo.page_object.left,
            right=sppo.page_object.right,
            bottom=sppo.page_object.bottom,
        ),
        page_object_uuid=sppo.uuid,
        semantic_page_uuid=sppo.semantic_page.uuid,
        processed_page_uuid=sppo.page_object.processed_page.uuid,
        semantic_document_uuid=sppo.semantic_page.semantic_document.uuid,
        semantic_document_title=sppo.semantic_page.semantic_document.title,
        semantic_document_suffix=sppo.semantic_page.semantic_document.title_suffix,
        dossier_uuid=sppo.page_object.processed_page.dossier.uuid,
        document_category=sppo.semantic_page.semantic_document.document_category.name,
        document_category_translated=sppo.semantic_page.semantic_document.document_category.translated(
            language
        ),
        semantic_page_rotation_angle=sppo.semantic_page.rotation_angle,
        page_object_title=PageObjectTitle(
            key=sppo.page_object.key.key,
            de=sppo.page_object.key.de,
            en=sppo.page_object.key.en,
            fr=sppo.page_object.key.fr,
            it=sppo.page_object.key.it,
        ),
        document_date=get_document_date(sppo),
        semantic_document_page_number=sppo.semantic_page.number,
        semantic_document_page_count=sppo.semantic_page.semantic_document.semantic_pages.count(),
        page_images=page_images,
        page_index_preferred=page_index_preferred,
    )


# @lru_cache
def get_document_date_from_semantic_document(sem_doc: SemanticDocument):
    # Fetch all relevant PageObjects in a single query
    page_objects = PageObject.objects.filter(
        processed_page__semantic_pages__semantic_document=sem_doc,
        key__key__in=["document_date", "document_validity_start_date"],
    ).select_related("processed_page", "key")

    # Process the fetched PageObjects
    for page_object in page_objects:
        if page_object.key.key == "document_date":
            parsed_date = parse_date(page_object.value)
            if parsed_date:
                return parsed_date
        elif page_object.key.key == "document_validity_start_date":
            print("document_validity_start_date", page_object.value)
            parsed_date = parse_date(page_object.value)
            if parsed_date:
                return parsed_date

    # If none of the semantic pages have the required dates, use the created_at date of the original document
    original_file = (
        sem_doc.semantic_pages.first().processed_page.processed_file.extracted_file.original_file
    )
    return original_file.created_at.date().isoformat()


def get_document_date(
    sppo: SemanticPagePageObject,
) -> str:
    """
    Get the document date for the recommendation out of all the possible dates for the semantic document
    Look for the following dates related to the sppo (SemanticPagePageObject):
    1. document_date
    2. document_validity_start_date
    3. If none of these are available, we use the created_at date of the original document
    """
    document = (
        sppo.semantic_page.semantic_document
    )  # semantic_document linked to the sppo if interest
    document_date = get_document_date_from_semantic_document(document)
    return document_date


def parse_date(date_str: str) -> str | None:
    """
    Parse the date from a string. Returns the date in ISO format : yyyy-mm-dd
    Returns None if the string cannot be parsed.
    """
    try:
        # Use dateutil parser to handle various date formats
        return parse(date_str).date().isoformat()
    except (ValueError, TypeError):
        return None  # Return None if the string is not a valid date


def get_return_type_for_assigned_field(assigned_field: AssignedField) -> str:
    return_type = (
        assigned_field.field_definition.return_type.key
        if assigned_field.field_definition.return_type
        else "string"
    )
    return return_type


def get_field_definitions_for_field_set(
    field_set: FieldSet,
) -> List[FieldDefinitionResponse]:
    assigned_field_for_given_field_set = AssignedField.objects.filter(
        field_set__key=field_set
    )

    if assigned_field_for_given_field_set:
        response = [
            FieldDefinitionResponse(
                field_definition_key=fd.field_definition.key,
                field_definition_flavour=fd.field_definition.flavour,
                field_definition_return_type=get_return_type_for_assigned_field(fd),
            )
            for fd in assigned_field_for_given_field_set
        ]
        return response
    return []


def parse_dossier_access_token(request):
    jwt: JWT = request.auth
    dossier_uuid = json.loads(jwt.claims).get("dossier_uuid")
    field_set = json.loads(jwt.claims).get("field_set")

    if dossier_uuid is None or field_set is None:
        raise HttpError(
            401,
            "Dossier UUID or Field Set not found in the JWT claims",
        )
    if not field_set:
        """
        If the field_set is not found in the JWT claims, return an error message 404 : Not Found
        """
        logger.error(
            "Field set not found in JWT claims",
        )
        raise HttpError(
            404,
            "Field set not found in JWT claims, please set the cdp_field_set for the Account",
        )
    return dossier_uuid, field_set


def get_num_recommendations(
    dossier_uuid, field_set, num_recommendation_request: NumRecommendationRequest
):
    num_recommendations_response = {}
    field_context = num_recommendation_request.field_context

    # if field_definitions is empty, return num_recommendations for all fields in the field_set
    if len(num_recommendation_request.field_definitions) > 0:
        field_definition_keys = num_recommendation_request.field_definitions
    else:
        field_definition_keys = list(
            AssignedField.objects.filter(
                field_set__key=field_set,
            )
            .all()
            .values_list("field_definition__key", flat=True)
        )

    # collect data for analytics
    analytics_payload = {"dossier_uuid": dossier_uuid, "missing_recommendations": []}
    for field_definition_key in field_definition_keys:
        try:
            rec_res = get_grouped_recommendations(
                dossier_uuid,
                field_set=field_set,
                field_definition_key=field_definition_key,
                field_context=field_context,
            )
        except Exception as e:
            logger.error(
                f"Error getting num recommendations for field {field_definition_key} \n"
                f"Error: {e}",
            )
            num_recommendations_response[field_definition_key] = NumRecommendations(
                total_recommendation_count=0,
                grouped_sppo_recommendation_count=0,
                hint_recommendation_count=0,
                is_single_recommendation=False,
                single_recommendation=None,
                field_context=field_context,
            )
            continue

        # to be included once the frontend logic for handling num_recommendations is also uses the same logic
        # total_recommendation_count = len(rec_res)
        grouped_sppo_recommendation_count = len(
            [rec for rec in rec_res if not rec.is_hint]
        )
        total_recommendation_count = 0
        for recommendation in rec_res:
            total_recommendation_count += len(
                recommendation.source.source_document_details
            )

        hint_recommendations_count = len([rec for rec in rec_res if rec.is_hint])
        recommendation_count = (
            grouped_sppo_recommendation_count + hint_recommendations_count
        )
        missing_recommendation = recommendation_count == 0
        if missing_recommendation:
            analytics_payload["missing_recommendations"].append(field_definition_key)

        is_single_recommendation = recommendation_count == 1
        # add value and document preview data for single value recommendation
        single_recommendation = None
        if is_single_recommendation:
            single_recommendation = rec_res[0]
        num_recommendations_response[field_definition_key] = NumRecommendations(
            total_recommendation_count=total_recommendation_count,
            grouped_sppo_recommendation_count=recommendation_count,
            hint_recommendation_count=hint_recommendations_count,
            is_single_recommendation=is_single_recommendation,
            single_recommendation=single_recommendation,
            field_context=field_context,
        )
    # calculate share of missing recommendations
    total_fields_requested = len(field_definition_keys)
    missing_recommendations = len(analytics_payload["missing_recommendations"])
    missing_recommendations_share = missing_recommendations / total_fields_requested
    analytics_payload["missing_recommendations_share"] = (
        f"{missing_recommendations_share:.4f}"
    )
    # log to analytics
    logger.info("[CDP] Num recommendations", **analytics_payload)
    return num_recommendations_response


def apply_confidence_threshold(sppos):
    """
    Filter out the SemanticPagePageObject instances that have a confidence value of < settings.CDP_CONFIDENCE_THRESHOLD
    """
    return [
        sppo
        for sppo in sppos
        if sppo.page_object.confidence_value >= settings.CDP_CONFIDENCE_THRESHOLD
    ]


def group_sppos(
    sppos: List[SemanticPagePageObject], assigned_field: AssignedField
) -> Dict[FieldValue, List[SemanticPagePageObject]]:
    """
    Get the number of unique recommendations (after appropriate parsing)
    """
    grouped_sppo_value_map = defaultdict(list)
    for sppo in sppos:
        parsed_sppo_value = parse_value(
            sppo.page_object.value,
            assigned_field=assigned_field,
            use_cached_parser=True,
        )
        grouped_sppo_value_map[parsed_sppo_value].append(sppo)
    return dict(grouped_sppo_value_map)


def sort_sppos_related_to_unique_value(
    unique_sppo_map: Dict[FieldValue, List[SemanticPagePageObject]],
    assigned_field: AssignedField,
) -> Dict[FieldValue, List[SemanticPagePageObject]]:
    """
    Sort the unique_sppo_map based on the priority mapping and confidence value
    """
    for unique_sppo_value, sppo_list in unique_sppo_map.items():
        unique_sppo_map[unique_sppo_value] = sort_sppos(
            sppo_list,
            assigned_field.field_definition.key,
            assigned_field.field_definition.flavour,
        )
    return unique_sppo_map


def prepare_src_doc_details(
    sppo_list: List[SemanticPagePageObject],
) -> List[SourceDocumentDetails]:
    src_doc_details = []
    for sppo in sppo_list:
        language = sppo.semantic_page.semantic_document.dossier.lang.lower()
        page_images, page_index_preferred = prepare_semantic_page_details(sppo)
        src_doc_details.append(
            SourceDocumentDetails(
                dossier_uuid=sppo.semantic_page.dossier.uuid,
                semantic_document_uuid=sppo.semantic_page.semantic_document.uuid,
                document_category=sppo.semantic_page.semantic_document.document_category.name,
                document_category_translated=sppo.semantic_page.semantic_document.document_category.translated(
                    language
                ),
                semantic_document_title=sppo.semantic_page.semantic_document.title,
                semantic_document_suffix=sppo.semantic_page.semantic_document.title_suffix,
                document_date=get_document_date(sppo),
                semantic_document_page_count=len(
                    sppo.semantic_page.semantic_document.semantic_pages.all()
                ),
                page_index_to_scroll=page_index_preferred,
                semantic_pages=page_images,
            )
        )
    return src_doc_details


def prepare_sp_page_object_details(
    sppo_list: List[SemanticPagePageObject],
) -> List[PageObjectDetails]:
    page_object_details = []
    for sppo in sppo_list:
        page_object_details.append(
            PageObjectDetails(
                page_object_uuid=sppo.uuid,
                page_object_title=PageObjectTitle(
                    key=sppo.page_object.key.key,
                    de=sppo.page_object.key.de,
                    en=sppo.page_object.key.en,
                    fr=sppo.page_object.key.fr,
                    it=sppo.page_object.key.it,
                ),
                confidence=Confidence(
                    confidence_value=sppo.page_object.confidence_value,
                    confidence_level=sppo.page_object.confidence_level,
                    confidence_formatted=sppo.page_object.confidence_formatted,
                ),
                bbox=BoundingBox(
                    ref_height=sppo.page_object.ref_height,
                    ref_width=sppo.page_object.ref_width,
                    top=sppo.page_object.top,
                    left=sppo.page_object.left,
                    right=sppo.page_object.right,
                    bottom=sppo.page_object.bottom,
                ),
            )
        )
    return page_object_details


def fetch_semantic_pages_for_document(sem_doc: SemanticDocument):
    """
    Returns a list of semantic pages for a given SemanticDocument, with all
    needed relationships eagerly loaded in a single query.
    The cache key includes the document's last_page_change_date to ensure
    changes to the document's pages invalidate the cache.
    """
    sem_doc.refresh_from_db()

    cache_key = f"semantic_pages_{sem_doc.uuid}_{sem_doc.last_page_change_date}"

    cached_result = caches["cdp-cache"].get(cache_key)
    if cached_result is not None:
        return cached_result

    result = list(
        SemanticPage.objects.filter(semantic_document=sem_doc)
        .select_related(
            "processed_page",
            "document_category",
            "processed_page__image",
            "processed_page__searchable_pdf",
            "processed_page__searchable_txt",
        )
        .order_by("number")
    )

    # Set cache with timeout
    caches["cdp-cache"].set(
        cache_key, result, settings.CDP_SEMANTIC_PAGES_CACHE_TIMEOUT
    )

    return result


def prepare_semantic_page_details(sppo: SemanticPagePageObject):
    """
    Prepare semantic page details with caching.
    The cache expires after 24 hours to match presigned URL validity period.
    """
    # Create cache key using sppo uuid and document's last page change date
    sem_doc = sppo.semantic_page.semantic_document
    sem_doc.refresh_from_db()
    cache_key = f"semantic_page_details_{sppo.uuid}_{sem_doc.last_page_change_date}"

    cached_result = caches["cdp-cache"].get(cache_key)
    if cached_result is not None:
        return cached_result

    preferred_page = sppo.semantic_page.uuid
    semantic_pages = fetch_semantic_pages_for_document(sem_doc)
    sem_page_data: List[SourceSemanticPageDetails] = []
    page_index_preferred = None

    for index, semantic_page in enumerate(semantic_pages):
        if semantic_page.uuid == preferred_page:
            page_index_preferred = index

        if semantic_page.processed_page.image:
            image_url = semantic_page.processed_page.image.fast_url
            searchable_pdf_url = (
                semantic_page.processed_page.searchable_pdf.fast_url
                if semantic_page.processed_page.searchable_pdf
                else None
            )
            searchable_txt_url = (
                semantic_page.processed_page.searchable_txt.fast_url
                if semantic_page.processed_page.searchable_txt
                else None
            )

            sem_page_data.append(
                SourceSemanticPageDetails(
                    semantic_page_uuid=semantic_page.uuid,
                    processed_page_uuid=semantic_page.processed_page.uuid,
                    document_category=semantic_page.document_category.name,
                    rotation_angle=semantic_page.rotation_angle,
                    image_url=image_url,
                    searchable_pdf_url=searchable_pdf_url,
                    searchable_txt_url=searchable_txt_url,
                    page_objects=prepare_sp_page_object_details([sppo]),
                )
            )

    result = (sem_page_data, page_index_preferred)

    # Set cache with timeout
    caches["cdp-cache"].set(
        cache_key,
        result,
        settings.CDP_SEMANTIC_PAGE_PAGE_OBJECT_DETAILS_CACHE_TIMEOUT,
    )

    return result


def prepare_grouped_recommendation_response(
    sorted_grouped_values, unique_sppo_map, assigned_field
) -> List[UnifiedRecommendationResponse]:
    response = []
    for unique_sppo_value in sorted_grouped_values:
        sppo_list = unique_sppo_map[unique_sppo_value]
        grouped_recommendation_response = UnifiedRecommendationResponse(
            field_value=unique_sppo_value,
            source=UnifiedRecommendationSource(
                source_document_details=prepare_src_doc_details(sppo_list),
                priority=min(
                    [
                        get_priority_mapping(
                            assigned_field.field_definition.key,
                            assigned_field.field_definition.flavour,
                            sppo.semantic_page.document_category.name,
                            sppo.page_object.key.key,
                        )
                        for sppo in sppo_list
                    ]
                ),
            ),
            is_hint=False,
        )
        response.append(grouped_recommendation_response)
    return response


def get_generic_priority_mappings_for_hint_sources(assigned_field: AssignedField):
    """
    Gather all GenericPriorityMapping objects linked via
    generic_priority_mapping from:
      - RelevantSemanticPage
      - RelevantSemanticDocument
    where each of those references assigned_field.field_definition.
    """
    field_definition = assigned_field.field_definition

    # Grab the relevant pages and documents for this FieldDefinition
    #    With .prefetch_related('generic_priority_mapping') we can load
    #    all GPM objects in a single pass for each set
    relevant_pages = RelevantSemanticPage.objects.filter(
        field_definition=field_definition
    ).prefetch_related(
        "generic_priority_mapping__document_category",
        "generic_priority_mapping__relevant_object",
    )

    relevant_docs = RelevantSemanticDocument.objects.filter(
        field_definition=field_definition
    ).prefetch_related(
        "generic_priority_mapping__document_category",
        "generic_priority_mapping__relevant_object",
    )

    gpms = []

    for page in relevant_pages:
        gpms.extend(list(page.generic_priority_mapping.all()))

    for doc in relevant_docs:
        gpms.extend(list(doc.generic_priority_mapping.all()))

    return gpms


def get_source_for_hint_recommendation(
    gpm, dossier: Dossier
) -> Dict[SemanticDocument, List[SourceSemanticPageDetails]]:
    """
    Return a dict of {SemanticDocument: [SourceSemanticPageDetails]}.
    """

    # This doc_map will store the mapping of "doc -> list of page details"
    doc_map: Dict[SemanticDocument, List[SourceSemanticPageDetails]] = {}

    # 1) If relevant object is a "semantic document"
    if (
        gpm.relevant_object.relevant_object_type
        == RelevantSemanticDocument.RELEVANT_SEMANTIC_DOCUMENT_CHOICES[0][0]
    ):
        sem_docs = (
            SemanticDocument.objects.filter(
                dossier=dossier,
                document_category__name=gpm.document_category,
            )
            .select_related("dossier", "document_category")
            .prefetch_related(
                "semantic_pages",
                "semantic_pages__processed_page",
                "semantic_pages__document_category",
                "semantic_pages__processed_page__image",
                "semantic_pages__processed_page__searchable_pdf",
                "semantic_pages__processed_page__searchable_txt",
            )
            .all()
        )

        for sem_doc in sem_docs:
            pages = sem_doc.semantic_pages.all()
            page_details: List[SourceSemanticPageDetails] = []
            for page in pages:
                if page.processed_page and page.processed_page.image:
                    page_details.append(
                        SourceSemanticPageDetails(
                            semantic_page_uuid=page.uuid,
                            processed_page_uuid=page.processed_page.uuid,
                            document_category=page.document_category.name,
                            image_url=page.processed_page.image.fast_url,
                            searchable_pdf_url=page.processed_page.searchable_pdf.fast_url,
                            searchable_txt_url=page.processed_page.searchable_txt.fast_url,
                            rotation_angle=page.rotation_angle,
                            page_objects=[],
                        )
                    )

            doc_map[sem_doc] = page_details

    # 2) If relevant object is a "semantic page"
    elif (
        gpm.relevant_object.relevant_object_type
        == RelevantSemanticPage.RELEVANT_SEMANTIC_PAGE_CHOICES[0][0]
    ):
        sem_pages = SemanticPage.objects.filter(
            dossier=dossier,
            document_category__name=gpm.document_category,
        ).select_related(
            "dossier",
            "semantic_document",
            "semantic_document__document_category",
            "processed_page",
            "processed_page__image",
            "processed_page__searchable_pdf",
            "processed_page__searchable_txt",
        )

        for page in sem_pages:
            doc = page.semantic_document
            if doc not in doc_map:
                doc_map[doc] = []

            # As above, check validity of processed_page & image
            if page.processed_page and page.processed_page.image:
                doc_map[doc].append(
                    SourceSemanticPageDetails(
                        semantic_page_uuid=page.uuid,
                        processed_page_uuid=page.processed_page.uuid,
                        document_category=page.document_category.name,
                        image_url=page.processed_page.image.fast_url,
                        searchable_pdf_url=page.processed_page.searchable_pdf.fast_url,
                        searchable_txt_url=page.processed_page.searchable_txt.fast_url,
                        rotation_angle=page.rotation_angle,
                        page_objects=[],
                    )
                )

    return doc_map


def prepare_hint_recommendation_response(generic_priority_mappings, dossier: Dossier):
    hint_recommendations = []

    for gpm in generic_priority_mappings:
        # We get back a dict of doc -> list of page details
        doc_map = get_source_for_hint_recommendation(gpm, dossier)

        # If it's empty, skip
        if not doc_map:
            continue

        # Build one or more recommendation responses
        for sem_doc, hint_source_list in doc_map.items():
            # Thanks to select_related('dossier'), we can access sem_doc.dossier without extra queries
            lang = sem_doc.dossier.lang.lower()

            # Possibly require prefetch if .translated() hits a related table
            doc_cat_t = sem_doc.document_category.translated(lang)

            # If semantic_pages was prefetched, len(sem_doc.semantic_pages.all()) will not cause extra queries
            page_count = len(sem_doc.semantic_pages.all())

            # Build your final recommendation
            hint_recommendations.append(
                UnifiedRecommendationResponse(
                    field_value=Hint(
                        return_type="hint",
                        expected_return_type=gpm.relevant_object.field_definition.return_type.key,
                    ),
                    is_hint=True,
                    source=UnifiedRecommendationSource(
                        source_document_details=[
                            SourceDocumentDetails(
                                dossier_uuid=dossier.uuid,
                                semantic_document_uuid=sem_doc.uuid,
                                document_category=sem_doc.document_category.name,
                                document_category_translated=doc_cat_t,
                                semantic_document_title=sem_doc.title,
                                semantic_document_suffix=sem_doc.title_suffix,
                                document_date=get_document_date_from_semantic_document(
                                    sem_doc
                                ),
                                semantic_document_page_count=page_count,
                                page_index_to_scroll=0,
                                # The pre-built list of page details for this doc
                                semantic_pages=hint_source_list,
                            )
                        ],
                        priority=gpm.priority,
                    ),
                )
            )

    return hint_recommendations


def get_hint_recommendations(assigned_field: AssignedField, dossier: Dossier):
    generic_priority_mappings = get_generic_priority_mappings_for_hint_sources(
        assigned_field
    )
    hint_recommendations_response = prepare_hint_recommendation_response(
        generic_priority_mappings, dossier
    )
    return hint_recommendations_response


def get_grouped_recommendations(
    dossier_uuid, field_set, field_definition_key, field_context
) -> List[UnifiedRecommendationResponse]:
    assigned_field = get_assigned_field_for_field_definition_key_in_field_set(
        field_definition_key, field_set
    )
    if not assigned_field:
        """
        If the field is not found in the field set, return an error message 404 : Not Found
        """
        logger.error(
            f"Field {field_definition_key} not found in field set {field_set}",
        )
        raise HttpError(
            404,
            f"Field {field_definition_key} not found in field set {field_set}",
        )
    page_object_types = get_page_object_types(
        assigned_field=assigned_field,
    )

    field_poks = [po.key for po in page_object_types]
    sppos = get_semantic_page_page_objects_with_key(
        dossier_uuid, field_poks, field_definition_key
    )

    sorted_sppos = get_recommended_sppos(sppos, assigned_field=assigned_field)
    unique_sppo_map = group_sppos(sorted_sppos, assigned_field)
    # since the sppos are already sorted based on the priority mapping and confidence value, they would be already in the grouped subset
    # unique_sppo_map = sort_sppos_related_to_unique_value(unique_sppo_map)

    sorted_grouped_values = sort_grouped_sppos(unique_sppo_map, sorted_sppos)

    dossier = Dossier.objects.filter(uuid=dossier_uuid).first()

    # next merge the list of hint_recommendations and sorted grouped values to get the final response

    grouped_page_object_recommendations = prepare_grouped_recommendation_response(
        sorted_grouped_values, unique_sppo_map, assigned_field
    )

    if not grouped_page_object_recommendations:
        hint_recommendations = get_hint_recommendations(assigned_field, dossier)
        combined_recommendations = hint_recommendations
    else:
        combined_recommendations = grouped_page_object_recommendations

    def get_priority(res):
        if isinstance(res, GroupedRecommendationResponse):
            return res.highest_priority
        elif isinstance(res, HintRecommendationResponse):
            return res.source.priority
        elif isinstance(res, UnifiedRecommendationResponse):
            return res.source.priority

        return float("inf")  # fallback priority

    combined_recommendations.sort(key=get_priority)
    rec_for_context = apply_field_context(
        combined_recommendations,
        field_context,
        field_definition=assigned_field.field_definition,
    )
    return rec_for_context


def apply_field_context(
    recommendations, field_context, field_definition: FieldDefinition
) -> List[UnifiedRecommendationResponse]:
    """
    Apply the field context to the recommendations
    """
    if not field_context:
        return recommendations

    relevant_fields_for_context = RelevantFieldContext.objects.filter(
        field_definition=field_definition
    ).all()
    # if we don't know the relevant fields for the context, we return the recommendations as is
    if not relevant_fields_for_context:
        return recommendations

    field_context_handler = FieldContextHandler(
        field_definition=field_definition,
    )

    field_context_strategy = CDP_FIELD_CONTEXT_STRATEGIES.model_dump().get(
        field_definition.field_context_strategy, FieldCtxInRecommendationSourceStrategy
    )()

    return field_context_handler.apply_field_context(
        recommendations, field_context, field_context_strategy
    )


def sort_grouped_sppos(
    unique_sppo_map: Dict[FieldValue, List[SemanticPagePageObject]], sorted_sppos
):
    """
    Create a sorted list of the unique_sppo_map keys based on the max_priority obtained from the group of sppos
    """
    grouped_unique_values = list(unique_sppo_map.keys())
    sorted_grouped_values = sorted(
        grouped_unique_values,
        key=lambda x: get_min_rank(unique_sppo_map[x], sorted_sppos),
    )
    return sorted_grouped_values


def get_min_rank(sppo_list, sorted_sppos):
    """
    Get the minimum rank of the sppos in the sorted sppo_list
    """
    return min([sorted_sppos.index(sppo) for sppo in sppo_list])
