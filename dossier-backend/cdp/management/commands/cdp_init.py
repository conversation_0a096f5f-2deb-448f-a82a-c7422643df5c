import djclick as click
from django.core.management import call_command


@click.command()
@click.option(
    "--truncate/--no-truncate",
    default=False,
    help="Truncate the tables in CDP before importing.",
)
def command(truncate):
    """
    Import all CDP data: page object types, document categories, and Field Set configuration.
    """

    if truncate:
        click.secho("Truncating CDP tables...", fg="blue")
        call_command("truncate_cdp")

    click.secho("Running management command import_cdp_page_object_keys...", fg="blue")
    call_command("import_cdp_page_object_keys")

    click.secho(
        "Running management command import_cdp_document_categories...", fg="blue"
    )
    call_command("import_cdp_document_categories")

    click.secho("Running management command : import_all_field_sets...", fg="blue")
    call_command("import_all_field_sets")

    click.secho(
        "Successfully imported CDP data: page object types, document categories, and Field Set configuration.",
        fg="green",
    )
