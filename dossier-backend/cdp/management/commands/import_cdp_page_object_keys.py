import json
from pathlib import Path
import djclick as click
from cdp.models import PageObjectType  # Assuming PageObjectType model exists


@click.command()
@click.option(
    "--file",
    type=click.Path(exists=False, readable=True),
    help="The path to the JSON file to import. If not provided, the latest file in the page_object_keys directory will be used.",
)
def command(file):
    """
    Import page object keys from a JSON file, defaulting to the latest file in the dossier-backend/cdp/management/assets/page_object_keys directory.
    """
    po_key_list = []
    page_object_keys_path = (
        Path(__file__).resolve().parent.parent / "assets" / "page_object_keys"
    )

    file_path = file

    if not file_path:
        # Get the latest JSON file from the directory
        json_files = list(
            page_object_keys_path.glob("*_Hypodossier_Page_Object_Keys.json")
        )
        if not json_files:
            click.secho(
                "No JSON files found in the page_object_keys directory.", fg="red"
            )
            return

        # Sort files by timestamp in the name (newest first)
        json_files.sort(key=lambda x: x.stem, reverse=True)
        file_path = json_files[0]

    # Open and load the JSON file
    click.secho(f"Importing page object keys from {file_path}", fg="green")
    try:
        with open(file_path) as json_file:
            data = json.load(json_file)
    except FileNotFoundError:
        click.secho(f"File {file_path} not found", fg="red")
        return
    except json.JSONDecodeError:
        click.secho("Invalid JSON format", fg="red")
        return

    # Iterate over the JSON data and create PageObjectType entries
    for obj_key in data:
        obj_key_value = obj_key["key"]

        # Get or create the PageObjectType based on the key
        page_object_type, created = PageObjectType.objects.get_or_create(
            key=obj_key_value
        )
        po_key_list.append(obj_key_value)

        if not created:
            click.secho(
                f"PageObjectType '{obj_key_value}' already exists.", fg="yellow"
            )

    click.secho(
        f"{len(po_key_list)} Page object keys imported successfully", fg="green"
    )
    click.echo(f"{po_key_list}")
