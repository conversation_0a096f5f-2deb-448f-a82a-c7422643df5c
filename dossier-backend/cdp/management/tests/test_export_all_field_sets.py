import json
import pytest
from django.core.management import call_command
from pathlib import Path
from django.utils import timezone

from cdp.models import (
    FieldSet,
    AssignedField,
    RelevantPageObject,
    PageObjectType,
    DocumentCategory,
    FieldDefinition,
    ReturnType,
)


@pytest.mark.django_db
def test_export_field_sets_data(tmp_path, mocker):
    """
    Test the Django management command to ensure that FieldSet data is exported correctly,
    including GenericPriorityMapping objects.
    """
    return_type = ReturnType.objects.create(
        key="return_type_key", description="Return Type Description"
    )
    field_set = FieldSet.objects.create(key="test_field_set")
    field_definition = FieldDefinition.objects.create(
        key="test_field_definition", flavour="test_flavour", return_type=return_type
    )
    AssignedField.objects.create(field_set=field_set, field_definition=field_definition)
    page_object = PageObjectType.objects.create(key="test_page_object_key")
    relevant_page_object = RelevantPageObject.objects.create(
        field_definition=field_definition, page_object=page_object
    )
    document_category = DocumentCategory.objects.create(key="test_document_category")

    # Create a GenericPriorityMapping entry referencing the RelevantPageObject
    #    using content_type & object_id or the GenericRelation:
    relevant_page_object.generic_priority_mapping.create(
        document_category=document_category, priority=1
    )

    export_dir = tmp_path.parent.parent / "assets" / "field_set_data"
    export_dir.mkdir(parents=True, exist_ok=True)

    timestamp = timezone.now().strftime("%Y%m%d")
    expected_file_path = export_dir / f"field_set_export_{timestamp}.json"

    mocker.patch(
        "cdp.management.commands.export_all_field_sets.Path",
        return_value=Path(export_dir),
    )
    call_command("export_all_field_sets")

    assert expected_file_path.exists(), "Exported JSON file was not found."

    with open(expected_file_path) as json_file:
        data = json.load(json_file)

    assert len(data) == 1
    assert data[0]["key"] == "test_field_set"
    assert len(data[0]["assigned_fields"]) == 1

    assigned_field_data = data[0]["assigned_fields"][0]
    assert assigned_field_data["field_definition_key"] == "test_field_definition"
    assert assigned_field_data["field_definition_flavour"] == "test_flavour"
    assert assigned_field_data["field_definition_return_type"] == "return_type_key"
    assert len(assigned_field_data["relevant_page_objects"]) == 1

    relevant_page_object_data = assigned_field_data["relevant_page_objects"][0]
    assert relevant_page_object_data["page_object_key"] == "test_page_object_key"
    assert len(relevant_page_object_data["priority_mappings"]) == 1

    priority_mapping_data = relevant_page_object_data["priority_mappings"][0]
    assert priority_mapping_data["document_category_key"] == "test_document_category"
    assert priority_mapping_data["priority"] == 1
