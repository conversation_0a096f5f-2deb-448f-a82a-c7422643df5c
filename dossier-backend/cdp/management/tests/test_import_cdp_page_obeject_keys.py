import json
import pytest
from django.core.management import call_command
from pathlib import Path
from cdp.models import PageObjectType


@pytest.mark.django_db
def test_import_page_object_keys_from_json(tmp_path):
    """
    Test the Django management command to ensure that PageObjectType objects
    are correctly created from a provided JSON file.
    """
    test_data = [
        {"key": "test_key_1"},
        {"key": "test_key_2"},
        {"key": "test_key_3"},
    ]

    temp_dir = tmp_path / "page_object_keys"
    temp_dir.mkdir(parents=True, exist_ok=True)
    temp_file = temp_dir / "test_page_object_keys.json"

    with open(temp_file, "w") as f:
        json.dump(test_data, f)

    call_command("import_cdp_page_object_keys", "--file", str(temp_file))

    assert PageObjectType.objects.filter(key="test_key_1").exists()
    assert PageObjectType.objects.filter(key="test_key_2").exists()
    assert PageObjectType.objects.filter(key="test_key_3").exists()

    assert PageObjectType.objects.count() == 3


@pytest.mark.django_db
def test_import_page_object_keys_default_file(mocker, tmp_path):
    """
    Test the command when no file is provided, ensuring that it defaults to the latest JSON file.
    """
    temp_dir = tmp_path.parent.parent / "assets" / "page_object_keys"
    temp_dir.mkdir(parents=True, exist_ok=True)

    old_file = temp_dir / "20230101_Hypodossier_Page_Object_Keys.json"
    new_file = temp_dir / "20240101_Hypodossier_Page_Object_Keys.json"

    old_data = [{"key": "old_key"}]
    new_data = [{"key": "new_key"}]

    with open(old_file, "w") as f:
        json.dump(old_data, f)

    with open(new_file, "w") as f:
        json.dump(new_data, f)

    mocker.patch(
        "cdp.management.commands.import_cdp_page_object_keys.Path",
        return_value=Path(temp_dir),
    )

    call_command("import_cdp_page_object_keys")

    assert PageObjectType.objects.filter(key="new_key").exists()

    assert PageObjectType.objects.count() == 1
