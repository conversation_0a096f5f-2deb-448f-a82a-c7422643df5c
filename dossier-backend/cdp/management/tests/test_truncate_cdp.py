import pytest

from cdp.management.commands.truncate_cdp import truncate_cdp_tables
from cdp.models import (
    GenericPriorityMapping,
    RelevantSemanticDocument,
    RelevantSemanticPage,
    RelevantPageObject,
    AssignedField,
    FieldDefinition,
    FieldSet,
    PageObjectType,
    DocumentCategory,
    ReturnType,
)


def assert_cdp_tables_empty():
    assert GenericPriorityMapping.objects.count() == 0
    assert RelevantSemanticDocument.objects.count() == 0
    assert RelevantSemanticPage.objects.count() == 0
    assert RelevantPageObject.objects.count() == 0
    assert AssignedField.objects.count() == 0
    assert FieldDefinition.objects.count() == 0
    assert FieldSet.objects.count() == 0
    assert PageObjectType.objects.count() == 0
    assert DocumentCategory.objects.count() == 0
    assert ReturnType.objects.count() == 0
    return True


def assert_cdp_tables_with_data():
    assert GenericPriorityMapping.objects.count() != 0
    assert RelevantSemanticDocument.objects.count() != 0
    assert RelevantSemanticPage.objects.count() != 0
    assert RelevantPageObject.objects.count() != 0
    assert AssignedField.objects.count() != 0
    assert FieldDefinition.objects.count() != 0
    assert FieldSet.objects.count() != 0
    assert PageObjectType.objects.count() != 0
    assert DocumentCategory.objects.count() != 0
    assert ReturnType.objects.count() != 0
    return True


@pytest.mark.django_db
def test_truncate_cdp_tables(sample_dossier_cdp_with_hints):
    """
    Test that all tables in the CDP app can be truncated.
    """
    assert assert_cdp_tables_with_data()
    truncate_cdp_tables()
    assert assert_cdp_tables_empty()
