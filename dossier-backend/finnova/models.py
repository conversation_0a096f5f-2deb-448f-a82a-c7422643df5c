from django.db import models
from django.db.models import CASCADE

from core.behaviors import Timestampable
from dossier.models import Dossier, Account


class FinnovaDossierProperties(Timestampable):
    account = models.ForeignKey(Account, on_delete=CASCADE)
    dossier = models.OneToOneField(
        Dossier, on_delete=CASCADE, related_name="finnova_properties"
    )

    # Fields from ChangeDossier schema
    sequence_number = models.CharField(
        max_length=255, null=True, blank=True, help_text="Laufnummer"
    )
    financing_number = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Finanzierungsnummer (Kundenummer/Rahmennummer/Laufnummer)",
    )
    client_id = models.Char<PERSON>ield(
        max_length=255, null=True, blank=True, help_text="Kundennummer"
    )
    client_key = models.CharField(
        max_length=255, null=True, blank=True, help_text="Kunden Schlüssel"
    )
