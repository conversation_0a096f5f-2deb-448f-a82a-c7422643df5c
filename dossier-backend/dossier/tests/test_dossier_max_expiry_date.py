from datetime import timed<PERSON><PERSON>

from core.authentication import AuthenticatedClient
from dossier import schemas
from dossier.helpers import get_dossier_queryset
from dossier.models import <PERSON>ssier, Account


def test_max_expiry_date(db, testuser1_client: AuthenticatedClient):
    # Test 1: check that max expiry is taken from account
    dossier: Dossier = get_dossier_queryset().get(
        name="sales pitch mix with errors dossier"
    )

    max_expiry_date_expected = dossier.created_at + timedelta(
        dossier.account.max_dossier_expiry_duration_days
    )

    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/data_v2", content_type="application/json"
    )
    assert result.status_code == 200
    actual = schemas.SemanticDossierSimple.model_validate_json(result.content)
    assert actual.max_expiry_date.replace(
        microsecond=0
    ) == max_expiry_date_expected.replace(microsecond=0)

    # Test 2: change the max expiry on the account, then we should find a different max expiry on the dossier
    a = Account.objects.get(uuid=dossier.account.uuid)
    a.max_dossier_expiry_duration_days += 1
    a.save()

    result2 = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/data_v2", content_type="application/json"
    )
    result2_actual = schemas.SemanticDossierSimple.model_validate_json(result2.content)

    assert result2_actual.max_expiry_date.replace(
        microsecond=0
    ) == max_expiry_date_expected.replace(microsecond=0) + timedelta(days=1)

    a.max_dossier_expiry_duration_days -= 1
    a.save()
