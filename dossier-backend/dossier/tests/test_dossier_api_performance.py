from statistics import mean
from django.utils import timezone

import structlog

from core.authentication import AuthenticatedClient
from dossier import schemas
from dossier.helpers import get_dossier_queryset
from dossier.models import Dossier

logger = structlog.get_logger()


def test_dossier_api_performance(db, testuser1_client: AuthenticatedClient):
    dossier: Dossier = get_dossier_queryset().get(
        name="sales pitch mix with errors dossier"
    )

    # dossier_uuid = "31ecb230-9947-4626-864b-4232e8cbce24"
    # dossier = Dossier.objects.get(uuid=dossier_uuid)

    durations = []
    num_runs = 5
    for i in range(1, num_runs):
        start = timezone.now()
        result = testuser1_client.get(
            f"/api/dossier/{dossier.uuid}/data_v2", content_type="application/json"
        )
        assert result.status_code == 200

        duration = (timezone.now() - start).total_seconds()
        durations.append(duration)

        sds = schemas.SemanticDossierSimple.model_validate_json(result.content)

        logger.info(
            "duration",
            duration_seconds=duration,
            num_original_files=sds.count_original_files,
            # these are incorrectly named in API, sds.extracted_files is actually a dict with
            # number of original files entries
            num_original_files_from_list=len(sds.extracted_files),
            num_extracted_files_v2=len(sds.extracted_files_v2),
            num_sem_docs=len(sds.semantic_documents),
        )

    avg = mean(durations)
    logger.info("average duration", average_seconds=avg, num_runs=num_runs)

    # Not a very strict test, should rather be around 0.2seconds
    assert avg < 0.5
