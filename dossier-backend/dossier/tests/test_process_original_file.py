import pytest
from unittest.mock import patch
from django.core.files.base import ContentFile

from dossier.factories import (
    DossierFileFactory,
    OriginalFileFactory,
)
from dossier.models import FileStatus
from dossier.tasks import process_original_file, ProcessOriginalFileRequestV1
from dossier.processing_config import OriginalFileProcessingConfig


@pytest.fixture
def original_content():
    return ContentFile(b"original content", name="test.pdf")


@pytest.fixture
def replacement_content():
    return ContentFile(b"replacement content", name="test_fixed.pdf")


@pytest.fixture
def original_file(original_content):
    """Create an original file with error status"""
    original_file = OriginalFileFactory(content_file=original_content)
    original_file.status = FileStatus.ERROR
    original_file.exception_en = "Processing error"
    original_file.save()
    return original_file


@pytest.fixture
def replacement_file(original_file, replacement_content):
    """Create a replacement file for the original file"""
    replacement = DossierFileFactory(
        dossier=original_file.dossier, data=replacement_content
    )
    original_file.file_replacement = replacement
    original_file.save()
    return replacement


@pytest.mark.django_db
@patch("dossier.tasks.publish")
def test_process_with_replacement(mock_publish, original_file, replacement_file):
    """Test that process_original_file uses replacement file when available"""
    process_original_file(original_file, use_replacement=True)

    # Verify the publish call used the replacement file URL
    publish_args = mock_publish.call_args[0][0]
    assert replacement_file.fast_url in publish_args.decode()
    assert original_file.file.fast_url not in publish_args.decode()


@pytest.mark.django_db
@patch("dossier.tasks.publish")
def test_process_without_replacement(mock_publish, original_file, replacement_file):
    """Test that process_original_file uses original file when use_replacement=False"""
    process_original_file(original_file, use_replacement=False)

    # Verify the publish call used the original file URL
    publish_args = mock_publish.call_args[0][0]
    assert original_file.file.fast_url in publish_args.decode()
    assert replacement_file.fast_url not in publish_args.decode()


@pytest.mark.django_db
@patch("dossier.tasks.publish")
def test_process_no_replacement_falls_back(mock_publish, original_file):
    """Test that process_original_file falls back to original file when no replacement exists"""
    # No replacement file is created
    process_original_file(original_file, use_replacement=True)

    # Should fall back to original file URL
    publish_args = mock_publish.call_args[0][0]
    assert original_file.file.fast_url in publish_args.decode()


@pytest.mark.django_db
@patch("dossier.tasks.publish")
def test_process_maintains_original_file_references(
    mock_publish, original_file, replacement_file
):
    """Test that process_original_file maintains original file references when using replacement"""
    process_original_file(original_file, use_replacement=True)

    # Verify the original file UUID is used in the request
    publish_args = mock_publish.call_args[0][0]

    publish_args_validated = ProcessOriginalFileRequestV1.model_validate_json(
        publish_args.decode()
    )

    assert publish_args_validated.original_file_uuid == original_file.uuid

    # Note: the filename changes to the filename of the replacment file
    assert publish_args_validated.filename == original_file.file_replacement.name

    assert replacement_file.name in publish_args_validated.file_url

    assert original_file.file.name not in publish_args_validated.file_url


@pytest.mark.django_db
@patch("dossier.tasks.publish")
def test_virus_scan_config_propagation(mock_publish, original_file):
    """Test that virus scan configuration is properly propagated"""
    # Enable virus scanning on the account
    original_file.dossier.account.enable_virus_scan = True
    original_file.dossier.account.save()

    process_original_file(original_file)

    # Verify virus scan setting was included in the request
    publish_args = mock_publish.call_args[0][0]
    assert '"enable_virus_scan":true' in publish_args.decode()


@pytest.mark.django_db
@patch("dossier.tasks.publish")
def test_processing_config_propagation(mock_publish, original_file):
    """Test that processing configuration is properly propagated"""
    # Create a processing config
    config = OriginalFileProcessingConfig(
        force_document_category_key="TEST_DOC",
        force_title_suffix="test_suffix",
        enable_virus_scan=False,
    )

    process_original_file(original_file, processing_config=config)

    # Verify config was included in the request
    publish_args = mock_publish.call_args[0][0]
    assert '"force_document_category_key":"TEST_DOC"' in publish_args.decode()
    assert '"force_title_suffix":"test_suffix"' in publish_args.decode()
    assert '"enable_virus_scan":false' in publish_args.decode()


@pytest.mark.django_db
@patch("dossier.tasks.publish")
def test_language_propagation(mock_publish, original_file):
    """Test that dossier language is properly propagated"""
    # Set dossier language
    original_file.dossier.lang = "DE"
    original_file.dossier.save()

    process_original_file(original_file)

    # Verify language was included in the request
    publish_args = mock_publish.call_args[0][0]
    assert '"lang":"de"' in publish_args.decode()  # Should be lowercase in request
