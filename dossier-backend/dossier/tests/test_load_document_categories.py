from pathlib import Path
from typing import List

import pytest
from pydantic import TypeA<PERSON>pter
import structlog
from django.contrib.contenttypes.models import ContentType

from assets import ASSETS_PATH
from dossier.doc_cat_helpers import (
    load_document_categories,
    load_document_categories_from_path,
)
from dossier.models import Account
from dossier.schemas import DocumentCategoryLoad

data_file = Path(__file__).parent / "data"

document_categories_json_file = data_file / "document_categories.json"

logger = structlog.get_logger()


@pytest.mark.django_db
def test_load_document_categories():
    document_categories = TypeAdapter(List[DocumentCategoryLoad]).validate_json(
        document_categories_json_file.read_text()
    )
    account = Account.objects.create(key="test_bekb", name="Test BEKB")
    l_all, l_created, l_updated, l_skipped = load_document_categories(
        account, document_categories
    )

    assert len(l_created) == 296
    assert len(l_updated) == 0
    assert len(l_updated) == 0


@pytest.mark.django_db
def test_load_document_categories_default_from_path():
    ContentType.objects.clear_cache()
    account = Account.objects.create(key="test default", name="Test default")

    # First load the previous document categories
    path_old = (
        ASSETS_PATH / "document_category/default/DocumentCategory-2024-05-23.json"
    )
    l_all, l_created, l_updated, l_skipped = load_document_categories_from_path(
        account, document_categories_json_path=path_old
    )

    assert len(l_created) == 259
    assert len(l_updated) == 0
    assert len(l_skipped) == 0

    # Then update with the latest version to see the changes
    l_all, l_created, l_updated, l_skipped = load_document_categories_from_path(
        account,
        document_categories_json_path=ASSETS_PATH
        / "document_category/default/DocumentCategory-2024-07-09.json",
    )

    logger.info(
        "updated with latest changes:",
        l_all=len(l_all),
        l_created=len(l_created),
        l_skipped=len(l_skipped),
    )

    # 240112 mt: now 249: 3 new categories CV_CLIENT, BUILDING_RIGHT_INTEREST, PLATFORM_AGREEMENT
    # 240326 mt: now 251: 2 new categories PROOF_OF_FUNDS, RENOVATIONS
    # 240514 mt: now 258: 7 new categories LIQUIDITY_PLAN_COMPANY, CASH_FLOW_COMPANY, INVESTMENT_PLAN_COMPANY,
    # ACCOUNTS_RECEIVABLE_PAYABLE_COMPANY, TURNOVER_LIST_COMPANY, BUDGET_CONTROL_COMPANY, FIN_REPORTING_COMPANY,
    # LOAN_AGREEMENT_SHAREHOLDER_COMPANY
    # 240709 mt: now 259: 1 new category PROPERTY_BILL
    assert len(l_created) == 0
    keys_updated = sorted([dc.name for dc in l_updated])
    assert keys_updated == [
        "AUTHORIZATION_FOR_INQUIRIES",
        "BROKER_AUTHORIZATION",
        "BROKER_MANDATE",
        "EXTRACT_AHV_ACCOUNT",
        "HRA",
    ]
    assert len(l_updated) == 5  # minor alias changes, now renamings
    assert len(l_skipped) == 254

    # Then update with the latest version to see the changes
    l_all, l_created, l_updated, l_skipped = load_document_categories_from_path(
        account,
        document_categories_json_path=ASSETS_PATH
        / "document_category/default/DocumentCategory-2025-03-10.json",
    )

    logger.info(
        "updated with latest changes:",
        l_all=len(l_all),
        l_created=len(l_created),
        l_skipped=len(l_skipped),
    )

    # 240912 mt: now 261: new DEATH_CERTIFICATE and MORTGAGE_SUBORDINATION_AGREEMENT
    # 241023 mt: now 262 with INCORPORATION_COMPANY
    # 241113 mt: now 264 with CORRESPONDENCE_NOTARY, LETTER_COMMITMENT_NOTARY
    assert len(l_created) == 5
    assert len(l_updated) == 175  # Mostly updates in French document category name
    assert len(l_skipped) == 84
    assert len(l_created) + len(l_updated) + len(l_skipped) == 264


@pytest.mark.django_db
def test_load_document_categories_bekb_from_path():
    account = Account.objects.create(key="test bekb path", name="Test BEKB path")
    l_all, l_created, l_updated, l_skipped = load_document_categories_from_path(
        account, str(document_categories_json_file)
    )

    assert len(l_created) == 296
    assert len(l_updated) == 0
    assert len(l_updated) == 0
