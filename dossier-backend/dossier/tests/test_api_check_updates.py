from typing import TypedDict, List, Optional, Dict

from django.urls import reverse

from dossier import schemas as dossier_schemas
from dossier.models import Dossier


# Type definitions
class FileDataItem(TypedDict):
    uuid: str
    path: str
    status: str


class RequestDataToCheckUpdates(TypedDict):
    original_files: List[FileDataItem]
    extracted_files: List[FileDataItem]
    exceptions: List[FileDataItem]
    note: Optional[str]
    name: Optional[str]
    businesscase_type_id: Optional[str]


class ExceptionData(TypedDict):
    uuid: str
    type: str
    de: str
    en: str
    fr: str
    it: str
    details: str
    path_from_original: str
    full_path: str
    last_update: str
    created_at: str
    dossier_file_uuid: str


class ExtractedFileData(TypedDict):
    dossier_file_uuid: str
    uuid: str
    type: str
    full_path: str
    last_update: str
    created_at: str


class FileStatus(TypedDict):
    original_file_path: str
    type: str
    uuid: str
    dossier_file_uuid: str
    last_update: str
    created_at: str
    data: Dict[str, list]


# There's a whole bunch of logic in the frontend that takes data from data_v2 and returns a structured request data
# to check updates. This is my attempt at replicating that logic in Python
def get_extracted_data(
    dossiers_data: dict, with_duplicate: bool = False
) -> Dict[str, FileStatus]:
    big_data: Dict[str, FileStatus] = {}

    # STEP 1: FILL ORIGINAL FILES
    if extracted_files := dossiers_data.get("extracted_files", {}):
        for original_file_uuid, extracted_file in extracted_files.items():
            if extracted_file:
                big_data[extracted_file["original_file_uuid"]] = {
                    "original_file_path": extracted_file["original_file_name"],
                    "type": extracted_file.get("status", ""),
                    "uuid": extracted_file.get("uuid", ""),
                    "dossier_file_uuid": extracted_file.get("dossier_file_uuid", ""),
                    "last_update": extracted_file.get("last_update", ""),
                    "created_at": extracted_file.get("created_at", ""),
                    "data": {"exceptions": [], "extracted_file_data": []},
                }

    # STEP 2: FILL EXCEPTIONS
    # 2.1 Extracted exceptions
    if extracted_files:
        for original_file_uuid, extracted_file in extracted_files.items():
            if not extracted_file:
                continue

            for exception_path, exception in extracted_file.get(
                "exceptions", {}
            ).items():
                if extracted_file["original_file_uuid"] in big_data:
                    big_data[extracted_file["original_file_uuid"]]["data"][
                        "exceptions"
                    ].append(
                        {
                            "uuid": exception["uuid"],
                            "type": exception.get("file_status", ""),
                            "de": exception["de"],
                            "en": exception["en"],
                            "fr": exception["fr"],
                            "it": exception["it"],
                            "details": exception["details"],
                            "path_from_original": exception["path_from_original"],
                            "full_path": exception_path,
                            "last_update": exception["last_update"],
                            "created_at": exception["created_at"],
                            "dossier_file_uuid": exception.get("dossier_file_uuid", ""),
                        }
                    )
                else:
                    big_data[original_file_uuid] = {
                        "type": "",
                        "uuid": "",
                        "last_update": "",
                        "created_at": "",
                        "dossier_file_uuid": exception.get("dossier_file_uuid", ""),
                        "original_file_path": original_file_uuid,
                        "data": {"exceptions": [], "extracted_file_data": []},
                    }

    # 2.2 Processing exceptions
    if processing_exceptions := dossiers_data.get("processing_exceptions", {}):
        for (
            processing_exception_path,
            processing_exception,
        ) in processing_exceptions.items():
            if not processing_exception:
                continue

            if processing_exception["original_file_uuid"] in big_data:
                big_data[processing_exception["original_file_uuid"]]["data"][
                    "exceptions"
                ].append(
                    {
                        "uuid": processing_exception["uuid"],
                        "type": processing_exception.get("file_status", ""),
                        "de": processing_exception["de"],
                        "en": processing_exception["en"],
                        "fr": processing_exception["fr"],
                        "it": processing_exception["it"],
                        "details": processing_exception["details"],
                        "path_from_original": processing_exception[
                            "path_from_original"
                        ],
                        "full_path": processing_exception_path,
                        "last_update": processing_exception["last_update"],
                        "created_at": processing_exception["created_at"],
                        "dossier_file_uuid": processing_exception.get(
                            "dossier_file_uuid", ""
                        ),
                    }
                )

    # STEP 3: FILL EXTRACTED FILES
    if extracted_files_v2 := dossiers_data.get("extracted_files_v2", {}):
        for file_uuid, extracted_file in extracted_files_v2.items():
            if (
                not extracted_file
                or extracted_file["original_file_uuid"] not in big_data
            ):
                continue

            is_duplicate = any(
                exception["full_path"] == extracted_file["path_from_original"]
                for exception in big_data[extracted_file["original_file_uuid"]]["data"][
                    "exceptions"
                ]
            )

            if not is_duplicate or with_duplicate:
                big_data[extracted_file["original_file_uuid"]]["data"][
                    "extracted_file_data"
                ].append(
                    {
                        "dossier_file_uuid": extracted_file.get(
                            "dossier_file_uuid", ""
                        ),
                        "uuid": extracted_file["uuid"],
                        "type": extracted_file["status"],
                        "full_path": extracted_file["path_from_original"],
                        "last_update": extracted_file["updated_at"],
                        "created_at": extracted_file["created_at"],
                    }
                )

    return big_data


def get_request_data(
    dossiers_data: dict, get_extracted_data_func
) -> RequestDataToCheckUpdates:
    """
    Generate request data structure from dossiers data.

    Args:
        dossiers_data: Dictionary containing dossier information
        get_extracted_data_func: Function to get extracted data

    Returns:
        RequestDataToCheckUpdates: Structured request data
    """
    data_for_request = {
        "original_files": [],
        "extracted_files": [],
        "exceptions": [],
        "note": dossiers_data.get("note"),
        "name": dossiers_data.get("name"),
        "businesscase_type_id": dossiers_data.get("businesscase_type_id"),
    }

    new_data = get_extracted_data_func(dossiers_data=dossiers_data, with_duplicate=True)

    for original_file_uuid, file_data in new_data.items():
        # Add original file
        data_for_request["original_files"].append(
            {"uuid": original_file_uuid, "last_update": file_data["last_update"]}
        )

        # Add extracted files
        data_for_request["extracted_files"].extend(
            [
                {
                    "uuid": extracted_file["uuid"],
                    "last_update": extracted_file["last_update"],
                }
                for extracted_file in file_data["data"]["extracted_file_data"]
            ]
        )

        # Add exceptions
        data_for_request["exceptions"].extend(
            [
                {"uuid": exception["uuid"], "last_update": exception["last_update"]}
                for exception in file_data["data"]["exceptions"]
            ]
        )

    return data_for_request


# Tests could be written dryer - but I wanted to keep the logic simple for now
# in case we refactor it later
def test_get_updates_sales_pitch_mix(
    db, testuser1_client, django_assert_max_num_queries
):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    original_file = dossier.original_files.first()

    original_file.exception_de = "Test exception"
    original_file.save()

    # Get data_v2 to create some updates
    data_v2_response = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )

    data_v2_parsed = dossier_schemas.SemanticDossierSimple.model_validate_json(
        data_v2_response.content
    )

    data_to_compare = get_request_data(data_v2_parsed.model_dump(), get_extracted_data)

    with django_assert_max_num_queries(17):
        response = testuser1_client.post(
            reverse(
                "api:check-updates",
                kwargs={"dossier_uuid": dossier.uuid},
            ),
            data=data_to_compare,
            content_type="application/json",
        )

    assert response.status_code == 200

    parsed = dossier_schemas.StatusUpdate.model_validate_json(response.content)

    assert parsed.need_update is False

    # Add remove exception to the original file
    original_file.exception_de = ""
    original_file.save()

    with django_assert_max_num_queries(17):
        response = testuser1_client.post(
            reverse(
                "api:check-updates",
                kwargs={"dossier_uuid": dossier.uuid},
            ),
            data=data_to_compare,
            content_type="application/json",
        )

    assert response.status_code == 200

    parsed = dossier_schemas.StatusUpdate.model_validate_json(response.content)

    assert parsed.need_update is True


def test_get_updates_sales_pitch_mix_no_exceptions(
    db, testuser1_client, django_assert_max_num_queries
):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    dossier.fileexception_set.all().delete()

    # Get data_v2 to create some updates
    data_v2_response = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )

    data_v2_parsed = dossier_schemas.SemanticDossierSimple.model_validate_json(
        data_v2_response.content
    )

    data_to_compare = get_request_data(data_v2_parsed.model_dump(), get_extracted_data)

    with django_assert_max_num_queries(17):
        response = testuser1_client.post(
            reverse(
                "api:check-updates",
                kwargs={"dossier_uuid": dossier.uuid},
            ),
            data=data_to_compare,
            content_type="application/json",
        )

    assert response.status_code == 200

    parsed = dossier_schemas.StatusUpdate.model_validate_json(response.content)

    assert parsed.need_update is False

    original_file = dossier.original_files.first()

    # Add an exception to the original file
    original_file.exception_de = "Test exception"
    original_file.save()

    with django_assert_max_num_queries(17):
        response = testuser1_client.post(
            reverse(
                "api:check-updates",
                kwargs={"dossier_uuid": dossier.uuid},
            ),
            data=data_to_compare,
            content_type="application/json",
        )

    assert response.status_code == 200

    parsed = dossier_schemas.StatusUpdate.model_validate_json(response.content)

    assert parsed.need_update is True
