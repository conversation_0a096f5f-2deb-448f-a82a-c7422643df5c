{"dossier_uuid": "DUMMY_DOSSIER_UUID", "processed_file": {"original_file_path": "TAX_max_maria_2019_4.pdf", "file_path": "TAX_max_maria_2019_4.pdf", "file_path_url_encoded": "TAX_max_maria_2019_4.pdf", "filename": "TAX_max_maria_2019_4.pdf", "pages": {"0": {"number": 0, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 110, "name": "TAX_DECLARATION_PAGE_PERSONAL_DATA"}, "confidence": 0.97755, "confidence_info": {"parsername": "TaxDeclarationZHPersonalDataPageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Deckblatt", "confidence": 0.9997667670249939, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 8.745212107896805e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Antrag auf Neuveranlagung", "confidence": 5.461315595312044e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Vermögen", "confidence": 2.0401219444465823e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/FR/Personalien", "confidence": 1.934035390149802e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2855, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2387, "bottom": 414}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 32, "left": 32, "right": 134, "bottom": 116}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 633, "left": 2047, "right": 2407, "bottom": 703}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_fullname", "title": "P1 Name", "titles": {"de": "P1 Name", "en": "P1 Name", "fr": "P1 Nom", "it": "[P1 Name]"}, "value": "<PERSON><PERSON><PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 723, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "address_block", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Address", "fr": "[Address]", "it": "[Address]"}, "value": "<PERSON><PERSON><PERSON> Maria\nBe<PERSON>pielstrasse 42\n8055 Zürich", "type": "ADDRESS_BLOCK", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 781, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "street", "title": "Strasse", "titles": {"de": "Strasse", "en": "Street", "fr": "[Street]", "it": "[Street]"}, "value": "Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 848, "left": 816, "right": 1183, "bottom": 911}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "zip", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "Zip Code", "fr": "[Zip Code]", "it": "[Zip Code]"}, "value": "8055", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 815, "right": 920, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "city", "title": "Ort", "titles": {"de": "Ort", "en": "City", "fr": "[City]", "it": "[City]"}, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 925, "right": 1054, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 311, "left": 38, "right": 172, "bottom": 354}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_date_of_birth", "title": "P1 Geburtsdatum", "titles": {"de": "P1 Geburtsdatum", "en": "P1 Date of Birth", "fr": "P1 Date de naissance", "it": "[P1 Date of Birth]"}, "value": "04.09.1967", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 964, "right": 1191, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_date_of_birth", "title": "P2 Geburtsdatum", "titles": {"de": "P2 Geburtsdatum", "en": "P2 Date of Birth", "fr": "P2 Date de naissance", "it": "[P2 Date of Birth]"}, "value": "21.10.1976", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 2040, "right": 2267, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_marital_status", "title": "P1 Zivilstand", "titles": {"de": "P1 Zivilstand", "en": "P1 Civil Status", "fr": "P1 Etat civil", "it": "[P1 Civil Status]"}, "value": "verheiratet", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1834, "left": 963, "right": 1181, "bottom": 1897}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_firstname", "title": "P2 Vorname", "titles": {"de": "P2 Vorname", "en": "P2 Lastname", "fr": "P2 Prénom", "it": "[P2 Lastname]"}, "value": "<PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1831, "left": 2043, "right": 2156, "bottom": 1894}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Wegleitung verwendeten Begriffe     Zivilstand      verheiratet                                   Vorname    Maria\\nwie verheiratet, getrennt, ge-       Konfession     keine                                         Konfession     keine\\nschieden, verwitwet oder <PERSON>he,       Beruf             Informatiker                                Beruf          Solution Consultant\"}", "type": "FIN_HURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1897, "left": 388, "right": 520, "bottom": 1960}, "page_number": 0, "confidence": 0.08460009843111038, "confidence_summary": {"value": 0.08460009843111038, "value_formatted": "8%", "color": "red"}, "source": null, "visible": false, "invisibility_reason": "Financial hurdle confidence is 8% but must be at least 10% to be visible", "confidence_value": 0.08460009843111038, "confidence_formatted": "8%", "confidence_level": "low"}, {"key": "p1_profession", "title": "P1 Beruf", "titles": {"de": "P1 Beruf", "en": "P1 Profession", "fr": "P1 Profession", "it": "[P1 Profession]"}, "value": "Informatiker", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 966, "right": 1207, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_profession", "title": "P2 Beruf", "titles": {"de": "P2 Beruf", "en": "P2 Profession", "fr": "P2 Profession", "it": "[P2 Profession]"}, "value": "Solution Consultant", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 2041, "right": 2435, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_employer", "title": "P1 Arbeitgeber", "titles": {"de": "P1 Arbeitgeber", "en": "P1 Employer", "fr": "P1 Employeur", "it": "[P1 Employer]"}, "value": "Beispielfirma AG", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 965, "right": 1300, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_employer", "title": "P2 Arbeitgeber", "titles": {"de": "P2 Arbeitgeber", "en": "P2 Employer", "fr": "P2 Employeur", "it": "[P2 Employer]"}, "value": "ServiceFirma", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 2041, "right": 2308, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_employer_location", "title": "P1 Arbeitsort", "titles": {"de": "P1 Arbeitsort", "en": "P1 Place of work", "fr": "P1 Lieu de travail", "it": "[P1 Place of work]"}, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2074, "left": 963, "right": 1090, "bottom": 2137}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "phone_primary", "title": "Telefonnummer", "titles": {"de": "Telefonnummer", "en": "Phone number", "fr": "[Phone number]", "it": "[Phone number]"}, "value": "0763331234      P 07633", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2127, "left": 964, "right": 1539, "bottom": 2197}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "phone_secondary", "title": "Telefonnummer (2. Prio)", "titles": {"de": "Telefonnummer (2. Prio)", "en": "Phone number (2nd Priority)", "fr": "[Phone number (2nd Priority)]", "it": "[Phone number (2nd Priority)]"}, "value": "Telefon G       0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 1769, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_phone_primary", "title": "P2 Telefonnummer", "titles": {"de": "P2 Telefonnummer", "en": "P2 Phone number", "fr": "P2 Téléphone", "it": "[P2 Phone number]"}, "value": "0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 2041, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "section_children", "title": "Kinder", "titles": {"de": "Kinder", "en": "Children", "fr": "<PERSON><PERSON><PERSON>", "it": "[Children]"}, "value": "<PERSON>", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2534, "left": 137, "right": 572, "bottom": 2656}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"wie verheiratet, getrennt, ge-       Konfession     keine                                         Konfession     keine\\nschieden, verwitwet oder Ehe,       Beruf             Informatiker                                Beruf          Solution Consultant\\nEhegatten, Ehemann und Ehe-     Arbeitgeber \\nBeispielfirma AG                          Arbeitgeber   ServiceFirma\"}", "type": "FIN_HURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2569, "left": 2687, "right": 2815, "bottom": 2615}, "page_number": 0, "confidence": 0.17452068626880646, "confidence_summary": {"value": 0.17452068626880646, "value_formatted": "17%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.17452068626880646, "confidence_formatted": "17%", "confidence_level": "low"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3799, "left": 139, "right": 2828, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 34, "right": 197, "bottom": 1146}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1088, "left": 721, "right": 790, "bottom": 1133}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 2489, "num_chars_alpha": 2217, "num_chars_digit": 220, "num_lines": 62, "plaintext": "06  Steuererklärung 2019\nfür natürliche Personen\nKanton Zürich       Staats-, Gemeinde- und direkte Bundessteuer\nDiese Original-Steuererklärung \n11157640                                         Zürich                      261\nist zusammen mit dem \nWertschriftenverzeichnis und den \nübrigen Unterlagen bis                                                                                  756.4078.6666.31\nEnde März 2020\ndem Gemeindesteueramt             Mustermann Max\neinzureichen.                     Mustermann Maria\nBeispielstrasse 42\n8055 Zürich                                       04.09.1967\n21.10.1976 \nInformatiker \nSolution Consultant \nlllllllllllllllllllllllll\nverheiratet\n011157640 \nVertreter/in bevollmächtigt zur Entgegennahme von Auflagen und Entscheiden bzw. Veranlagungsverfügungen\nName / Firma \nVorname                                                                          Telefon\nStrasse                                         Nr.          Treuhänder-ID\nPLZ              Ort                                      CHE\nDie eingetragene Partnerschaft     Personalien, Berufs- und Familienverhältnisse am 31. Dezember 2019\ngleichgeschlechtlicher Paare wird ^Ehemann / Einzelperson / P1                                  Ehefrau / P2\ngleich behandelt wie die Ehe. Die    Geburtsdatum 04.09.1967                             Geburtsdatum 21.10.1976\nin der Steuererklärung und der \nWegleitung verwendeten Begriffe     Zivilstand      verheiratet                                   Vorname    Maria\nwie verheiratet, getrennt, ge-       Konfession     keine                                         Konfession     keine\nschieden, verwitwet oder Ehe,       Beruf             Informatiker                                Beruf          Solution Consultant\nEhegatten, Ehemann und Ehe-     Arbeitgeber \nBeispielfirma AG                          Arbeitgeber   ServiceFirma\nfrau gelten sinngemäss für die \neingetragene Partnerschaft.         Arbeitsort      Zürich                            Arbeitsort \nP1 steht für Partner/Partnerin 1       Telefon     G. 0763331234      P 0763331234     Telefon G.       0799991234\nP2 steht für Partner/Partnerin 2       Zahlungen an Pensionskasse (2. Säule)?      ja ■ nein           ■ ja       nein\nPartn. steht für Partner/Partnerin\nIn welcher zürcherischen Gemeinde haben Sie die letzte Steuererklärung eingereicht?\nKinder der Jahrgänge 2002-2019 oder in beruflicher Erstausbildung stehende Kinder, deren Unterhalt Sie bestreiten:\nKinder in Ihrem Haushalt:                           GeburtsdatumSchule oder Lehrfirma            Voraussichtlich    Leistet der andere Elternteil\nVorname, Name                                                                    (wenn in Ausbildung)                   bis            Unterhaltsbeiträge?* \nRaphael Mustermann                       12.04.2013  Primarschule                      2023          ja ■ nein  * wenn Sie le dig \noder geschieden *\nAngelo Mustermann                       08.02.2015  Kindergarten                      2021     ja ■ nein   sind oder von \nIhrem Ehegatten \n ja  nein    getrennt leben.\nKinder ausserhalb Ihres Haushaltes:\nVorname, Name                                GeburtsdatumAdresse                        Schule / Lehrfirma        Voraussichtlich bis\n\n\nErwerbsunfähige oder beschränkt erwerbsfähige Personen (ohne Ehegatten/Partn. und oben aufgeführte Kinder),                           Unterstützungsbetrag \ndie Sie mit einem jährlichen Beitrag von mindestens S Staatssteuer CHF 2'700 © Bundessteuer CHF 6'500 unterstützen:                      pro Jahr CHF\nIn Ihrem Haushalt:\nVorname, Name                                   Geburtsjahr       Adresse\n\n\nAusserhalb Ihres Haushaltes:\nVorname, Name                                   Geburtsjahr       Adresse\n                     \n                     \nBitte nicht ausfüllen\nZustellung                        Einreichungsfrist erstreckt bis            Frist erstreckt bis                       gemahnt am                   Eingang\nI1BIIIIIIIIII    Mustermann Max und Maria, Zürich\nSeite 1\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n0106192601761            Seite 1 von 13       20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Steuererklärung 2019", "für natürliche Personen", "Kanton Zürich Staats-, Gemeinde- und direkte Bundessteuer", "Personalien, Berufs- und Familienverhältnisse am 31. Dezember 2019", "<PERSON><PERSON><PERSON>", "bis", "In <PERSON><PERSON><PERSON>:", "I1BIIIIIIIIII", "Seite 1"], "top_titles": ["Steuererklärung 2019", "I1BIIIIIIIIII", "Seite 1"], "titles_with_size": [["Steuererklärung 2019", 4.045131201877803], ["für natürliche Personen", 1.2503370988414253], ["Kanton Zürich Staats-, Gemeinde- und direkte Bundessteuer", 1.2509843453727834], ["Personalien, Berufs- und Familienverhältnisse am 31. Dezember 2019", 1.310680388335302], ["<PERSON><PERSON><PERSON>", 1.2528581464560111], ["bis", 1.252321066084736], ["In <PERSON><PERSON><PERSON>:", 1.2530089238440867], ["I1BIIIIIIIIII", 5.124785639470098], ["Seite 1", 1.7049425588663278]], "top_titles_with_size": [["Steuererklärung 2019", 4.045131201877803], ["I1BIIIIIIIIII", 5.124785639470098], ["Seite 1", 1.7049425588663278]]}, "image": "pageimages/c26bdb58-5825-45cb-ae3a-0e7d67e9fbdb/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/0.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/0.txt", "uuid": "6c2bd5ca-2e77-408e-afaf-f20d4cd50f4a", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/c26bdb58-5825-45cb-ae3a-0e7d67e9fbdb/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=068e4901534c810ad872b98cec37286cddc99bf0feebafbb5f38bfa1a3c743ed", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/0.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=05d380195e87b10cbfe5f677543684b037576878d388b110ce8cafb2507278b7", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/0.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=77ab536d88729d5345dc6851f3d74c08d44ac754e4873982affcf4cc43c5dca0", "source": null}, "1": {"number": 1, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 120, "name": "TAX_DECLARATION_PAGE_INCOME"}, "confidence": 0.*********, "confidence_info": {"parsername": "TaxDeclarationZHIncomePageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Einkünfte", "confidence": 0.9998792409896851, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SH/Einkünfte", "confidence": 4.8135261749848723e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/BS/Details zu den Krankheitskosten", "confidence": 1.2898743989353534e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/OW/Einkünfte", "confidence": 1.0976544217555784e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt", "confidence": 7.870098670537118e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "p1_income_employed_main", "title": "P1 Einkommen Haupterwerb", "titles": {"de": "P1 Einkommen Haupterwerb", "en": "P1 Main Income", "fr": "P1 Revenu net dépendante", "it": "[P1 Main Income]"}, "value": "CHF 22'506", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 280, "left": 2662, "right": 2802, "bottom": 336}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_income_employed_main", "title": "P2 Einkommen Haupterwerb", "titles": {"de": "P2 Einkommen Haupterwerb", "en": "P2 Main Income", "fr": "P2 Revenu net dépendante", "it": "[P2 Main Income]"}, "value": "CHF 128'991", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 360, "left": 2640, "right": 2796, "bottom": 427}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_income_employed_side", "title": "P1 Einkommen Nebenverdienst", "titles": {"de": "P1 Einkommen Nebenverdienst", "en": "P1 Additional Income", "fr": "[P1 Additional Income]", "it": "[P1 Additional Income]"}, "value": "CHF 7'502", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 459, "left": 2688, "right": 2802, "bottom": 514}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_gewerbe", "title": "Gewerbe", "titles": {"de": "Gewerbe", "en": "Gewerbe", "fr": "Gewerbe", "it": "Gewerbe"}, "value": "{\"value\": \"<PERSON><PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON><PERSON><PERSON>\", \"classifier\": null, \"context\": \"Verwaltungsrats- und Vorstands-\\nhon<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, \\n2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 652, "left": 1731, "right": 1888, "bottom": 708}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_landwirtschaft", "title": "Landwirtschaft", "titles": {"de": "Landwirtschaft", "en": "Landwirtschaft", "fr": "Landwirtschaft", "it": "Landwirtschaft"}, "value": "{\"value\": \"Landwirtschaft\", \"value_found\": \"Landwirtschaft\", \"classifier\": null, \"context\": \"2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\\n2: Inklusive Ertr\\u00e4ge aus                 2.1  Haupterwerb           Ehemann / Einzelperson / P1                     Hilfsblatt   120\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 719, "left": 1343, "right": 1605, "bottom": 775}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_arbeitslos", "title": "arbeitslos", "titles": {"de": "arbeitslos", "en": "arbeitslos", "fr": "arbeitslos", "it": "arbeitslos"}, "value": "{\"value\": \"arbeitslos\", \"value_found\": \"Arbeitslos\", \"classifier\": null, \"context\": \"3.3  Erwerbsausfallentsch\\u00e4digungen aus Arbeitslosenversicherung\\n3.3: Direkt ausbezahlte Erwerbs-\\nausfallentsch\\u00e4digungen.                   Ehemann / Einzelperson / P1                                    Bescheinigung 140\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1801, "left": 1441, "right": 1624, "bottom": 1857}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "p2_income_eo", "title": "P2 Einkünfte Erwerbsausfallentschädigung", "titles": {"de": "P2 Einkünfte Erwerbsausfallentschädigung", "en": "P2 Income EO", "fr": "[P2 Income EO]", "it": "[P2 Income EO]"}, "value": "CHF 4'388", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1939, "left": 2686, "right": 2802, "bottom": 2006}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_income_child_benefits", "title": "P2 Einkünfte Kinder- und Familienzulagen", "titles": {"de": "P2 Einkünfte Kinder- und Familienzulagen", "en": "P2 Income Child Benefits", "fr": "[P2 Income Child Benefits]", "it": "[P2 Income Child Benefits]"}, "value": "CHF 4'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2190, "left": 2686, "right": 2802, "bottom": 2244}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_portfolio", "title": "Wertschriftenerträge", "titles": {"de": "Wertschriftenerträge", "en": "Income Portfolio", "fr": "Revenue mobilier", "it": "[Income Portfolio]"}, "value": "CHF 15", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2341, "left": 2753, "right": 2803, "bottom": 2408}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_alimente", "title": "Alimente", "titles": {"de": "Alimente", "en": "Alimente", "fr": "Alimente", "it": "Alimente"}, "value": "{\"value\": \"Ali<PERSON><PERSON>\", \"value_found\": \"Alimente\", \"classifier\": null, \"context\": \"5.1 und 5.2\\n5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2566, "left": 366, "right": 497, "bottom": 2620}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": null, \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1194, "right": 1387, "bottom": 2665}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": null, \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1449, "right": 1593, "bottom": 2665}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "property_imputed_rental_value", "title": "Liegenschaft Eigenmietwert", "titles": {"de": "Liegenschaft Eigenmietwert", "en": "Imputed Rental Value", "fr": "Valeur locative", "it": "Valore locativo"}, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3111, "left": 1971, "right": 2110, "bottom": 3165}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_real_estate_gross", "title": "Liegenschaftenertrag brutto", "titles": {"de": "Liegenschaftenertrag brutto", "en": "Income Real Estate gross", "fr": "Revenu brut immobilier", "it": "[Income Real Estate gross]"}, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3305, "left": 1971, "right": 2110, "bottom": 3358}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "property_maintenance_cost", "title": "Liegenschaft Unterhaltskosten", "titles": {"de": "Liegenschaft Unterhaltskosten", "en": "Maintenance Cost", "fr": "Charges et frais d'entretien d'immeuble", "it": "[Maintenance Cost]"}, "value": "CHF 5'140", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3397, "left": 1996, "right": 2110, "bottom": 3451}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_real_estate_net_primary", "title": "Primärliegenschaft Ertrag netto", "titles": {"de": "Primärliegenschaft Ertrag netto", "en": "Primary Self used Real Estate Income net", "fr": "[Primary Self used Real Estate Income net]", "it": "[Primary Self used Real Estate Income net]"}, "value": "CHF 20'560", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3588, "left": 2662, "right": 2802, "bottom": 3642}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3765, "left": 2640, "right": 2802, "bottom": 3833}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1086, "left": 33, "right": 198, "bottom": 1147}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1086, "left": 715, "right": 795, "bottom": 1132}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 2863, "num_chars_alpha": 2440, "num_chars_digit": 333, "num_lines": 77, "plaintext": "Einkünfte 2019\nEhemann / Einzelperson / P1,      Einkünfte im In- und Ausland\nEhefrau / P2 und minderjährige                                                                                                                                     CHF ohne Rappen\nKinder, ohne Erwerbseinkommen       1.    Einkünfte aus unselbständiger Erwerbstätigkeit\ndieser Kinder                                                                                                                                                                      *\n1.1   Haupterwerb           Ehemann / Einzelperson / P1                 Lohnausweis  100                    22 506\n*\nEhefrau / P2                               Lohnausweis  101                    128 991\n1.2 und 2.2: Entschädigungen                                                                                                                                                                *\n1.2  Nebenerwerb       Ehemann / Einzelperson / P1                 Lohnausweis  102                     7 502\nfür Dienstleistungen jeder Art, \nVergütungen für Amtstätigkeit, \nEhefrau / P2                               Lohnausweis  103\nVerwaltungsrats- und Vorstands-\nhonorare, Tantiemen, Lizenzen, \n2.    Einkünfte aus selbständiger Erwerbstätigkeit in Handel, Gewerbe, \nAutorenrechte usw.\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\n2: Inklusive Erträge aus                 2.1  Haupterwerb           Ehemann / Einzelperson / P1                     Hilfsblatt   120\nqualifizierten Beteiligungen im \nGeschäftsvermögen und                                             Ehefrau / P2                               Hilfsblatt   121\nLiquidationsgewinne aus der \nAufgabe der selbständigen             2.2  Nebenerwerb       Ehemann / Einzelperson / P1                  Hilfsblatt   122\nErwerbstätigkeit am:\nEhefrau / P2                bzw. Aufstellung  123\n3.    Einkünfte aus Sozial- und anderen Versicherungen, Leibrenten\n3.1   AHV- / IV-Renten (100%) Ehemann / Einzelperson / P1           AHV   IV   130\nEhefrau / P2                     AHV  IV   131\nBetrag                    Prozente\n3.2  Renten / Pensionen\nEhemann / Einzelpers./ P1 960                           961                  134\nEhemann / Einzelpers./ P1 962                           963                135\nEhefrau / P2             964                           965                  136\nEhefrau / P2             966                           967                 137\n3.3  Erwerbsausfallentschädigungen aus Arbeitslosenversicherung\n3.3: Direkt ausbezahlte Erwerbs-\nausfallentschädigungen.                   Ehemann / Einzelperson / P1                                    Bescheinigung 140\n3.4: Von Ausgleichskassen direkt                                                                                                                          4 388\nEhefrau / P2                                                   Bescheinigung 141\nausbezahlte Kinder- und Fami-\nlienzulagen, Taggelder aus Kran-     3.4  Kinder- und Familienzulagen, Mutterschaftsentschädigungen, Taggelder\nken-, Unfall- und Invalidenversi-                                                                                 Bescheinigung 142\nEhemann / Einzelperson / P1 \ncherung, aus Militärversicherung \nsowie EO-Entschädigungen inkl.                                                                                                                          4 800\nEhefrau / P2                                                   Bescheinigung 143\nMutterschaftsentschädigungen.\n4.   Wertschriftenertrag\n4.2: Teilsatzverfahren gilt für                                                                                                                                                                  *\n4.1   Ertrag aus Wertschriften, Guthaben und Lotterien       Wertschriftenverzeichnis  150                         15\nqualifizierte Beteiligungen (ohne \nKapitalgewinne).\n4.2  Davon aus qualifizierten Beteiligungen                                       151\n5.1 und 5.2\n5.   Übrige Einkünfte und Gewinne\nName/Adresse Alimentenzahler/in\n5.1   Unterhaltsbeiträge vom geschiedenen / getrennten Ehegatten / Partn.                  160\n5.2  Unterhaltsbeiträge für minderjährige Kinder (bis zum Monat der Volljährigkeit)         161\n5.5: Kapitalleistungen aus Vor-         5.3   Ertrag aus unverteilten Erbschaften, Geschäfts-, Korporationsanteilen     Aufstellung 162\nsorge sind auf Seite 4, Ziffer 40 \n5.4  Weitere Einkünfte, nähere Bezeichnung:                                                     163\neinzutragen. Nähere Bezeichnung:\n5.5   Kapitalabfindungen: wiederkehrende Leistungen für         1641        Monate  164\n6.    Einkünfte aus Liegenschaften\n6.1   Ertrag aus Einfamilienhaus / Stockwerkeigentum:    Wert der Eigennutzung                     25 700   180\nbzw. Mietzinsen                                          181\nBruttoertrag                              25 700   183\n6.2  Abzüglich Unterhalt und Abgaben:                   Pauschal                                                 5 140   184\noder effektive Kosten                                      185\n6.3  Verbleibender Ertrag                                                                  20 560   186                   20 560\n6.4  Ertrag aus anderen Liegenschaften                                                 Liegenschaftenverzeichnis  188\n7.     Total der Einkünfte, zu übertragen auf Seite3, Ziffer 19                                                               199                  188 762\nMustermann Max und Maria, Zürich\nI1BIIIIIIIIII                                                         Seite ^2\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n0106192602761            Seite 2 von 13       20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Einkünfte im In- und Ausland", "Wertschriftenverzeichnis", "I1BIIIIIIIIII", "Seite ^2"], "top_titles": ["Einkünfte im In- und Ausland", "I1BIIIIIIIIII", "Seite ^2"], "titles_with_size": [["Einkünfte im In- und Ausland", 1.7652713052463629], ["Wertschriftenverzeichnis", 1.4026016056658148], ["I1BIIIIIIIIII", 5.537355228399661], ["Seite ^2", 2.0442386957890575]], "top_titles_with_size": [["Einkünfte im In- und Ausland", 1.7652713052463629], ["I1BIIIIIIIIII", 5.537355228399661], ["Seite ^2", 2.0442386957890575]]}, "image": "pageimages/2d89aa3d-ee64-4f27-965e-949fad648391/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/1.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/1.txt", "uuid": "b288feb1-ec75-40ec-a4df-b2b08318033e", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/2d89aa3d-ee64-4f27-965e-949fad648391/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=557b3ec356a98482ae9958c81334a4ddea3cfb6add794e14409e32c80e6885da", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/1.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=658b560aa6d8366daca0c9ceebbaadf3b212c1bd37d1a00df079e574b4900920", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/1.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=a0156d3c50725548831c96ed14196563301f85b550ea6b52bb714085eeb1ac33", "source": null}, "2": {"number": 2, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 130, "name": "TAX_DECLARATION_PAGE_DEDUCTIONS"}, "confidence": 0.*********, "confidence_info": {"parsername": "TaxDeclarationZHDeductionsPageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Abzüge", "confidence": 0.9990065693855286, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Aufstellung/Aufstellungen", "confidence": 0.00023063612752594054, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Deckblatt", "confidence": 0.0001802736078388989, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt", "confidence": 9.90312619251199e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZG/Abzüge", "confidence": 7.834019197616726e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 26, "left": 594, "right": 756, "bottom": 81}, "page_number": 2, "confidence": 0.94, "confidence_summary": {"value": 0.94, "value_formatted": "94%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.94, "confidence_formatted": "94%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 34, "left": 29, "right": 108, "bottom": 59}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 46, "left": 480, "right": 593, "bottom": 80}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_expense_employment", "title": "P1 Berufskosten", "titles": {"de": "P1 Berufskosten", "en": "P1 Employment Expenses", "fr": "P1 Déductions liées a l'activite dependante", "it": "[P1 Employment Expenses]"}, "value": "CHF 7'901", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 295, "left": 2116, "right": 2224, "bottom": 351}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_expense_employment", "title": "P2 Berufskosten", "titles": {"de": "P2 Berufskosten", "en": "P2 Employment Expenses", "fr": "P2 Déductions liées a l'activite dependante", "it": "[P2 Employment Expenses]"}, "value": "CHF 8'850", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 385, "left": 2116, "right": 2230, "bottom": 441}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "interest_paid_on_debt", "title": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "en": "Interest paid on Debt", "fr": "[Interest paid on Debt]", "it": "[Interest paid on Debt]"}, "value": "CHF 11'257", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 470, "left": 2093, "right": 2230, "bottom": 545}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": null, \"context\": \"13. Unterhaltsbeitr\\u00e4ge und Rentenleistungen\\n1\\\"3 .1' Unterhaltsbeitr\\u00e4ge an den geschiedenen oder getrennt lebenden Ehegatten / Partn.\\n(mit der Steuererkl\\u00e4rung 2019 sind alle Belege einzureichen)                              254\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 609, "left": 655, "right": 842, "bottom": 663}, "page_number": 2, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": null, \"context\": \"13. Unterhaltsbeitr\\u00e4ge und Rentenleistungen\\n1\\\"3 .1' Unterhaltsbeitr\\u00e4ge an den geschiedenen oder getrennt lebenden Ehegatten / Partn.\\n(mit der Steuererkl\\u00e4rung 2019 sind alle Belege einzureichen)                              254\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 609, "left": 966, "right": 1107, "bottom": 663}, "page_number": 2, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "p1_contribution_pillar_3a", "title": "P1 Beiträge Säule 3a", "titles": {"de": "P1 Beiträge Säule 3a", "en": "P1 Contribution Pillar 3a", "fr": "[P1 Contribution Pillar 3a]", "it": "[P1 Contribution Pillar 3a]"}, "value": "CHF 3'350", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1026, "left": 2116, "right": 2230, "bottom": 1082}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_contribution_pillar_3a", "title": "P2 Beiträge Säule 3a", "titles": {"de": "P2 Beiträge Säule 3a", "en": "P2 Contribution Pillar 3a", "fr": "[P2 Contribution Pillar 3a]", "it": "[P2 Contribution Pillar 3a]"}, "value": "CHF 6'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1117, "left": 2115, "right": 2230, "bottom": 1173}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "insurance_premiums_and_interest_on_savings_accounts", "title": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "titles": {"de": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "en": "Insurance Premiums and Interest on Savings Accounts", "fr": "[Insurance Premiums and Interest on Savings Accounts]", "it": "[Insurance Premiums and Interest on Savings Accounts]"}, "value": "CHF 7'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1229, "left": 2116, "right": 2230, "bottom": 1282}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "expense_children_daycare", "title": "Fremdbetreuung von Kindern", "titles": {"de": "Fremdbetreuung von Kindern", "en": "Expenses Daycare Children", "fr": "[Expenses Daycare Children]", "it": "[Expenses Daycare Children]"}, "value": "CHF 19'248", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1819, "left": 2093, "right": 2230, "bottom": 1894}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "deductions_total", "title": "Total der Abzüge", "titles": {"de": "Total der Abzüge", "en": "Total Deductions", "fr": "[Total Deductions]", "it": "[Total Deductions]"}, "value": "CHF 71'006", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2112, "left": 2091, "right": 2230, "bottom": 2166}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2345, "left": 2068, "right": 2230, "bottom": 2419}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_net_total", "title": "Total der Einkünfte (netto)", "titles": {"de": "Total der Einkünfte (netto)", "en": "Total Income net", "fr": "[Total Income net]", "it": "[Total Income net]"}, "value": "CHF 117'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2536, "left": 2068, "right": 2230, "bottom": 2610}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_taxable_global", "title": "Steuerbares Einkommen gesamt", "titles": {"de": "Steuerbares Einkommen gesamt", "en": "Taxable Income total", "fr": "[Taxable Income total]", "it": "[Taxable Income total]"}, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3414, "left": 2091, "right": 2230, "bottom": 3488}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_taxable_local", "title": "Steuerbares Einkommen im Kanton", "titles": {"de": "Steuerbares Einkommen im Kanton", "en": "Taxable Income in Canton", "fr": "[Taxable Income in Canton]", "it": "[Taxable Income in Canton]"}, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3757, "left": 2091, "right": 2230, "bottom": 3832}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3837, "left": 269, "right": 2836, "bottom": 4124}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3837, "left": 269, "right": 2836, "bottom": 4124}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 33, "right": 197, "bottom": 1147}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1083, "left": 715, "right": 794, "bottom": 1134}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 2459, "num_chars_alpha": 1886, "num_chars_digit": 511, "num_lines": 51, "plaintext": "Abzüge 2019\nAbzüge                                              u\nK  Staatssteuer                      Bundessteuer\nCHF ohne Rappen                   CHF ohne Rappen\n11. Berufsauslagen bei unselbständiger Erwerbstätigkeit\n11.1 Ehemann / Einzelperson / P1                                    Berufsauslagen  220                       7 901                     7 901 *\n11.2 Ehefrau / P2                                              Berufsauslagen   240                       8 850                    8 850 *\n12. <PERSON><PERSON><PERSON><PERSON><PERSON> (soweit nicht schon unter Ziff. 2 abgezogen)   Schuldenverzeichnis  250                       11 257                     11 257 *\n13. Unterhaltsbeiträge und Rentenleistungen\n1\"3 .1' Unterhaltsbeiträge an den geschiedenen oder getrennt lebenden Ehegatten / Partn.\n(mit der Steuererklärung 2019 sind alle Belege einzureichen)                              254\n13.2 Unterhaltsbeiträge für minderjährige Kinder (bis zum Monat der Volljährigkeit)\n(mit der Steuererklärung 2019 sind alle Belege einzureichen)                                 255\n13.3 Rentenleistungen   CHF 2561                         abzugsfähig: 40%  256\n14. Beiträge an anerkannte Formen der geb. Selbstvorsorge (3. Säule a)\n14.1 Ehemann / Einzelpers./ P1 eff. CHF 262             3 350   Bescheinigung  260                       3 350                    3 350\n14.2 Ehefrau / P2              eff. CHF 263             6 700    Bescheinigung  261                       6 700                    6 700\n15. Versicherungsprämien, Zinsen von Sparkapitalien         Versich.prämien  270                       7 800                   4 900 *\n16. Weitere Abzüge:                                                  Bescheinigung\n16.1 Beiträge an die AHV, IV und 2. Säule, sofern nicht unter Ziff. 1 und 2 abgezogen     280\n16.2 Berufsorientierte Aus- und Weiterbildungskosten                Hilfsblatt   292\n16.3 Kosten für die Verwaltung des beweglichen Privatvermögens                    283\n16.4 Behinderungsbedingte Kosten                                Hilfsblatt 3160\n16.5 Weitere Abzüge (z.B. Beiträge an politische Parteien)                 Aufstellung  284\n16.6 Abzug für fremdbetreute Kinder (Jahrg. 2005-2019)               max. 10'100   376                      19 248                   19 248 *\n17. Sonderabzug bei Erwerbstätigkeit beider Ehegatten/Partn.\nSiehe Wegleitung zur Steuererklärung                                       290                      5 900                    9 379\n18. Total der Abzüge, zu übertragen in Ziffer20                                  299                      71 006                    71 585\nEinkommensberechnung\n19. Total der Einkünfte                        Übertrag von Seite 2, Ziffer 7  199                   188 762                 188 762\n20. Total der Abzüge                               Übertrag von Ziffer 18   299                     71 006                    71 585\n21.  Nettoeinkommen                                                 310                   117 756                 117 177\n22. Zusätzliche Abzüge\n22.1 Krankheits- und Unfallkosten                                 Hilfsblatt   320\n22.2 Gemeinnützige Zuwendungen                            Aufstellung  324\n23.  Reineinkommen                        (Ziffer 21 abzüglich Ziffern 22.1 und 22.2)   350                   117 756                 117 177\n24. Steuerfreie Beträge (Sozialabzüge)          SStaatssteuer © Bundessteuer\n24.1 Abzug für Kinder in Ihrem Haushalt (gemäss Seite 1)         9'000            6'500  370                      18 000                   13 000 *\nAbzug für Kinder ausserhalb Ihres Haushaltes (gem. S. 1)    9'000        6'500  372\n24.2 Abzug für unterstützte Personen        Bestätigung   2'700         6'500  374\n24.3 Abzug für Ehegatten / Partn.                                                       2'600  365                                                 2 600\n25. Steuerbares Einkommen gesamt                     (Ziffer 23 abz. Ziff. 24.1 bis 24.3)   390                     99 756                  101 577\n26. Vom steuerbaren Einkommen gemäss Ziffer 25 entfallen:\n26.1 Auf steuerbare Einkünfte in anderen Kantonen                                394\n26.2 Auf steuerbare Einkünfte im Ausland                                        396\n27.  Steuerbares Einkommen im Kanton Zürich bzw. in der Schweiz            398                     99 756                  101 577\nMustermann Max und Maria, Zürich                   3\nIIBIIIIIIIIII\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321                    Seite\n0106192603761            Seite 3 von 13       20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Abzüge", "Einkommensberechnung", "IIBIIIIIIIIII", "Seite"], "top_titles": ["Abzüge", "Einkommensberechnung", "IIBIIIIIIIIII"], "titles_with_size": [["Abzüge", 1.739355767442696], ["Einkommensberechnung", 1.7436021470587728], ["IIBIIIIIIIIII", 5.483756268384182], ["Seite", 1.479383866242082]], "top_titles_with_size": [["Abzüge", 1.739355767442696], ["Einkommensberechnung", 1.7436021470587728], ["IIBIIIIIIIIII", 5.483756268384182]]}, "image": "pageimages/d55253b8-b7b1-4fa9-bfaf-26bda0a6cbf6/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/2.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/2.txt", "uuid": "5c7457b9-7e92-4b99-998a-89e4823d1991", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/d55253b8-b7b1-4fa9-bfaf-26bda0a6cbf6/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=9b90fd17d1cf78c231846056f6e24333ee304f2ec0a7685db7cccfe15f6aa657", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/2.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=c73615d730999b77b331b519acbabee1c3aa6f2116362eed179540bcb12045bb", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/2.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=862a5bbe7a5c19d787f15705a1c2d8afb8decf0a4872ae031135955617a64d49", "source": null}, "3": {"number": 3, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 140, "name": "TAX_DECLARATION_PAGE_ASSETS"}, "confidence": 0.97727, "confidence_info": {"parsername": "TaxDeclarationZHAssetsPageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Vermögen", "confidence": 0.9989376664161682, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 0.000810145924333483, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SH/Vermögen", "confidence": 8.892193727660924e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Deckblatt", "confidence": 4.986931890016422e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt", "confidence": 3.088490848313086e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "misc_signature", "title": "misc_signature", "titles": {"de": "misc_signature", "en": "[misc_signature]", "fr": "[[misc_signature]]", "it": "[[misc_signature]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 34, "left": 617, "right": 788, "bottom": 73}, "page_number": 3, "confidence": 0.46, "confidence_summary": {"value": 0.46, "value_formatted": "46%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": "confidence is lower than threshold global_settings.PAGE_OBJECT_MIN_VISIBLE_CONFIDENCE=0.8", "confidence_value": 0.46, "confidence_formatted": "46%", "confidence_level": "low"}, {"key": "fh_nutzniessung", "title": "Nutzniessungs-Thema", "titles": {"de": "Nutzniessungs-Thema", "en": "Nutzniessungs-Thema", "fr": "Nutzniessungs-Thema", "it": "Nutzniessungs-Thema"}, "value": "{\"value\": \"Nutzniessung\", \"value_found\": \"Nutzniessung\", \"classifier\": null, \"context\": \"Verm\\u00f6gen im In- und Ausland                                        Steuerwert am 31. Dezember 2019\\nEhemann / Einzelperson / P1, Ehefrau / P2 und minderj\\u00e4hrige <PERSON>er, einschliesslich Nutzniessungsverm\\u00f6gen\\nCHF ohne Rappen\\n30. Bewegliches Verm\\u00f6gen\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 193, "left": 1371, "right": 1580, "bottom": 256}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "assets_portfolio", "title": "Wertschriften und Guthaben", "titles": {"de": "Wertschriften und Guthaben", "en": "Portfolio and Accounts", "fr": "[Portfolio and Accounts]", "it": "[Portfolio and Accounts]"}, "value": "CHF 2'458'532", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 319, "left": 2617, "right": 2865, "bottom": 394}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_cars", "title": "Fahrzeuge", "titles": {"de": "Fahrzeuge", "en": "Vehicles", "fr": "[Vehicles]", "it": "[Vehicles]"}, "value": "CHF 1'040", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 841, "left": 2709, "right": 2820, "bottom": 904}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_other", "title": "Übrige Vermögenswerte", "titles": {"de": "Übrige Vermögenswerte", "en": "Further Assets", "fr": "[Further Assets]", "it": "[Further Assets]"}, "value": "CHF 180'288", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1019, "left": 2658, "right": 2820, "bottom": 1082}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "address_real_estate_primary", "title": "Primärliegenschaft Adresse", "titles": {"de": "Primärliegenschaft Adresse", "en": "Primary Self used Real Estate Address", "fr": "[Primary Self used Real Estate Address]", "it": "[Primary Self used Real Estate Address]"}, "value": "Gemeinde ZH                    Zürich           Strasse Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1240, "left": 238, "right": 1590, "bottom": 1303}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_real_estate_main_property", "title": "Liegenschaft EFH oder Stockwerkeigentum", "titles": {"de": "Liegenschaft EFH oder Stockwerkeigentum", "en": "Real Estate (house or appartment", "fr": "[Real Estate (house or appartment]", "it": "[Real Estate (house or appartment]"}, "value": "CHF 1'172'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1235, "left": 2621, "right": 2820, "bottom": 1298}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_erben", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"erben\", \"value_found\": \"erben\", \"classifier\": null, \"context\": \"30.2 Bargeld, Gold und andere Edelmetalle                                                                   404\\n30.3 Lebens- und Rentenversicherungen (Steuerwert gem. Bescheinigung der Versicherungsges.)\\nVersicherungsgesellschaft                                       Abschlussjahr Ablaufsjahr Steuerwert\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1520, "left": 742, "right": 849, "bottom": 1583}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "assets_gross_total", "title": "Total der Vermögenswerte", "titles": {"de": "Total der Vermögenswerte", "en": "Total Assets Gross", "fr": "Fortune brut", "it": "[Total Assets Gross]"}, "value": "CHF 3'811'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1630, "left": 2619, "right": 2820, "bottom": 1693}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "debt_total", "title": "Total Schulden", "titles": {"de": "Total Schulden", "en": "Total Debt", "fr": "[Total Debt]", "it": "[Total Debt]"}, "value": "CHF 1'135'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1727, "left": 2621, "right": 2865, "bottom": 1790}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_taxable_global", "title": "Steuerbares Vermögen gesamt", "titles": {"de": "Steuerbares Vermögen gesamt", "en": "Taxable Assets (global)", "fr": "Fortune imposable (globale)", "it": "[Taxable Assets (global)]"}, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1832, "left": 2617, "right": 2820, "bottom": 1892}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_taxable_local", "title": "Steuerbares Vermögen im Kanton", "titles": {"de": "Steuerbares Vermögen im Kanton", "en": "Taxable Assets in Canton", "fr": "Fortune imposable (locale)", "it": "[Taxable Assets in Canton]"}, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2182, "left": 2617, "right": 2820, "bottom": 2245}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_schenkung", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"<PERSON>henkung\", \"value_found\": \"Schenkung\", \"classifier\": null, \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 293, "right": 495, "bottom": 2819}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_erbvorbezug", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"Erbvorbezug\", \"value_found\": \"Erbvorbezug\", \"classifier\": null, \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 705, "right": 931, "bottom": 2819}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_erbe", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"Erbe\", \"value_found\": \"Erbe\", \"classifier\": null, \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 1860, "right": 1948, "bottom": 2819}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_erbengemeinschaft", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"Erbengemeinschaft\", \"value_found\": \"Erbengemeinschaft\", \"classifier\": null, \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 1860, "right": 2205, "bottom": 2819}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1086, "left": 33, "right": 198, "bottom": 1147}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3909, "left": 269, "right": 2834, "bottom": 4126}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3909, "left": 269, "right": 2834, "bottom": 4126}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1087, "left": 710, "right": 792, "bottom": 1136}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 2343, "num_chars_alpha": 2014, "num_chars_digit": 282, "num_lines": 47, "plaintext": "Vermögen im In- und Ausland                                        Steuerwert am 31. Dezember 2019\nEhemann / Einzelperson / P1, <PERSON>he<PERSON><PERSON> / P2 und minderj<PERSON><PERSON><PERSON> Kinder, einschliesslich Nutzniessungsvermögen\nCHF ohne Rappen\n30. Bewegliches Vermögen\n30.1 Wertschriften und Guthaben                                       Wertschriftenverzeichnis  400                    2 458 532 *\n30.2 Bargeld, Gold und andere Edelmetalle                                                                   404\n30.3 Lebens- und Rentenversicherungen (Steuerwert gem. Bescheinigung der Versicherungsges.)\nVersicherungsgesellschaft                                       Abschlussjahr Ablaufsjahr Steuerwert\n\n\nTotal                                406\n30.4 Motorfahrzeuge: Mercedes B200 CDI           Kaufpreis:                       33 400 Jahrgang:            2012   412                                 1 040\n30.5 Anteile an unverteilten Erbschaften, Geschäfts- / Korporationsanteile                           Aufstellung 414\n30.6 Übrige Vermögenswerte; nähere Bezeichnung: Kryptowährungen USD 174'591                                  416                      180 288\n31. Lie<PERSON>chaft<PERSON>, Verkehrswert gemäss Neufestsetzung ab 1.1.2009\n31.1 Einfamilienhaus oder Stockwerkeigentum                  Bewertungsdifferenz ausserkantonale Liegenschaften 9020\nGemeinde ZH                    Zürich           Strasse Beispielstrasse 42                                    420                           1 172 000\n31.2 Zum Verkehrswert besteuert                                                                      Liegenschaftenverzeichnis 421\n31.3 Zum Ertragswert besteuert (Land- oder Forstwirtschaft)                                     Liegenschaftenverzeichnis 422\n32. Eigenkapital Selbständigerwerbender ohne Geschäftswertschriften                     Hilfsblatt A  430\n33. Total der Vermögenswerte                                                                       460                     3 811 860\n34.  Schulden                                                                 Schuldenverzeichnis  470                           1 135 000 *\n35. Steuerbares Vermögen gesamt                                                                                        490                    2 676 860\n36. Vom steuerbaren Vermögen gemäss Ziffer 35 entfallen:\n36.1 Auf steuerbare Vermögenswerte in anderen Kantonen                                                     494\n36.2 Auf steuerbare Vermögenswerte im Ausland                                                             496\n37. Steuerbares Vermögen im Kanton Zürich                                                         498                    2 676 860\n\n\nAuszahlungsdatum\nKapitalleistungen im Jahr 2019\nBei mehreren Kapitalleistungen ist eine Aufstellung einzureichen.\n40.  Auszahlung  aus AHV / IV                      aus Einrichtung der beruflichen Vorsorge (2. Säule)                             CHF ohne Rappen\n aus Freizügigkeitskonto / -police   ■ aus anerkannter Form der geb. Selbstvorsorge (3. Säule a)\n510\n infolge Tod oder für bleibende körperliche oder gesundheitliche Nachteile\n50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\n50.1 Am                2019 erhalten von                                        Wert: 516\n50.2 Am                2019 ausgerichtet an                                        Wert: 519\n60. Bemerkungen:\n\n\n\n\n\nBeilagen                                                  Diese Steuererklärung ist vollständig und wahrheitsgetreu ausgefüllt\n PC-Steuererklärung inkl. Barcode-Blatt  ■ Bescheinigungen 3. Säule a\n■ Wertschriftenverzeichnis           Hilfsblatt/ Fragebogen         Ort und Datum\n■ Lohnausweise                Bilanz und Erfolgsrechnung\n■ Berufsausl. / Versicherungsprämien                                      Unterschrift Ehemann / Einzelperson / P1          Unterschrift Ehefrau / P2\nMustermann Max und Maria, Zürich\nIIBIIIIIIIIII                                                     Seite 4\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n0106192604761            Seite 4 von 13       20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Vermögen im In- und Ausland", "Schulden", "Kapitalleistungen im Jahr 2019", "IIBIIIIIIIIII", "Seite 4"], "top_titles": ["Vermögen im In- und Ausland", "Kapitalleistungen im Jahr 2019", "IIBIIIIIIIIII", "Seite 4"], "titles_with_size": [["Vermögen im In- und Ausland", 1.5465515563533911], ["Schulden", 1.3667165579483433], ["Kapitalleistungen im Jahr 2019", 1.5433306367887711], ["IIBIIIIIIIIII", 4.85469980821627], ["Seite 4", 1.779478822223044]], "top_titles_with_size": [["Vermögen im In- und Ausland", 1.5465515563533911], ["Kapitalleistungen im Jahr 2019", 1.5433306367887711], ["IIBIIIIIIIIII", 4.85469980821627], ["Seite 4", 1.779478822223044]]}, "image": "pageimages/e963b24b-923b-45e3-afc0-98b11de5b3bf/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/3.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/3.txt", "uuid": "47570832-d381-46e6-937c-230ef09acaf3", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/e963b24b-923b-45e3-afc0-98b11de5b3bf/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=7373d84e09f98f18d919b583e9cecad354589060e728669c540517ca37d5d7d1", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/3.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=1822a64aeb291b311ed020591847eef7a525cee1bcb14126d3a634a8eb782520", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/3.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=6e39d81877ce61f56f29636d591c30a79faa92100931247cc5e53cba5871c65e", "source": null}, "4": {"number": 4, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 199, "name": "TAX_DECLARATION_MISC"}, "confidence": 0.97775, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Aufstellung/Aufstellungen', meta_json=None, page_cat=None, parser=None), confidence=0.97775)", "spacy_classifications": [{"classification": "DE/310/ZH/Aufstellung/Aufstellungen", "confidence": 0.9999732971191406, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Aufstellung/Aufstellung zu Berufsauslagen", "confidence": 6.404324722097954e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/952/Kinderbetreuung", "confidence": 4.953000370733207e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/TG/<PERSON>aben zu minderjährigen ... Kindern", "confidence": 3.7603940654662438e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SG/Betreuungskosten Kinder", "confidence": 2.341750132472953e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 139, "right": 2853, "bottom": 4125}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 2578, "right": 2853, "bottom": 278}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 33, "left": 31, "right": 136, "bottom": 121}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 37, "left": 712, "right": 800, "bottom": 73}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 33, "right": 198, "bottom": 1146}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 1521, "num_chars_alpha": 1263, "num_chars_digit": 233, "num_lines": 49, "plaintext": "Aufstellungen                 2019\nzur Steuererklärung\nKanton Zürich\nAHVN13 756.4078.6666.31          AHV-Nr.                         Gemeinde Zürich\n13-stellig --------------------------------------------\nName  Mustermann                                         Vorname  Max und Maria\nKinder im Haushalt\nKinder der Jahrgänge 2002-2019 oder in beruflicher Ausbildung stehende Kinder, deren Unterhalt Sie bestreiten\nLeistet der an-\nSchule o. Lehrfirma                       voraussicht-   dere Elternteil \nName                       Geburtsdatum (wenn in Ausbildung)                                          lich bis      Unterhaltsbeit.\n<PERSON>             12.04.2013  Primarschule                           2023       Nein\nAngelo <PERSON>              08.02.2015   Kindergarten                            2021        Nein\nKind: <PERSON>\n16.6\nAufstellung über den Abzug für fremdbetreute Kinder in Ihrem Haushalt\nStaatssteuer Bundessteuer\nDatum     Name                      Empfänger/in                    Betrag CHF Abzug CHF Abzug CHF\nRaphael <PERSON>          31.12.2U19     9 148\n12.04.2013                    Total                                                                     9 148       9 148        9 148\nAngaben zum Sorgerecht/Unterhalt         Schulamt Zürich (Hort)                                     24\nGemeinsames Kind beider Ehepartner                                                                                                    Ja\n\n\n\nKind: <PERSON>\n16.6\nAufstellung über den Abzug für fremdbetreute Kinder in Ihrem Haushalt\nStaatssteuer Bundessteuer\nName                      Datum     Empfänger/in                    Betrag CHF Abzug CHF Abzug CHF\nAngelo Mustermann\n08.02.2015\n31.12.2019   Schulamt Zürich (Hort, Aug - Dez)             3 575\n30.06.2019   Verein Kindertagesstätte Wydäcker          12 010\n(Jan-Jun)\nTotal                                                               15 585     10 100       10 100\nAngaben zum Sorgerecht/Unterhalt                                                        24\nGemeinsames Kind beider Ehepartner                                                                                                    Ja\n1.1\nAufstellung über Einkünfte aus unselbständigem Erwerb \nEhemann/Einzelperson/P1\nDatum von   Datum bis   Arbeitgeber                                                                 Nettolohn CHF\n01.01.2019   31.12.2019  Beispielfirma AG                                                     22 506\nTotal Nettolohn (Übertrag in Ziffer 1.1 der Steuererklärung)                                                                22 506\n1.1\nAufstellung über Einkünfte aus unselbständigem Erwerb \nEhefrau/P2\nMustermann Max und Maria, Zürich\n■llllllllllll\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n4916192601761010          Seite 5 von 13       20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Aufstellungen 2019", "zur Steuererklärung", "Kanton Zürich", "<PERSON>er im <PERSON>", "■llllllllllll"], "top_titles": ["Aufstellungen 2019", "zur Steuererklärung", "■llllllllllll"], "titles_with_size": [["Aufstellungen 2019", 2.446401946032437], ["zur Steuererklärung", 2.3253956526012005], ["Kanton Zürich", 1.108532432062448], ["<PERSON>er im <PERSON>", 1.1635325843466764], ["■llllllllllll", 4.227409211008555]], "top_titles_with_size": [["Aufstellungen 2019", 2.446401946032437], ["zur Steuererklärung", 2.3253956526012005], ["■llllllllllll", 4.227409211008555]]}, "image": "pageimages/8e1f2bc7-8153-4026-bcbf-719480a773f7/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/4.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/4.txt", "uuid": "78b092fc-4831-41b5-bf81-417e420ce82c", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/8e1f2bc7-8153-4026-bcbf-719480a773f7/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=da1aecfc10dd6be61dc6df9ab0538c613f4122e78a63b452f256509882ff7f99", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/4.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=208120d70557665d1f537f7d0b25e124193f198ec0dfd5ff7af97c08388fd2b5", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/4.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=a36ce7b2fd76407ea1f278aea1892fd6c7173e1ef678ff9bc1db1cc1db1dc943", "source": null}, "5": {"number": 5, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 199, "name": "TAX_DECLARATION_MISC"}, "confidence": 0.9777, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Aufstellung/Aufstellungen', meta_json=None, page_cat=None, parser=None), confidence=0.9777)", "spacy_classifications": [{"classification": "DE/310/ZH/Aufstellung/Aufstellungen", "confidence": 0.9999157190322876, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Aufstellung/Aufstellung zu Berufsauslagen", "confidence": 8.319559856317937e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 5.463264756144781e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt Fortsetzung", "confidence": 1.4292474759258766e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/952/Kinderbetreuung", "confidence": 7.5038805391614e-08, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 139, "right": 2853, "bottom": 4125}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 2578, "right": 2853, "bottom": 278}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 31, "left": 31, "right": 136, "bottom": 123}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 37, "left": 712, "right": 800, "bottom": 73}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 30, "right": 199, "bottom": 1145}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 1110, "num_chars_alpha": 860, "num_chars_digit": 236, "num_lines": 31, "plaintext": "Aufstellungen                 2019\nzur Steuererklärung\nKanton Zürich\nAHVN13 756.4078.6666.31          AHV-Nr.                        Gemeinde Zürich\n13-stellig ---------------------------------------------\nName  Mustermann                                         Vorname  Max und Maria\nDatum von   Datum bis   Arbeitgeber                                                                 Nettolohn CHF\n14.01.2019 2288..0022..22001199   XY AG, Zürich                                                                      13 964\n01.04.2019 3311..1122..22001199   ZZZZZ Switzerland GmbH                                                  115 027\nTotal Nettolohn (Übertrag in Ziffer 1.1 der Steuererklärung)                                                               128 991\nAufstellung über Einkünfte aus unselbständigem Nebenerwerb                             1.2\n \nEhemann/Einzelperson/P1\nDatum von   Datum bis   Art                                                                                  Betrag CHF\n01.01.2019    31.12.2019 UVW AG, Verwaltungsrat                                                                                  7 502\nTotal Einkünfte aus unselbständigem Nebenerwerb (Übertrag in Ziffer 1.2 der Steuererklärung)                           7 502\n16.6\nAufstellung über den Abzug für fremdbetreute Kinder in Ihrem Haushalt\nStaatssteuer     Bundessteuer\nName                                   Geburtsdatum     Betrag CHF      Abzug CHF     Abzug CHF\nRaphael Mustermann                           12.04.2013               9 148             9 148             9 148\n16.6\nAufstellung über den Abzug für fremdbetreute Kinder in Ihrem Haushalt\nStaatssteuer     Bundessteuer\nName                                   Geburtsdatum     Betrag CHF      Abzug CHF     Abzug CHF\nAngelo Mustermann                                       08.02.2015             15 585           10 100           10 100\nTotal Abzug fremdbetreute Kinder (Übertrag in Ziffer 16.6)                                             19 248           19 248\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nMustermann Max und Maria, Zürich\n■llllllllllll\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n4916192602761010          Seite 6 von 13       20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Aufstellungen 2019", "zur Steuererklärung", "■llllllllllll"], "top_titles": ["Aufstellungen 2019", "zur Steuererklärung", "■llllllllllll"], "titles_with_size": [["Aufstellungen 2019", 2.368888259019926], ["zur Steuererklärung", 2.251716022363695], ["■llllllllllll", 4.0934647155067605]], "top_titles_with_size": [["Aufstellungen 2019", 2.368888259019926], ["zur Steuererklärung", 2.251716022363695], ["■llllllllllll", 4.0934647155067605]]}, "image": "pageimages/d7c845f0-ad40-40a2-b13c-9f449468642c/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/5.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/5.txt", "uuid": "55d3b0f4-1818-4752-9c5b-2f0edc76e7c3", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/d7c845f0-ad40-40a2-b13c-9f449468642c/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=a086140134015e6e2fad6d19c735e840ffc3ec7fc667fc80c0b2bcd3bfa62c49", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/5.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=6b3909e6c9c365c626a51ea2e9e404b1dc84ab972b8e70fd51fe8e228f97a43b", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/5.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=e0ae5826172f1d151c8a114eae1e5feed347efc8501920b53f3e9a151a8d2782", "source": null}, "6": {"number": 6, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 167, "name": "TAX_DECLARATION_OCCUPATIONAL_EXPENSES_FORM"}, "confidence": 0.97777, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Berufsauslagen', meta_json=None, page_cat=None, parser=None), confidence=0.97777)", "spacy_classifications": [{"classification": "DE/310/ZH/Berufsauslagen", "confidence": 0.9998736381530762, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 4.9868271162267774e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/311/ZH/Provisorische Steuerberechnung (kantonal)", "confidence": 3.342134368722327e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/OW/Berufsauslagen", "confidence": 1.261207762581762e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Berufsorientierte Aus- und Weiterbildungskosten", "confidence": 9.989171303459443e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2828, "bottom": 4126}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1560, "right": 1827, "bottom": 270}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 30, "left": 31, "right": 135, "bottom": 123}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 30, "left": 194, "right": 520, "bottom": 87}, "page_number": 6, "confidence": 0.9, "confidence_summary": {"value": 0.9, "value_formatted": "90%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.9, "confidence_formatted": "90%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 30, "right": 197, "bottom": 1147}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 2973, "num_chars_alpha": 2543, "num_chars_digit": 350, "num_lines": 62, "plaintext": "06  Berufsauslagen 2019\nEhemann / Einzelperson / P1 (Berufskosten Ehefrau / P2 siehe Rückseite)\nKanton Zürich         AHVN13 756.4078.6666.31          AHV-Nr.                          Gemeinde Zürich\n13-stellig \nName  Mustermann                                       Vorname   Max\nEhemann / Einzelperson / P1: Arbeitsort / Strasse Haldenstrasse 20, Zürich\nStaatssteuer                Bundessteuer\nCHF ohne Rappen              CHF ohne Rappen\n1.     Fahrkosten zwischen Wohn- und Arbeitsstätte (bzw. bei auswärtigem Wochenaufenthalt)\n1.1   Abonnementkosten für öffentliche Verkehrsmittel                                                                     201                            201\n1.2   Fahrra<PERSON>, Kleinmotorrad (gelbes Kontrollschild) pauschal CHF 700                               202               700    202             700\n1.3   Auto, Motorrad (weisses Kontrollschild) in der Regel begrenzt auf240 Tage\nEhemann /Einzelperson/PI     Q  AAuutoto: :C CHHFF - .-7.700  pprroo  kkmm  Motorrad: CHF -.40 pro km      geleastes Fahrzeug\nAnzahl          AAnnzzaahhll        FFaahhrtretenn      Anzahl km           Rappen       Abzug CHF \nArbeitsort                 Arbeitstage       kkmm           pprroo T Taagg       pro Jahr               pro km       ohne Rappen\n240                                               } 204 \n204\n240\nZwischentotal                                                                                                   205                700    205              700\n► max. CHF 5'000              ► max. CHF 3'000\n2.   Mehrkosten der Verpflegung\n2.1   bei auswärtiger Verpflegung sofern die Dauer der Arbeitspause die Heimkehr nicht ermöglicht: \nwenn die Verpflegung durch den Arbeitgeber verbilligt wird und dem Arbeitnehmer trotzdem \nMehrkosten entstehen: pro Arbeitstag CHF 7.50 / im Jahr CHF 1'600                                206                         206\nwenn die Verpflegung voll zu Lasten des Arbeitnehmers geht: pro Arbeitstag CHF 15 / im Jahr CHF 3'200        208             3 200    208            3 200\n2.2   bei durchgehender, mindestens achtstündiger Schicht- / Nachtarbeit, \npro ausgewiesenem Schichttag CHF 15 / im Jahr CHF 3'200                                   Anzahl Tage\nEhemann / Einzelperson / P1                                                                          210                         210\n3.   Übrige für die Ausübung des Berufes erforderliche Kosten\npauschal 3% des Nettolohnes gem. Lohnausweis, mind. CHF 2'000, höchstens CHF 4'000   212             2 000    212            2 000\nbzw. effektiv   gemäss Aufstellung                                                   213                         213\n4.   Mehrkosten bei auswärtigem Wochenaufenthalt (gemäss Aufstellung, siehe Wegleitung)               2860                        2860\n5.    Aus- und Weiterbildungskosten\npauschal CHF 500 (sofern keine effektiven Aus- und Weiterbildungskosten in der \nSteuererklärung Seite 3, Ziffer 16.2 zum Abzug gebracht werden)                  214               500    214             500\n6.    Auslagen bei Nebenerwerb\npauschal 20% der Einkünfte aus Nebenerwerb, mind. CHF 800 und höchstens CHF 2'400       216                 1 501    216                1 501\nbzw. effektiv    gemäss Aufstellung                                                      217                         217\n7.    Total der Berufsauslagen                                                            220             7 901    220            7 901\n► Zu übertragen in die          ► Zu übertragen in die\n8.    Begründung für die Benützung eines privaten Motorfahrzeuges für den Arbeitsweg                Steuererklärung            Steuererklärung\n| Seite 3, Ziffer 11.1                   I Seite 3, Ziffer 11.1\nbei unselbständiger Erwerbstätigkeit (Zutreffendes ankreuzen)\nFehlen eines öffentlichen Verkehrsmittels (siehe Wegleitung)                                                                                      2041\nZeitersparnis von über 1 Stunde bei Benützung des privaten Motorfahrzeuges                                                        2042\nStändige Benützung während der Arbeitszeit auf Verlangen und gegen Entschädigung des Arbeitgebers                                 2043\nUnmöglichkeit der Benützung des öffentl. Verkehrsmittels zufolge Krankheit / Gebrechlichkeit (Arztzeugnis beilegen)                                     2044\nZu deklarierendes Einkommen bei Besitz eines Geschäftsfahrzeuges und unentgeltlicher Beförderung an den Arbeitsplatz\nAnzahl Arbeitstage Anzahl km Fahrten AAnnzzaahhll  kkmm                      Rappen            Einkommen CHF\nArbeitsort                                (ohne Aussendiensttätigkeit)                         pro Tag pprroo  JJaahhrr                           pro km              ohne Rappen\n240                                                                          } 104\n240\n► Zu übertragen in die\nQ Auto: CHF -.70 pro km     | | Motorrad: CHF -.40 pro km                                                      Steuererklärung \nSeite 2, Ziffer 5.4 \nsowie in Ziffer 1.3 \ndieses Formulars \nMustermann Max und Maria, Zürich\nIlBliiiiiiiii\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n1066192601761            Seite 7 von 13       20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Berufsauslagen 2019", "Kanton Zürich", "IlBliiiiiiiii"], "top_titles": ["Berufsauslagen 2019", "IlBliiiiiiiii"], "titles_with_size": [["Berufsauslagen 2019", 3.4102149372970665], ["Kanton Zürich", 1.2530552585765928], ["IlBliiiiiiiii", 5.256891508963511]], "top_titles_with_size": [["Berufsauslagen 2019", 3.4102149372970665], ["IlBliiiiiiiii", 5.256891508963511]]}, "image": "pageimages/ae701e25-32d4-4deb-8295-fddfdeea0e3b/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/6.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/6.txt", "uuid": "3490c0be-ef3e-41ec-b2d3-4f0ab455407a", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/ae701e25-32d4-4deb-8295-fddfdeea0e3b/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=df17760710098acbede0e504c36b2ab9a06a3889d7c88d3874db912fd201f9dd", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/6.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=98a2a4654dac5df3388743ebf94236209312d06a175546c0b201793c342f5b5f", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/6.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=777b1c5408043ecffb23a34555c8d2526a1285b0d109454c0c7f51962c5dce31", "source": null}, "7": {"number": 7, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 167, "name": "TAX_DECLARATION_OCCUPATIONAL_EXPENSES_FORM"}, "confidence": 0.97763, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Berufsauslagen', meta_json=None, page_cat=None, parser=None), confidence=0.97763)", "spacy_classifications": [{"classification": "DE/310/ZH/Berufsauslagen", "confidence": 0.9998682737350464, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SZ/Berufsauslagen", "confidence": 3.210130307707004e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 2.3776914531481452e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/311/ZH/Provisorische Steuerberechnung (kantonal)", "confidence": 2.2873298803460784e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/OW/Berufsauslagen", "confidence": 1.1204620022908784e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2828, "bottom": 4126}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1560, "right": 1827, "bottom": 270}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 31, "left": 31, "right": 135, "bottom": 123}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 30, "left": 193, "right": 514, "bottom": 87}, "page_number": 7, "confidence": 0.76, "confidence_summary": {"value": 0.76, "value_formatted": "76%", "color": "orange"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.76, "confidence_formatted": "76%", "confidence_level": "medium"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1086, "left": 30, "right": 197, "bottom": 1147}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 2842, "num_chars_alpha": 2428, "num_chars_digit": 331, "num_lines": 58, "plaintext": "06  Berufsauslagen 2019\nEhefrau / P2 (Berufskosten Einzelperson / Ehemann / P1 siehe Rückseite)\nKanton Zürich         Name  Mustermann                                       Vorname   Maria\nEhefrau / P2:                  Arbeitsort / Strasse Dingsstrasse 4, 8050 Zürich\n\n\nStaatssteuer                Bundessteuer\nCHF ohne Rappen             CHF ohne Rappen\n1.     Fahrkosten zwischen Wohn- und Arbeitsstätte (bzw. bei auswärtigem Wochenaufenthalt)\n1.1   Abonnementkosten für öffentliche Verkehrsmittel                                                                     221                   1 150    221                 1 150\n1.2   <PERSON>ah<PERSON><PERSON>, Kleinmotorrad (gelbes Kontrollschild) pauschal CHF 700                               222                          222\n1.3   Auto, Motorrad (weisses Kontrollschild) in der Regel begrenzt auf240 Tage\nEhefrau/P2            O Auto: CHF -.70 pro km     | | Motorrad: CHF -.40 pro km  □ geleastes Fahrzeug\nAnzahl            Anzahl         Fahrten     Anzahl km          Rappen      Abzug CHF \nArbeitsort                   Arbeitstage      km        pro Tag     pro Jahr              pro km      ohne Rappen\n240                                                    } 224\n224\n240\nZwischentotal                                                                                                   225                   1 150    225                1 150\n► max. CHF 5'000              ► max. CHF 3'000\n2.   Mehrkosten der Verpflegung\n2.1   bei auswärtiger Verpflegung sofern die Dauer der Arbeitspause die Heimkehr nicht ermöglicht: \nwenn die Verpflegung durch den Arbeitgeber verbilligt wird und dem Arbeitnehmer trotzdem \nMehrkosten entstehen: pro Arbeitstag CHF 7.50 / im Jahr CHF 1'600                                226                         226\nwenn die Verpflegung voll zu Lasten des Arbeitnehmers geht: pro Arbeitstag CHF 15 / im Jahr CHF 3'200        228             3 200    228            3 200\n2.2   bei durchgehender, mindestens achtstündiger Schicht- / Nachtarbeit, \npro ausgewiesenem Schichttag CHF 15 / im Jahr CHF 3'200                                   Anzahl Tage\nEhefrau / P2                                                                                     230                         230\n3.   Übrige für die Ausübung des Berufes erforderliche Kosten\npauschal 3% des Nettolohnes gem. Lohnausweis, mind. CHF 2'000, höchstens CHF 4'000   232             4 000    232            4 000\nbzw. effektiv   gemäss Aufstellung                                                   233                         233\n4.   Mehrkosten bei auswärtigem Wochenaufenthalt (gemäss Aufstellung, siehe Wegleitung)              2861                         2861\n5.    Aus- und Weiterbildungskosten\npauschal CHF 500 (sofern keine effektiven Aus- und Weiterbildungskosten in der \nSteuererklärung Seite 3, Ziffer 16.2 zum Abzug gebracht werden)                  234               500    234             500\n6.    Auslagen bei Nebenerwerb\npauschal 20% der Einkünfte aus Nebenerwerb, mind. CHF 800 und höchstens CHF 2'400       236                         236\nbzw. effektiv    gemäss Aufstellung                                                      237                         237\n7.    Total der Berufsauslagen                                                            240             8 850    240            8 850\n► Zu übertragen in die          ► Zu übertragen in die\nBegründung für die Benützung eines privaten Motorfahrzeuges für den Arbeitsweg                Steuererklärung            Steuererklärung\n8.                                                                                                                                                                                    | Seite 3, Ziffer 11.2                   | Seite 3, Ziffer 11.2\nbei unselbständiger Erwerbstätigkeit (Zutreffendes ankreuzen)\nFehlen eines öffentlichen Verkehrsmittels (siehe Wegleitung)                                                                                      2241     □\nZeitersparnis von über 1 Stunde bei Benützung des privaten Motorfahrzeuges                                                        2242    □\nStändige Benützung während der Arbeitszeit auf Verlangen und gegen Entschädigung des Arbeitgebers                                 2243\nUnmöglichkeit der Benützung des öffentl. Verkehrsmittels zufolge Krankheit / Gebrechlichkeit (Arztzeugnis beilegen)                                     2244    □\nZu deklarierendes Einkommen bei Besitz eines Geschäftsfahrzeuges und unentgeltlicher Beförderung an den Arbeitsplatz\nAnzahl Arbeitstage Anzahl km Fahrten Anzahl km                   Rappen           Einkommen CHF\nArbeitsort                                (ohne Aussendiensttätigkeit)                       pro Tag pro Jahr                         pro km             ohne Rappen\n240                                                                          } 105\n240                                                                                    ► Zu übertragen in die\nSteuererklärung \nO Auto: CHF -.70 pro km     | | Motorrad: CHF -.40 pro km                                                        Seite 2, Ziffer 5.4 \nsowie in Ziffer 1.3 \ndieses Formulars\nIllllllllllll    Mustermann Max und Maria, Zürich\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n1076192601761            Seite 8 von 13       20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Berufsauslagen 2019", "Kanton Zürich"], "top_titles": ["Berufsauslagen 2019"], "titles_with_size": [["Berufsauslagen 2019", 3.2746492623123795], ["Kanton Zürich", 1.203242773133465]], "top_titles_with_size": [["Berufsauslagen 2019", 3.2746492623123795]]}, "image": "pageimages/e65d3451-fa15-4899-a3df-655654fd9a5c/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/7.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/7.txt", "uuid": "6411d3a1-cc9e-4d44-b6fd-ee7a29754168", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/e65d3451-fa15-4899-a3df-655654fd9a5c/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=f533c0093779d1d551e7011673a3bc20817ef90100fcbe3b3f2b9979bce7e6f9", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/7.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=2d1700ab2525086d1cceb0031103e7cd79c5e57849393e2fe4aa326020c7b400", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/7.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=f4f466c79102c88ac6cb58a2b21b73302b5a35cfcb74f52814371701efa08d10", "source": null}, "8": {"number": 8, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 150, "name": "TAX_DECLARATION_DEBT_INVENTORY"}, "confidence": 0.97778, "confidence_info": {"parsername": "TaxDeclarationZHDebtInventoryPageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Schuldenverzeichnis", "confidence": 0.9999966621398926, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Beiblatt zum Liegenschaftenverzeichnis", "confidence": 1.6702687162251095e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Berufsorientierte Aus- und Weiterbildungskosten", "confidence": 5.166530172573403e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Liegenschaftenerträge und Unterhaltskosten", "confidence": 5.039026973463478e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/729/Kündigung Hypothekarvertrag", "confidence": 2.924207649357413e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 31, "left": 31, "right": 134, "bottom": 122}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 32, "left": 190, "right": 508, "bottom": 77}, "page_number": 8, "confidence": 0.92, "confidence_summary": {"value": 0.92, "value_formatted": "92%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.92, "confidence_formatted": "92%", "confidence_level": "high"}, {"key": "debt_detail_lines", "title": "Schulden Details", "titles": {"de": "Schulden Details", "en": "Debt Details", "fr": "[Debt Details]", "it": "[Debt Details]"}, "value": "Credit Suisse, 8070 Zürich, Fix-Hypothek                                                                1 135 000                    11 257", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 926, "left": 142, "right": 2812, "bottom": 1001}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "debt_total", "title": "Total Schulden", "titles": {"de": "Total Schulden", "en": "Total Debt", "fr": "[Total Debt]", "it": "[Total Debt]"}, "value": "CHF 1'135'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3589, "left": 2047, "right": 2247, "bottom": 3656}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "interest_paid_on_debt", "title": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "en": "Interest paid on Debt", "fr": "[Interest paid on Debt]", "it": "[Interest paid on Debt]"}, "value": "CHF 11'257", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3583, "left": 2652, "right": 2788, "bottom": 3658}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3805, "left": 139, "right": 2827, "bottom": 4127}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3805, "left": 139, "right": 2827, "bottom": 4127}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 31, "right": 198, "bottom": 1147}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 629, "num_chars_alpha": 489, "num_chars_digit": 125, "num_lines": 15, "plaintext": "06  Schuldenverzeichnis\nKanton Zürich\nAHVN13 Zürich  756.4078.6666.31  AHV-Nr.                       Gemeinde \n13-stellig\n3299       2019 ◄Jahr    Name  Mustermann                                       Vorname  Max und Maria\n\n\n\nPrivatschulden inkl. Grundpfandschulden                                          Schuld am 31. Dezember              Schuld<PERSON>sen\nName, Vorname und Adresse des Gläubigers                                                            CHF ohne Rappen                  CHF ohne Rappen\nCredit Suisse, 8070 Zürich, Fix-Hypothek                                                                1 135 000                    11 257\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTotal Privatschulden / private Schuldzinsen                             3200 / 3201                           1 135 000                   11 257\n► Zu übertragen in die                 ► Zu übertragen in die\n|Steuererklärung Seite 4, <PERSON><PERSON><PERSON> 34        | S teuererklärung Seite 3, <PERSON><PERSON><PERSON> 12\nIIUlllllllllll    Mustermann Max und Maria, Zürich\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n2006162601761            Seite 9 von 13       20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Schuldenverzeichnis", "IIUlllllllllll"], "top_titles": ["Schuldenverzeichnis", "IIUlllllllllll"], "titles_with_size": [["Schuldenverzeichnis", 2.****************], ["IIUlllllllllll", 4.280765308936478]], "top_titles_with_size": [["Schuldenverzeichnis", 2.****************], ["IIUlllllllllll", 4.280765308936478]]}, "image": "pageimages/b9d46a39-334c-4bbf-9c95-da5f2f5be037/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/8.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/8.txt", "uuid": "*************-4f3b-a174-4b4cf160fa37", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/b9d46a39-334c-4bbf-9c95-da5f2f5be037/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=66fc4538026d117f1ad491592a9342ae9a180bdf8d2c9e3c673b2b53e451c522", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/8.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=8aeef08fd229d1071cd689a9956129b939f8eb926ed87b4e9ee7ca1ed12e4b28", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/8.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=56745a9dd8b8e792e29b22ad25e969525d86c7328085daf4c16458604c252817", "source": null}, "9": {"number": 9, "lang": "de", "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "page_category": {"id": 166, "name": "TAX_DECLARATION_INSURANCE_PREMIUMS"}, "confidence": 0.97757, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Versicherungsprämien', meta_json=None, page_cat=None, parser=None), confidence=0.97757)", "spacy_classifications": [{"classification": "DE/310/ZH/Versicherungsprämien", "confidence": 0.9997884631156921, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Schuldenverzeichnis", "confidence": 0.0001509400608483702, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/440/Vorsorgepolice 3/AXA", "confidence": 1.589584644534625e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Liegenschaftenerträge und Unterhaltskosten", "confidence": 1.2910507393826265e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SZ/Versicherungsprämien und Zinsen von Sparkapitalien", "confidence": 9.800565749173984e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2827, "bottom": 4127}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1942, "right": 2209, "bottom": 270}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 32, "left": 31, "right": 136, "bottom": 123}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 570, "left": 684, "right": 776, "bottom": 601}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 918, "left": 687, "right": 777, "bottom": 946}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 31, "right": 198, "bottom": 1147}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 1748, "num_chars_alpha": 1475, "num_chars_digit": 235, "num_lines": 42, "plaintext": "06  Versicherungsprämien 2019\nKanton Zürich         AHVN13 756.4078.6666.31          AHV-Nr.                          Gemeinde Zürich\n13-stellig \nName  Mustermann                                       Vorname    Max und Maria\n\n\n\n\nA.  Bezahlte Versicherungsprämien und <PERSON><PERSON><PERSON> von Sparkapitalien                CHF ohne Rappen\n1. Private Krankenversicherungsprämien                                                601              9 749\n2. Private Unfallversicherungsprämien                                             602\n3. Private Lebens- und Rentenversicherungsprämien                                   603\n4. <PERSON><PERSON><PERSON> von Sparkapitalien                                                        604\nDie individuelle Prämienverbilli-\ngung wird in der Regel mit den              5. Zwischentotal                                                                                                607             9 749\nPrämien Ihres Krankenversiche-\nrers verrechnet. In diesem Fall tra-          6. Abzüglich erhaltene Prämienverbilligungen                                         605\ngen Sie die um die Prämienverbil-\n(soweit nicht schon unter Ziffer 1. berücksichtigt)\nligung reduzierten Kranken-\nversicherungsprämien ein.                       Total bezahlte Versicherungsprämien und Zinsen von Sparkapitalien                (A)   606             9 749\n\n\nB. Maximaler Abzug für Versicherungsprämien und Zinsen von Sparkapitalien\nStaatssteuer          Bundessteuer\n1.    Für Verheiratete\ndie Beiträge an die 2. oder 3. Säule a geleistet haben                  t 5200     3'500\n611           5 200             3 500\noder: sofern weder Beiträge an die 2. noch an die 3. Säule a geleistet wurden      t 7'800  ® 5'250\n\n\n2.    Übrige Steuerpflichtige\ndie Beiträge an die 2. oder 3. Säule a geleistet haben                S 2'600  g 1'700\n612\noder: sofern weder Beiträge an die 2. noch an die 3. Säule a geleistet wurden      t 3'900  © 2'550\n\n\n3.    Zusätzlicher Abzug für Kinder und unterstützungsbedürftige Personen\nZusätzlicher Abzug für jedes Kind                   Anzahl:         2  t 1'300  © 700 613          2 600                1 400\nZusätzlicher Abzug für jede unterstützungsbed. Person   Anzahl:           W 1'300           614\nZusätzlicher Abzug für jede unterstützungsbed. Person   Anzahl:                    © 700 615\nTotal der Abzüge für Versicherungsprämien und Zinsen von Sparkapitalien                     (B) 616          7 800            4 900\n\n\nStaatssteuer          Bundessteuer\nC. Abzug\nDer niedrigere Betrag: (A) oder (B)                                    270          7 800            4 900\n► Zu übertragen in die   ► Zu übertragen in die\nSteuererklärung        I Steuererklärung\nSeite 3, Ziffer 15         | Seite 3, Ziffer 15\n□Qllllll   Mustermann Max und Maria, Zürich\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n1636192601761            Seite 10 von 13     20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Versicherungsprämien 2019", "Kanton Zürich", "A. Bezahlte Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "B. Maximaler Abzug für Versicherungsprämien und Z<PERSON><PERSON> von Sparkapitalien", "<PERSON><PERSON>", "□Qllllll"], "top_titles": ["Versicherungsprämien 2019", "□Qllllll"], "titles_with_size": [["Versicherungsprämien 2019", 3.1095300598210778], ["Kanton Zürich", 1.1403471729281045], ["A. Bezahlte Versicherungsprämien und Z<PERSON>en von Sparkapitalien", 1.1470684423876938], ["B. Maximaler Abzug für Versicherungsprämien und Z<PERSON><PERSON> von Sparkapitalien", 1.196046986037692], ["<PERSON><PERSON>", 1.1443355981567827], ["□Qllllll", 4.787209674091791]], "top_titles_with_size": [["Versicherungsprämien 2019", 3.1095300598210778], ["□Qllllll", 4.787209674091791]]}, "image": "pageimages/7e3aeeb8-d784-4301-9847-538477caa3f1/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/9.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/9.txt", "uuid": "662deef5-f4fc-498d-9a70-f5bbaa06b71e", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/7e3aeeb8-d784-4301-9847-538477caa3f1/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=d7e4048d137e63572f15f3befbaa51c63871f80ea9973204da28cf0697f0c7d0", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/9.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=56f2fc9a82038bc7fe422dd748bad46aecff6720819d5891179fef7908de4aa3", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/9.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=ec49164ac6b02a93e571b23e13ff23e1b24fba55b47980d1657d0d96eced7604", "source": null}, "10": {"number": 10, "lang": "de", "document_category": {"id": "316", "name": "TAX_LIST_FINANCIAL_ASSETS"}, "page_category": {"id": 160, "name": "TAX_DECLARATION_ACCOUNTS_FORM_FRONT"}, "confidence": 0.97747, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Wertschriftenverzeichnis Front', meta_json=None, page_cat=None, parser=None), confidence=0.97747)", "spacy_classifications": [{"classification": "DE/310/ZH/Wertschriftenverzeichnis Front", "confidence": 0.****************, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Wertschriftenverzeichnis Details/Rechte Seite (Zugang | Abgang)", "confidence": 3.64458196600026e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/311/ZH/Provisorische Steuerberechnung (kantonal)", "confidence": 2.2151830307848286e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Wertschriftenverzeichnis Details/Ganze Seite & Linke Seite", "confidence": 1.5345234487540438e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/LU/Deckblatt", "confidence": 1.1075338761656894e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2856, "bottom": 4127}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 31, "left": 32, "right": 133, "bottom": 116}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 218, "left": 2586, "right": 2856, "bottom": 409}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 67, "left": 713, "right": 798, "bottom": 110}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 33, "right": 197, "bottom": 1148}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 1466, "num_chars_alpha": 1272, "num_chars_digit": 175, "num_lines": 48, "plaintext": "Wertschriften- und Guthabenverzeichnis\n06\n2019\nKanton Zürich\nWertschriften- und \nGuthabenverzeichnis                                                                                                  Zürich                 261\nmit Verrechnungsantrag\n756.4078.6666.31\n<PERSON><PERSON><PERSON> Max\nMustermann Maria\nBeispielstrasse 42\n8050 Zürich                                            04.09.1967\n21.10.1976 \nInformatiker \nSolution Consultant\nverheiratet\n\n\n\n\n\n\nRückerstattung der Verrechnungssteuer\nEin allfälliges Guthaben - nach Verrechnung mit den Staats- und Gemeindesteuern - ist wie folgt zu überweisen:\n►  IBAN-Nr.            CH66 0070 0112 8000 7777 3\nIhr Konto\nfür die Rückerstattung \nKonto lautend auf   Max Mustermann\neines allfälligen Guthabens.\n\n\n\n\n► IBAN-Nr.                       2010  CH66 0070 0112 8000 7777 3\nSollten diese Angaben nicht mehr \naktuell sein, bitten wir Sie, Ihre \nKonto lautend auf            549Max Mustermann\nKorrekturen hier einzutragen.\n\n\n\n\n\n\nEingang                                                              Bitte leer lassen\nVerrechnung mit Staats-                   Bruttoertrag 2019         Steuerwert am 31.12.2019        Verrechnungsanspruch\nund Gemeindesteuern 2019\nCode                                                                                         Ihr Verrechnungssteueranspruch                0.00\ndavon\nTotal Ertrag aus qualifizierter    Wertschriftenertrag                             15\nDatum:                                        Beteiligung\nCHF                  Wertschriften und Guthaben             2 458 532\nSteuerkom.:\nIch bestätige die Richtigkeit und Vollständigkeit der in diesem Verzeichnis und Antrag gemachten Angaben, insbesondere, dass auf \nBeilagen                   allen unter Kolonne A angegebenen Erträgen die eidg. Verrechnungssteuer zu meinen oder zu Lasten der von mir vertretenen Steuer-\npflichtigen abgezogen worden ist.\nEf Beiblätter\nn Formular DA-1 (KoPie)\n□. Bankabrechnungen, Lotto- \nund Toto-Abrechnungen    Ort und Datum              Unterschrift Ehemann / Einzelperson / P1      Unterschrift Ehefrau / P2\nIlffllllllllll    Mustermann Max und Maria, Zürich\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n1006192601761            Seite 11 von 13     20.04.2020 17:11                       unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Wertschriften- und Guthabenverzeichnis", "Kanton Zürich", "n Formular DA-1 (<PERSON><PERSON><PERSON>)", "Ilffllllllllll"], "top_titles": ["Wertschriften- und Guthabenverzeichnis", "Ilffllllllllll"], "titles_with_size": [["Wertschriften- und Guthabenverzeichnis", 3.****************], ["Kanton Zürich", 1.****************], ["n Formular DA-1 (<PERSON><PERSON><PERSON>)", 1.****************], ["Ilffllllllllll", 5.***************]], "top_titles_with_size": [["Wertschriften- und Guthabenverzeichnis", 3.****************], ["Ilffllllllllll", 5.***************]]}, "image": "pageimages/6d48eb86-c05d-49cc-b730-ee02220368ac/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/10.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/10.txt", "uuid": "6c540c81-f507-41f5-a471-cd104740d5e5", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/6d48eb86-c05d-49cc-b730-ee02220368ac/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=67f8923ca6883213133ee5b7bce907abc88f17eabf4ae2c4d9815616bd61e615", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/10.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=5fde71fbc8e4b8fd88dec4cb37719a86cee81add9dde52214c20db722091bc8f", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/10.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=13be271d7fafb9fb8b8e044e11aee4df2873c7fc74f831656774050d35ce7e79", "source": null}, "11": {"number": 11, "lang": "de", "document_category": {"id": "316", "name": "TAX_LIST_FINANCIAL_ASSETS"}, "page_category": {"id": 161, "name": "TAX_DECLARATION_ACCOUNTS_FORM_DETAILS"}, "confidence": 0.97777, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Wertschriftenverzeichnis Details/Ganze Seite & Linke Seite', meta_json=None, page_cat=None, parser=None), confidence=0.97777)", "spacy_classifications": [{"classification": "DE/310/ZH/Wertschriftenverzeichnis Details/Ganze Seite & Linke Seite", "confidence": 0.****************, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Wertschriftenverzeichnis Front", "confidence": 0.00023302054614759982, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Schuldenverzeichnis", "confidence": 8.785539830569178e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Wertschriftenverzeichnis Details/Rechte Seite (Zugang | Abgang)", "confidence": 6.467847560998052e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Versicherungsprämien", "confidence": 3.5787928936770186e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 118, "left": 139, "right": 4062, "bottom": 2898}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 291, "left": 3604, "right": 3686, "bottom": 354}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_da<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"<PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON><PERSON>\", \"classifier\": null, \"context\": \"1. Kundenguthaben deren Bruttozins CHF 200.-nicht \\u00fcbersteigt                                                   \\u00dcbertrag Bruttoertrag A in Kolonne Bruttoertrag B                                                 l       +                  539\\n2. <PERSON><PERSON><PERSON>, Ko<PERSON> und Guthaben aller Art ohne Verrechnungssteuerabzug\\n3. Ausl\\u00e4ndische Wertschriften aller Art                                                                                                                                                                                                                                                                              I\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2217, "left": 184, "right": 301, "bottom": 2271}, "page_number": 11, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_nutzniessung", "title": "Nutzniessungs-Thema", "titles": {"de": "Nutzniessungs-Thema", "en": "Nutzniessungs-Thema", "fr": "Nutzniessungs-Thema", "it": "Nutzniessungs-Thema"}, "value": "{\"value\": \"Nutzniessung\", \"value_found\": \"Nutzniessung\", \"classifier\": null, \"context\": \"5. Anteile an Stockwerkeigent\\u00fcmergemeinschaft                                                                                                                                                                                                                                                                \\n* Code: G Gesch\\u00e4ftsverm\\u00f6gen            N Nutzniessungsverm\\u00f6gen    Q Qualifizierte Beteiligung ^                                                                              \\u25ba Zu \\u00fcbertragen in die\\nIhr Verrechnungssteueranspruch                                  davon 35% '            Steuererkl\\u00e4rung\\nE Wertschriften aus Erbschaften      S Schenkung\\n35% von Total Bruttoertrag A                                                                       | Seite 2, <PERSON><PERSON><PERSON> 4.1\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2396, "left": 846, "right": 1030, "bottom": 2449}, "page_number": 11, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_schenkung", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"<PERSON><PERSON>kung\", \"value_found\": \"<PERSON>henkung\", \"classifier\": null, \"context\": \"* Code: G Gesch\\u00e4ftsverm\\u00f6gen            N Nutzniessungsverm\\u00f6gen    Q Qualifizierte Beteiligung ^                                                                              \\u25ba Zu \\u00fcbertragen in die\\nIhr Verrechnungssteueranspruch                                  davon 35% '            Steuererkl\\u00e4rung\\nE Wertschriften aus Erbschaften      S Schenkung\\n35% von Total Bruttoertrag A                            0.00                                    | Seite 2, <PERSON><PERSON><PERSON> 4.1\\n540\\n151\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2435, "left": 846, "right": 995, "bottom": 2489}, "page_number": 11, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 744, "left": 33, "right": 193, "bottom": 802}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 2311, "num_chars_alpha": 1813, "num_chars_digit": 430, "num_lines": 46, "plaintext": "Wertschriften- und Guthabenverzeichnis\nNennwert      Valoren-Nr.      Genaue Bezeichnung der Vermögenswerte                                Zugang    Abgang         Steuerwert                   Bruttoertrag 2019\nStückzahl                                       (bei Konto inkl. <PERSON><PERSON><PERSON>, bei nichtkotierten Wertpapieren inkl. UID)                       2019      2019         am 31.12.2019\nWerte mit        Werte ohne \n■ CD                                                                                                                                                Kauf         Verkauf                                   Verrechnungs-      Verrechnungs-\n*    « =\nEröffnung   Saldierung                          steuerabzug       steuerabzug\nCD -C\no  o S                                                                     Datum    Datum     CHF ohne Rappen    CHF ohne Rappen    CHF ohne Rappen\n2300-001     CHF                            1128-0051.777, <PERSON><PERSON><PERSON>, CH, Privatkonto (Lohnkonto etc.)                                                                              1 687 725                                    2309-001\n2300-002    CHF                            1100-7777.999, ZKB, CH, Privatkonto (Lohnkonto etc.)                                                       561 883                                  2309-002\n2300-003    CHF                            1100-1234.567, <PERSON><PERSON><PERSON>, CH, Sparkonto (Sparheft)                                                                  6 528                                  2309-003\n2300-004     USD                                                                                                            19 621                                    2309-004\n2300-005    CHF                                                                                                                  5 634                                            1   2309-005\n2300-006    CHF                                                                                                                 20 006                                 4  2309-006\n2300-007    CHF                                                                                                                  9 119                                  10  2309-007\n2300-008    CHF                                                                                                                    944                                  2309-008\n2300-009     USD                                                                                                        45 794                                    2309-009\n2300-010    CHF                               Liegenschaft SampleHaus, Erneuerungsfonds Stockwerkeigentümer, CH, Stockwerke gentum                       21 282                                  2309-010\n2300-011                  50 000                                                                                                        50 000                                  2309-011\n2300-012                        8                                                                                 27.11.                               29 996                                  2309-012\nBemerkungen                                                                Übertrag aus Beiblättern                      2350                                                     2352\nÜbertrag ab Formular DA-1                    2370                                                       2371\nA Werte mitVerrechnungssteuerabzug, deren Erträge um 35% eidg. Verrechnungssteuer gekürzt wurden,             Total Steuerwert                                   400        2 458 532\ngeordnet nach folgenden Gruppen (Reihenfolge analog Vorperiode):\n1. Spar-, Privat-, Anlage-, Salär-, Postkonti, Kontokorrente etc.                                                                                                                                                               ► Zu übertragen in die\n2. Inländische Aktien, Anlagefonds, Obligationen und Wertschriften aller Art mit Verrechnungssteuerabzug                                                                                                                                    i Steuererklärung\n3. Gewinne aus inländischen Grossspielen und aus Online-Spielbankenspielen über CHF 1 '000'000                                                        |Seite 4, Ziffer 30.1\n(Originalbescheinigungen sind beizulegen)\nB Werte ohne Verrechnungssteuerabzug, deren Erträge nicht um 35% eidg. Verrechnungssteuer gekürzt              Zwischentotal Bruttoerträge                                                                           15    542\nwurden, geordnet nach folgenden Gruppen (Reihenfolge analog Vorperiode):\n1. Kundenguthaben deren Bruttozins CHF 200.-nicht übersteigt                                                   Übertrag Bruttoertrag A in Kolonne Bruttoertrag B                                                 l       +                  539\n2. Darlehen, Konti und Guthaben aller Art ohne Verrechnungssteuerabzug\n3. Ausländische Wertschriften aller Art                                                                                                                                                                                                                                                                              I\n4. Gewinne aus ausländischen Spielbanken, Lotterien und Sportwetten sowie Naturaltreffer                                    Total Bruttoertrag A+B                                                                                 15    150\n5. Anteile an Stockwerkeigentümergemeinschaft                                                                                                                                                                                                                                                                I\n* Code: G Geschäftsvermögen            N Nutzniessungsvermögen    Q Qualifizierte Beteiligung ^                                                                              ► Zu übertragen in die\nIhr Verrechnungssteueranspruch                                  davon 35% '            Steuererklärung\nE Wertschriften aus Erbschaften      S Schenkung\n35% von Total Bruttoertrag A                            0.00                                    | Seite 2, Ziffer 4.1\n540\n151\n^ITIIIIIII      Mustermann Max und Maria, Zürich\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n1006192602761            Seite 12 von 13      20.04.2020 17:11                                                                             unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": ["Wertschriften- und Guthabenverzeichnis", "Zugang ", "o S", "Zwischentotal Bruttoerträge", "davon 35% '", "^ITIIIIIII"], "top_titles": ["^ITIIIIIII"], "titles_with_size": [["Wertschriften- und Guthabenverzeichnis", 1.2900709683821743], ["Zugang ", 1.3518079786413058], ["o S", 1.216673607210191], ["Zwischentotal Bruttoerträge", 1.292253126901566], ["davon 35% '", 1.40813312410913], ["^ITIIIIIII", 4.999227440804697]], "top_titles_with_size": [["^ITIIIIIII", 4.999227440804697]]}, "image": "pageimages/c9d582f2-2700-49aa-9945-c6ba4e75e3bd/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/11.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/11.txt", "uuid": "3fb02c24-0a43-4529-939c-cc9a409e0d58", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/c9d582f2-2700-49aa-9945-c6ba4e75e3bd/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=c25e8ea3891a9b6569dcfc6fab746650c7a9470b70f2bdd6db091809b77f3995", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/11.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=1abf47c940f894a3247606a46d5e8830127f2b58baa74e3604f03c5e18b521fb", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/11.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=995516b3aeaced4b4ce690ea72e6a64e24a3427a1008729d67384ab4785f0315", "source": null}, "12": {"number": 12, "lang": "de", "document_category": {"id": "316", "name": "TAX_LIST_FINANCIAL_ASSETS"}, "page_category": {"id": 161, "name": "TAX_DECLARATION_ACCOUNTS_FORM_DETAILS"}, "confidence": 0.97725, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Wertschriftenverzeichnis Details Beiblatt', meta_json=None, page_cat=None, parser=None), confidence=0.97725)", "spacy_classifications": [{"classification": "DE/310/ZH/Wertschriftenverzeichnis Details Beiblatt", "confidence": 0.****************, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/CH/Steuerausscheidung", "confidence": 0.00021327173453755677, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt Fortsetzung", "confidence": 0.0001081037480616942, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/LU/Wertschriftennverzeichnis Details", "confidence": 5.**************8e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Berufsorientierte Aus- und Weiterbildungskosten", "confidence": 4.461951175471768e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 60, "left": 137, "right": 4062, "bottom": 2898}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 34, "left": 33, "right": 119, "bottom": 93}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 744, "left": 32, "right": 192, "bottom": 804}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": null, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "text_content_stats": {"num_chars": 730, "num_chars_alpha": 576, "num_chars_digit": 139, "num_lines": 19, "plaintext": "06             i      . <PERSON><PERSON><PERSON> zum Wertschriften- und Guthabenverzeichnis\n6191\nKanton Zürich                    AHVN13 756.4078.6666.31         AHV-Nr.                              Gemeinde Zürich\n13-stellig\n6199       2019◄-Jahr      Name  Mustermann                                        VornameMax\nNennwert      Valoren-Nr.      Genaue Bezeichnung der Vermögenswerte                                Zugang     Abgang         Steuerwert                   Bruttoertrag 2019\nStückzahl                                       (bei Konto inkl. <PERSON><PERSON>mer, bei nichtkotierten Wertpapieren inkl. UID)                       2019      2019         am 31.12.2019\nWerte mit        Werte ohne \n■ CD                                                                                                                                                            Kauf         Verkauf                                   Verrechnungs-      Verrechnungs-\n*     75 =\nEröffnung   Saldierung                          steuerabzug       steuerabzug\n—CD  -ijCo \no  O S                                                                     Datum    Datum     CHF ohne Rappen    CHF ohne Rappen    CHF ohne Rappen\n2300-013                                     27.11.2019, 8 Zugang, Kauf                                                                                                                                            2309-013\n2350                                     Total (Übertrag in Wertschriftenverzeichnis)                                                                                                                   2352\n\n\n\n\n\n\nJlllll    Mustermann Max und Maria, Zürich\n756.4078.6666.31\nZHprivateTax Version 2.0.0 / FK-Version 2019-7abd4-0d321\n1016172601761            Seite 13 von 13      20.04.2020 17:11                                                                             unverbindlicher Einzelblattausdruck"}, "page_layout_info": {"titles": [". Be<PERSON><PERSON> zum Wertschriften- und Guthabenverzeichnis", "Jlllll"], "top_titles": [". Be<PERSON><PERSON> zum Wertschriften- und Guthabenverzeichnis", "Jlllll"], "titles_with_size": [[". Be<PERSON><PERSON> zum Wertschriften- und Guthabenverzeichnis", 1.5237477692105619], ["Jlllll", 4.2659291725753]], "top_titles_with_size": [[". Be<PERSON><PERSON> zum Wertschriften- und Guthabenverzeichnis", 1.5237477692105619], ["Jlllll", 4.2659291725753]]}, "image": "pageimages/4f1f7ef6-**************-8cf7777f48ca/0.jpg", "searchable_pdf": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/12.pdf", "searchable_txt": "2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/12.txt", "uuid": "25f85ea9-6810-460a-a298-6fd749d1722d", "image_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/pageimages/4f1f7ef6-**************-8cf7777f48ca/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=37bb175f7660fe8195f5dccc85ea62d0a5203c41b1c19b19b1cf1507f1ae6b29", "searchable_pdf_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/12.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=cdb9573fa07ce1474ae36f67544fc40b577321f177ad61e51024ac5c29c835fc", "searchable_txt_file_url": "https://minio.hypo.duckdns.org/dp-processing/2987e5a4-b616-4ebf-8815-b3eb3c16ea81/searchable_pages/TAX_max_maria_2019_4.pdf/12.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20220614%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20220614T124809Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=572216a3873ce3deffd05f7408992f2d5afb52fa1a33718f0b840c1f39abb66e", "source": null}}, "classifications": {"FileTextSpacyClassifier": [{"classification": "DE/310/ZH/Deckblatt", "confidence": 0.5468324422836304, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 0.39141857624053955, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Einkünfte", "confidence": 0.026292800903320312, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Vermögen", "confidence": 0.012181513011455536, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt", "confidence": 0.010984695516526699, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "extracted_file_uuid": "595a1b1c-7f43-4d33-9d13-80665c7bfb8f"}, "semantic_documents": [{"filename": "310 Steuererklärung Mustermann Max ZH 2019.pdf", "filename_offline_version": "310 Steuererklärung Mustermann Max ZH 2019.pdf", "title": "310 Steuererklärung Mustermann Max ZH 2019", "title_elements": ["<PERSON><PERSON><PERSON>", "ZH", "2019"], "title_suffix": "Mustermann Max ZH 2019", "confidence_summary": {"value": 1.0, "value_formatted": "100%", "color": "green"}, "confidence": 1.0, "confidence_info": {"page_confidences": [0.97755, 0.*********, 0.*********, 0.97727, 0.97775, 0.9777, 0.97777, 0.97763, 0.97778, 0.97757, 0.97747, 0.97777, 0.97725]}, "semantic_pages": [{"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 0, "page_category": {"id": 110, "name": "TAX_DECLARATION_PAGE_PERSONAL_DATA"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.97755, "value_formatted": "98%", "color": "green"}, "confidence": 0.97755, "confidence_info": {"parsername": "TaxDeclarationZHPersonalDataPageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Deckblatt", "confidence": 0.9997667670249939, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 8.745212107896805e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Antrag auf Neuveranlagung", "confidence": 5.461315595312044e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Vermögen", "confidence": 2.0401219444465823e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/FR/Personalien", "confidence": 1.934035390149802e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2855, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2387, "bottom": 414}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 633, "left": 2047, "right": 2407, "bottom": 703}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_fullname", "title": "P1 Name", "titles": {"de": "P1 Name", "en": "P1 Name", "fr": "P1 Nom", "it": "[P1 Name]"}, "value": "<PERSON><PERSON><PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 723, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "address_block", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Address", "fr": "[Address]", "it": "[Address]"}, "value": "<PERSON><PERSON><PERSON> Maria\nBe<PERSON>pielstrasse 42\n8055 Zürich", "type": "ADDRESS_BLOCK", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 781, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "street", "title": "Strasse", "titles": {"de": "Strasse", "en": "Street", "fr": "[Street]", "it": "[Street]"}, "value": "Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 848, "left": 816, "right": 1183, "bottom": 911}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "zip", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "Zip Code", "fr": "[Zip Code]", "it": "[Zip Code]"}, "value": "8055", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 815, "right": 920, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "city", "title": "Ort", "titles": {"de": "Ort", "en": "City", "fr": "[City]", "it": "[City]"}, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 925, "right": 1054, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_date_of_birth", "title": "P1 Geburtsdatum", "titles": {"de": "P1 Geburtsdatum", "en": "P1 Date of Birth", "fr": "P1 Date de naissance", "it": "[P1 Date of Birth]"}, "value": "04.09.1967", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 964, "right": 1191, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_date_of_birth", "title": "P2 Geburtsdatum", "titles": {"de": "P2 Geburtsdatum", "en": "P2 Date of Birth", "fr": "P2 Date de naissance", "it": "[P2 Date of Birth]"}, "value": "21.10.1976", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 2040, "right": 2267, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_marital_status", "title": "P1 Zivilstand", "titles": {"de": "P1 Zivilstand", "en": "P1 Civil Status", "fr": "P1 Etat civil", "it": "[P1 Civil Status]"}, "value": "verheiratet", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1834, "left": 963, "right": 1181, "bottom": 1897}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_firstname", "title": "P2 Vorname", "titles": {"de": "P2 Vorname", "en": "P2 Lastname", "fr": "P2 Prénom", "it": "[P2 Lastname]"}, "value": "<PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1831, "left": 2043, "right": 2156, "bottom": 1894}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Wegleitung verwendeten Begriffe     Zivilstand      verheiratet                                   Vorname    Maria\\nwie verheiratet, getrennt, ge-       Konfession     keine                                         Konfession     keine\\nschieden, verwitwet oder <PERSON>he,       Beruf             Informatiker                                Beruf          Solution Consultant\"}", "type": "FIN_HURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1897, "left": 388, "right": 520, "bottom": 1960}, "page_number": 0, "confidence": 0.08460009843111038, "confidence_summary": {"value": 0.08460009843111038, "value_formatted": "8%", "color": "red"}, "source": 2, "visible": false, "invisibility_reason": "Financial hurdle confidence is 8% but must be at least 10% to be visible", "confidence_value": 0.08460009843111038, "confidence_formatted": "8%", "confidence_level": "low"}, {"key": "p1_profession", "title": "P1 Beruf", "titles": {"de": "P1 Beruf", "en": "P1 Profession", "fr": "P1 Profession", "it": "[P1 Profession]"}, "value": "Informatiker", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 966, "right": 1207, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_profession", "title": "P2 Beruf", "titles": {"de": "P2 Beruf", "en": "P2 Profession", "fr": "P2 Profession", "it": "[P2 Profession]"}, "value": "Solution Consultant", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 2041, "right": 2435, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_employer", "title": "P1 Arbeitgeber", "titles": {"de": "P1 Arbeitgeber", "en": "P1 Employer", "fr": "P1 Employeur", "it": "[P1 Employer]"}, "value": "Beispielfirma AG", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 965, "right": 1300, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_employer", "title": "P2 Arbeitgeber", "titles": {"de": "P2 Arbeitgeber", "en": "P2 Employer", "fr": "P2 Employeur", "it": "[P2 Employer]"}, "value": "ServiceFirma", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 2041, "right": 2308, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_employer_location", "title": "P1 Arbeitsort", "titles": {"de": "P1 Arbeitsort", "en": "P1 Place of work", "fr": "P1 Lieu de travail", "it": "[P1 Place of work]"}, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2074, "left": 963, "right": 1090, "bottom": 2137}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "phone_primary", "title": "Telefonnummer", "titles": {"de": "Telefonnummer", "en": "Phone number", "fr": "[Phone number]", "it": "[Phone number]"}, "value": "0763331234      P 07633", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2127, "left": 964, "right": 1539, "bottom": 2197}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "phone_secondary", "title": "Telefonnummer (2. Prio)", "titles": {"de": "Telefonnummer (2. Prio)", "en": "Phone number (2nd Priority)", "fr": "[Phone number (2nd Priority)]", "it": "[Phone number (2nd Priority)]"}, "value": "Telefon G       0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 1769, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_phone_primary", "title": "P2 Telefonnummer", "titles": {"de": "P2 Telefonnummer", "en": "P2 Phone number", "fr": "P2 Téléphone", "it": "[P2 Phone number]"}, "value": "0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 2041, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "section_children", "title": "Kinder", "titles": {"de": "Kinder", "en": "Children", "fr": "<PERSON><PERSON><PERSON>", "it": "[Children]"}, "value": "<PERSON>", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2534, "left": 137, "right": 572, "bottom": 2656}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"wie verheiratet, getrennt, ge-       Konfession     keine                                         Konfession     keine\\nschieden, verwitwet oder Ehe,       Beruf             Informatiker                                Beruf          Solution Consultant\\nEhegatten, Ehemann und Ehe-     Arbeitgeber \\nBeispielfirma AG                          Arbeitgeber   ServiceFirma\"}", "type": "FIN_HURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2569, "left": 2687, "right": 2815, "bottom": 2615}, "page_number": 0, "confidence": 0.17452068626880646, "confidence_summary": {"value": 0.17452068626880646, "value_formatted": "17%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.17452068626880646, "confidence_formatted": "17%", "confidence_level": "low"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3799, "left": 139, "right": 2828, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97755, "confidence_formatted": "98%", "confidence_level": "high", "number": 0, "processed_page_uuid": "6c2bd5ca-2e77-408e-afaf-f20d4cd50f4a"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 1, "page_category": {"id": 120, "name": "TAX_DECLARATION_PAGE_INCOME"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.*********, "value_formatted": "99%", "color": "green"}, "confidence": 0.*********, "confidence_info": {"parsername": "TaxDeclarationZHIncomePageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Einkünfte", "confidence": 0.9998792409896851, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SH/Einkünfte", "confidence": 4.8135261749848723e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/BS/Details zu den Krankheitskosten", "confidence": 1.2898743989353534e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/OW/Einkünfte", "confidence": 1.0976544217555784e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt", "confidence": 7.870098670537118e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "p1_income_employed_main", "title": "P1 Einkommen Haupterwerb", "titles": {"de": "P1 Einkommen Haupterwerb", "en": "P1 Main Income", "fr": "P1 Revenu net dépendante", "it": "[P1 Main Income]"}, "value": "CHF 22'506", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 280, "left": 2662, "right": 2802, "bottom": 336}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_income_employed_main", "title": "P2 Einkommen Haupterwerb", "titles": {"de": "P2 Einkommen Haupterwerb", "en": "P2 Main Income", "fr": "P2 Revenu net dépendante", "it": "[P2 Main Income]"}, "value": "CHF 128'991", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 360, "left": 2640, "right": 2796, "bottom": 427}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_income_employed_side", "title": "P1 Einkommen Nebenverdienst", "titles": {"de": "P1 Einkommen Nebenverdienst", "en": "P1 Additional Income", "fr": "[P1 Additional Income]", "it": "[P1 Additional Income]"}, "value": "CHF 7'502", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 459, "left": 2688, "right": 2802, "bottom": 514}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_gewerbe", "title": "Gewerbe", "titles": {"de": "Gewerbe", "en": "Gewerbe", "fr": "Gewerbe", "it": "Gewerbe"}, "value": "{\"value\": \"<PERSON><PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON><PERSON><PERSON>\", \"classifier\": null, \"context\": \"Verwaltungsrats- und Vorstands-\\nhon<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, \\n2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 652, "left": 1731, "right": 1888, "bottom": 708}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_landwirtschaft", "title": "Landwirtschaft", "titles": {"de": "Landwirtschaft", "en": "Landwirtschaft", "fr": "Landwirtschaft", "it": "Landwirtschaft"}, "value": "{\"value\": \"Landwirtschaft\", \"value_found\": \"Landwirtschaft\", \"classifier\": null, \"context\": \"2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\\n2: Inklusive Ertr\\u00e4ge aus                 2.1  Haupterwerb           Ehemann / Einzelperson / P1                     Hilfsblatt   120\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 719, "left": 1343, "right": 1605, "bottom": 775}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_arbeitslos", "title": "arbeitslos", "titles": {"de": "arbeitslos", "en": "arbeitslos", "fr": "arbeitslos", "it": "arbeitslos"}, "value": "{\"value\": \"arbeitslos\", \"value_found\": \"Arbeitslos\", \"classifier\": null, \"context\": \"3.3  Erwerbsausfallentsch\\u00e4digungen aus Arbeitslosenversicherung\\n3.3: Direkt ausbezahlte Erwerbs-\\nausfallentsch\\u00e4digungen.                   Ehemann / Einzelperson / P1                                    Bescheinigung 140\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1801, "left": 1441, "right": 1624, "bottom": 1857}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "p2_income_eo", "title": "P2 Einkünfte Erwerbsausfallentschädigung", "titles": {"de": "P2 Einkünfte Erwerbsausfallentschädigung", "en": "P2 Income EO", "fr": "[P2 Income EO]", "it": "[P2 Income EO]"}, "value": "CHF 4'388", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1939, "left": 2686, "right": 2802, "bottom": 2006}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_income_child_benefits", "title": "P2 Einkünfte Kinder- und Familienzulagen", "titles": {"de": "P2 Einkünfte Kinder- und Familienzulagen", "en": "P2 Income Child Benefits", "fr": "[P2 Income Child Benefits]", "it": "[P2 Income Child Benefits]"}, "value": "CHF 4'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2190, "left": 2686, "right": 2802, "bottom": 2244}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_portfolio", "title": "Wertschriftenerträge", "titles": {"de": "Wertschriftenerträge", "en": "Income Portfolio", "fr": "Revenue mobilier", "it": "[Income Portfolio]"}, "value": "CHF 15", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2341, "left": 2753, "right": 2803, "bottom": 2408}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_alimente", "title": "Alimente", "titles": {"de": "Alimente", "en": "Alimente", "fr": "Alimente", "it": "Alimente"}, "value": "{\"value\": \"Ali<PERSON><PERSON>\", \"value_found\": \"Alimente\", \"classifier\": null, \"context\": \"5.1 und 5.2\\n5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2566, "left": 366, "right": 497, "bottom": 2620}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": null, \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1194, "right": 1387, "bottom": 2665}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": null, \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1449, "right": 1593, "bottom": 2665}, "page_number": 1, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "property_imputed_rental_value", "title": "Liegenschaft Eigenmietwert", "titles": {"de": "Liegenschaft Eigenmietwert", "en": "Imputed Rental Value", "fr": "Valeur locative", "it": "Valore locativo"}, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3111, "left": 1971, "right": 2110, "bottom": 3165}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_real_estate_gross", "title": "Liegenschaftenertrag brutto", "titles": {"de": "Liegenschaftenertrag brutto", "en": "Income Real Estate gross", "fr": "Revenu brut immobilier", "it": "[Income Real Estate gross]"}, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3305, "left": 1971, "right": 2110, "bottom": 3358}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "property_maintenance_cost", "title": "Liegenschaft Unterhaltskosten", "titles": {"de": "Liegenschaft Unterhaltskosten", "en": "Maintenance Cost", "fr": "Charges et frais d'entretien d'immeuble", "it": "[Maintenance Cost]"}, "value": "CHF 5'140", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3397, "left": 1996, "right": 2110, "bottom": 3451}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_real_estate_net_primary", "title": "Primärliegenschaft Ertrag netto", "titles": {"de": "Primärliegenschaft Ertrag netto", "en": "Primary Self used Real Estate Income net", "fr": "[Primary Self used Real Estate Income net]", "it": "[Primary Self used Real Estate Income net]"}, "value": "CHF 20'560", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3588, "left": 2662, "right": 2802, "bottom": 3642}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3765, "left": 2640, "right": 2802, "bottom": 3833}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.*********, "confidence_formatted": "99%", "confidence_level": "high", "number": 1, "processed_page_uuid": "b288feb1-ec75-40ec-a4df-b2b08318033e"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 2, "page_category": {"id": 130, "name": "TAX_DECLARATION_PAGE_DEDUCTIONS"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.*********, "value_formatted": "99%", "color": "green"}, "confidence": 0.*********, "confidence_info": {"parsername": "TaxDeclarationZHDeductionsPageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Abzüge", "confidence": 0.9990065693855286, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Aufstellung/Aufstellungen", "confidence": 0.00023063612752594054, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Deckblatt", "confidence": 0.0001802736078388989, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt", "confidence": 9.90312619251199e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZG/Abzüge", "confidence": 7.834019197616726e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "p1_expense_employment", "title": "P1 Berufskosten", "titles": {"de": "P1 Berufskosten", "en": "P1 Employment Expenses", "fr": "P1 Déductions liées a l'activite dependante", "it": "[P1 Employment Expenses]"}, "value": "CHF 7'901", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 295, "left": 2116, "right": 2224, "bottom": 351}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_expense_employment", "title": "P2 Berufskosten", "titles": {"de": "P2 Berufskosten", "en": "P2 Employment Expenses", "fr": "P2 Déductions liées a l'activite dependante", "it": "[P2 Employment Expenses]"}, "value": "CHF 8'850", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 385, "left": 2116, "right": 2230, "bottom": 441}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "interest_paid_on_debt", "title": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "en": "Interest paid on Debt", "fr": "[Interest paid on Debt]", "it": "[Interest paid on Debt]"}, "value": "CHF 11'257", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 470, "left": 2093, "right": 2230, "bottom": 545}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": null, \"context\": \"13. Unterhaltsbeitr\\u00e4ge und Rentenleistungen\\n1\\\"3 .1' Unterhaltsbeitr\\u00e4ge an den geschiedenen oder getrennt lebenden Ehegatten / Partn.\\n(mit der Steuererkl\\u00e4rung 2019 sind alle Belege einzureichen)                              254\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 609, "left": 655, "right": 842, "bottom": 663}, "page_number": 2, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": null, \"context\": \"13. Unterhaltsbeitr\\u00e4ge und Rentenleistungen\\n1\\\"3 .1' Unterhaltsbeitr\\u00e4ge an den geschiedenen oder getrennt lebenden Ehegatten / Partn.\\n(mit der Steuererkl\\u00e4rung 2019 sind alle Belege einzureichen)                              254\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 609, "left": 966, "right": 1107, "bottom": 663}, "page_number": 2, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "p1_contribution_pillar_3a", "title": "P1 Beiträge Säule 3a", "titles": {"de": "P1 Beiträge Säule 3a", "en": "P1 Contribution Pillar 3a", "fr": "[P1 Contribution Pillar 3a]", "it": "[P1 Contribution Pillar 3a]"}, "value": "CHF 3'350", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1026, "left": 2116, "right": 2230, "bottom": 1082}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_contribution_pillar_3a", "title": "P2 Beiträge Säule 3a", "titles": {"de": "P2 Beiträge Säule 3a", "en": "P2 Contribution Pillar 3a", "fr": "[P2 Contribution Pillar 3a]", "it": "[P2 Contribution Pillar 3a]"}, "value": "CHF 6'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1117, "left": 2115, "right": 2230, "bottom": 1173}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "insurance_premiums_and_interest_on_savings_accounts", "title": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "titles": {"de": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "en": "Insurance Premiums and Interest on Savings Accounts", "fr": "[Insurance Premiums and Interest on Savings Accounts]", "it": "[Insurance Premiums and Interest on Savings Accounts]"}, "value": "CHF 7'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1229, "left": 2116, "right": 2230, "bottom": 1282}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "expense_children_daycare", "title": "Fremdbetreuung von Kindern", "titles": {"de": "Fremdbetreuung von Kindern", "en": "Expenses Daycare Children", "fr": "[Expenses Daycare Children]", "it": "[Expenses Daycare Children]"}, "value": "CHF 19'248", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1819, "left": 2093, "right": 2230, "bottom": 1894}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "deductions_total", "title": "Total der Abzüge", "titles": {"de": "Total der Abzüge", "en": "Total Deductions", "fr": "[Total Deductions]", "it": "[Total Deductions]"}, "value": "CHF 71'006", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2112, "left": 2091, "right": 2230, "bottom": 2166}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2345, "left": 2068, "right": 2230, "bottom": 2419}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_net_total", "title": "Total der Einkünfte (netto)", "titles": {"de": "Total der Einkünfte (netto)", "en": "Total Income net", "fr": "[Total Income net]", "it": "[Total Income net]"}, "value": "CHF 117'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2536, "left": 2068, "right": 2230, "bottom": 2610}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_taxable_global", "title": "Steuerbares Einkommen gesamt", "titles": {"de": "Steuerbares Einkommen gesamt", "en": "Taxable Income total", "fr": "[Taxable Income total]", "it": "[Taxable Income total]"}, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3414, "left": 2091, "right": 2230, "bottom": 3488}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_taxable_local", "title": "Steuerbares Einkommen im Kanton", "titles": {"de": "Steuerbares Einkommen im Kanton", "en": "Taxable Income in Canton", "fr": "[Taxable Income in Canton]", "it": "[Taxable Income in Canton]"}, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3757, "left": 2091, "right": 2230, "bottom": 3832}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3837, "left": 269, "right": 2836, "bottom": 4124}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3837, "left": 269, "right": 2836, "bottom": 4124}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.*********, "confidence_formatted": "99%", "confidence_level": "high", "number": 2, "processed_page_uuid": "5c7457b9-7e92-4b99-998a-89e4823d1991"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 3, "page_category": {"id": 140, "name": "TAX_DECLARATION_PAGE_ASSETS"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.97727, "value_formatted": "98%", "color": "green"}, "confidence": 0.97727, "confidence_info": {"parsername": "TaxDeclarationZHAssetsPageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Vermögen", "confidence": 0.9989376664161682, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 0.000810145924333483, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SH/Vermögen", "confidence": 8.892193727660924e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Deckblatt", "confidence": 4.986931890016422e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt", "confidence": 3.088490848313086e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "fh_nutzniessung", "title": "Nutzniessungs-Thema", "titles": {"de": "Nutzniessungs-Thema", "en": "Nutzniessungs-Thema", "fr": "Nutzniessungs-Thema", "it": "Nutzniessungs-Thema"}, "value": "{\"value\": \"Nutzniessung\", \"value_found\": \"Nutzniessung\", \"classifier\": null, \"context\": \"Verm\\u00f6gen im In- und Ausland                                        Steuerwert am 31. Dezember 2019\\nEhemann / Einzelperson / P1, Ehefrau / P2 und minderj\\u00e4hrige <PERSON>er, einschliesslich Nutzniessungsverm\\u00f6gen\\nCHF ohne Rappen\\n30. Bewegliches Verm\\u00f6gen\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 193, "left": 1371, "right": 1580, "bottom": 256}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "assets_portfolio", "title": "Wertschriften und Guthaben", "titles": {"de": "Wertschriften und Guthaben", "en": "Portfolio and Accounts", "fr": "[Portfolio and Accounts]", "it": "[Portfolio and Accounts]"}, "value": "CHF 2'458'532", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 319, "left": 2617, "right": 2865, "bottom": 394}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_cars", "title": "Fahrzeuge", "titles": {"de": "Fahrzeuge", "en": "Vehicles", "fr": "[Vehicles]", "it": "[Vehicles]"}, "value": "CHF 1'040", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 841, "left": 2709, "right": 2820, "bottom": 904}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_other", "title": "Übrige Vermögenswerte", "titles": {"de": "Übrige Vermögenswerte", "en": "Further Assets", "fr": "[Further Assets]", "it": "[Further Assets]"}, "value": "CHF 180'288", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1019, "left": 2658, "right": 2820, "bottom": 1082}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "address_real_estate_primary", "title": "Primärliegenschaft Adresse", "titles": {"de": "Primärliegenschaft Adresse", "en": "Primary Self used Real Estate Address", "fr": "[Primary Self used Real Estate Address]", "it": "[Primary Self used Real Estate Address]"}, "value": "Gemeinde ZH                    Zürich           Strasse Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1240, "left": 238, "right": 1590, "bottom": 1303}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_real_estate_main_property", "title": "Liegenschaft EFH oder Stockwerkeigentum", "titles": {"de": "Liegenschaft EFH oder Stockwerkeigentum", "en": "Real Estate (house or appartment", "fr": "[Real Estate (house or appartment]", "it": "[Real Estate (house or appartment]"}, "value": "CHF 1'172'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1235, "left": 2621, "right": 2820, "bottom": 1298}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_erben", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"erben\", \"value_found\": \"erben\", \"classifier\": null, \"context\": \"30.2 Bargeld, Gold und andere Edelmetalle                                                                   404\\n30.3 Lebens- und Rentenversicherungen (Steuerwert gem. Bescheinigung der Versicherungsges.)\\nVersicherungsgesellschaft                                       Abschlussjahr Ablaufsjahr Steuerwert\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1520, "left": 742, "right": 849, "bottom": 1583}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "assets_gross_total", "title": "Total der Vermögenswerte", "titles": {"de": "Total der Vermögenswerte", "en": "Total Assets Gross", "fr": "Fortune brut", "it": "[Total Assets Gross]"}, "value": "CHF 3'811'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1630, "left": 2619, "right": 2820, "bottom": 1693}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "debt_total", "title": "Total Schulden", "titles": {"de": "Total Schulden", "en": "Total Debt", "fr": "[Total Debt]", "it": "[Total Debt]"}, "value": "CHF 1'135'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1727, "left": 2621, "right": 2865, "bottom": 1790}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_taxable_global", "title": "Steuerbares Vermögen gesamt", "titles": {"de": "Steuerbares Vermögen gesamt", "en": "Taxable Assets (global)", "fr": "Fortune imposable (globale)", "it": "[Taxable Assets (global)]"}, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1832, "left": 2617, "right": 2820, "bottom": 1892}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_taxable_local", "title": "Steuerbares Vermögen im Kanton", "titles": {"de": "Steuerbares Vermögen im Kanton", "en": "Taxable Assets in Canton", "fr": "Fortune imposable (locale)", "it": "[Taxable Assets in Canton]"}, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2182, "left": 2617, "right": 2820, "bottom": 2245}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_schenkung", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"<PERSON>henkung\", \"value_found\": \"Schenkung\", \"classifier\": null, \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 293, "right": 495, "bottom": 2819}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_erbvorbezug", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"Erbvorbezug\", \"value_found\": \"Erbvorbezug\", \"classifier\": null, \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 705, "right": 931, "bottom": 2819}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_erbe", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"Erbe\", \"value_found\": \"Erbe\", \"classifier\": null, \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 1860, "right": 1948, "bottom": 2819}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_erbengemeinschaft", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"Erbengemeinschaft\", \"value_found\": \"Erbengemeinschaft\", \"classifier\": null, \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 1860, "right": 2205, "bottom": 2819}, "page_number": 3, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3909, "left": 269, "right": 2834, "bottom": 4126}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3909, "left": 269, "right": 2834, "bottom": 4126}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97727, "confidence_formatted": "98%", "confidence_level": "high", "number": 3, "processed_page_uuid": "47570832-d381-46e6-937c-230ef09acaf3"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 4, "page_category": {"id": 199, "name": "TAX_DECLARATION_MISC"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.97775, "value_formatted": "98%", "color": "green"}, "confidence": 0.97775, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Aufstellung/Aufstellungen', meta_json=None, page_cat=None, parser=None), confidence=0.97775)", "spacy_classifications": [{"classification": "DE/310/ZH/Aufstellung/Aufstellungen", "confidence": 0.9999732971191406, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Aufstellung/Aufstellung zu Berufsauslagen", "confidence": 6.404324722097954e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/952/Kinderbetreuung", "confidence": 4.953000370733207e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/TG/<PERSON>aben zu minderjährigen ... Kindern", "confidence": 3.7603940654662438e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SG/Betreuungskosten Kinder", "confidence": 2.341750132472953e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 139, "right": 2853, "bottom": 4125}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 2578, "right": 2853, "bottom": 278}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97775, "confidence_formatted": "98%", "confidence_level": "high", "number": 4, "processed_page_uuid": "78b092fc-4831-41b5-bf81-417e420ce82c"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 5, "page_category": {"id": 199, "name": "TAX_DECLARATION_MISC"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.9777, "value_formatted": "98%", "color": "green"}, "confidence": 0.9777, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Aufstellung/Aufstellungen', meta_json=None, page_cat=None, parser=None), confidence=0.9777)", "spacy_classifications": [{"classification": "DE/310/ZH/Aufstellung/Aufstellungen", "confidence": 0.9999157190322876, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Aufstellung/Aufstellung zu Berufsauslagen", "confidence": 8.319559856317937e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 5.463264756144781e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt Fortsetzung", "confidence": 1.4292474759258766e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/952/Kinderbetreuung", "confidence": 7.5038805391614e-08, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 139, "right": 2853, "bottom": 4125}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 2578, "right": 2853, "bottom": 278}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.9777, "confidence_formatted": "98%", "confidence_level": "high", "number": 5, "processed_page_uuid": "55d3b0f4-1818-4752-9c5b-2f0edc76e7c3"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 6, "page_category": {"id": 167, "name": "TAX_DECLARATION_OCCUPATIONAL_EXPENSES_FORM"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.97777, "value_formatted": "98%", "color": "green"}, "confidence": 0.97777, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Berufsauslagen', meta_json=None, page_cat=None, parser=None), confidence=0.97777)", "spacy_classifications": [{"classification": "DE/310/ZH/Berufsauslagen", "confidence": 0.9998736381530762, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 4.9868271162267774e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/311/ZH/Provisorische Steuerberechnung (kantonal)", "confidence": 3.342134368722327e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/OW/Berufsauslagen", "confidence": 1.261207762581762e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Berufsorientierte Aus- und Weiterbildungskosten", "confidence": 9.989171303459443e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2828, "bottom": 4126}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1560, "right": 1827, "bottom": 270}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97777, "confidence_formatted": "98%", "confidence_level": "high", "number": 6, "processed_page_uuid": "3490c0be-ef3e-41ec-b2d3-4f0ab455407a"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 7, "page_category": {"id": 167, "name": "TAX_DECLARATION_OCCUPATIONAL_EXPENSES_FORM"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.97763, "value_formatted": "98%", "color": "green"}, "confidence": 0.97763, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Berufsauslagen', meta_json=None, page_cat=None, parser=None), confidence=0.97763)", "spacy_classifications": [{"classification": "DE/310/ZH/Berufsauslagen", "confidence": 0.9998682737350464, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SZ/Berufsauslagen", "confidence": 3.210130307707004e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Abzüge", "confidence": 2.3776914531481452e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/311/ZH/Provisorische Steuerberechnung (kantonal)", "confidence": 2.2873298803460784e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/OW/Berufsauslagen", "confidence": 1.1204620022908784e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2828, "bottom": 4126}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1560, "right": 1827, "bottom": 270}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97763, "confidence_formatted": "98%", "confidence_level": "high", "number": 7, "processed_page_uuid": "6411d3a1-cc9e-4d44-b6fd-ee7a29754168"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 8, "page_category": {"id": 150, "name": "TAX_DECLARATION_DEBT_INVENTORY"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.97778, "value_formatted": "98%", "color": "green"}, "confidence": 0.97778, "confidence_info": {"parsername": "TaxDeclarationZHDebtInventoryPageParser", "spacy_classifications": [{"classification": "DE/310/ZH/Schuldenverzeichnis", "confidence": 0.9999966621398926, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Beiblatt zum Liegenschaftenverzeichnis", "confidence": 1.6702687162251095e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Berufsorientierte Aus- und Weiterbildungskosten", "confidence": 5.166530172573403e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Liegenschaftenerträge und Unterhaltskosten", "confidence": 5.039026973463478e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/729/Kündigung Hypothekarvertrag", "confidence": 2.924207649357413e-07, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "debt_detail_lines", "title": "Schulden Details", "titles": {"de": "Schulden Details", "en": "Debt Details", "fr": "[Debt Details]", "it": "[Debt Details]"}, "value": "Credit Suisse, 8070 Zürich, Fix-Hypothek                                                                1 135 000                    11 257", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 926, "left": 142, "right": 2812, "bottom": 1001}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "debt_total", "title": "Total Schulden", "titles": {"de": "Total Schulden", "en": "Total Debt", "fr": "[Total Debt]", "it": "[Total Debt]"}, "value": "CHF 1'135'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3589, "left": 2047, "right": 2247, "bottom": 3656}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "interest_paid_on_debt", "title": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "en": "Interest paid on Debt", "fr": "[Interest paid on Debt]", "it": "[Interest paid on Debt]"}, "value": "CHF 11'257", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3583, "left": 2652, "right": 2788, "bottom": 3658}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3805, "left": 139, "right": 2827, "bottom": 4127}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3805, "left": 139, "right": 2827, "bottom": 4127}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97778, "confidence_formatted": "98%", "confidence_level": "high", "number": 8, "processed_page_uuid": "*************-4f3b-a174-4b4cf160fa37"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 9, "page_category": {"id": 166, "name": "TAX_DECLARATION_INSURANCE_PREMIUMS"}, "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "confidence_summary": {"value": 0.97757, "value_formatted": "98%", "color": "green"}, "confidence": 0.97757, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Versicherungsprämien', meta_json=None, page_cat=None, parser=None), confidence=0.97757)", "spacy_classifications": [{"classification": "DE/310/ZH/Versicherungsprämien", "confidence": 0.9997884631156921, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Schuldenverzeichnis", "confidence": 0.0001509400608483702, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/440/Vorsorgepolice 3/AXA", "confidence": 1.589584644534625e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Liegenschaftenerträge und Unterhaltskosten", "confidence": 1.2910507393826265e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/SZ/Versicherungsprämien und Zinsen von Sparkapitalien", "confidence": 9.800565749173984e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2827, "bottom": 4127}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1942, "right": 2209, "bottom": 270}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97757, "confidence_formatted": "98%", "confidence_level": "high", "number": 9, "processed_page_uuid": "662deef5-f4fc-498d-9a70-f5bbaa06b71e"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 10, "page_category": {"id": 160, "name": "TAX_DECLARATION_ACCOUNTS_FORM_FRONT"}, "document_category": {"id": "316", "name": "TAX_LIST_FINANCIAL_ASSETS"}, "confidence_summary": {"value": 0.97747, "value_formatted": "98%", "color": "green"}, "confidence": 0.97747, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Wertschriftenverzeichnis Front', meta_json=None, page_cat=None, parser=None), confidence=0.97747)", "spacy_classifications": [{"classification": "DE/310/ZH/Wertschriftenverzeichnis Front", "confidence": 0.****************, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Wertschriftenverzeichnis Details/Rechte Seite (Zugang | Abgang)", "confidence": 3.64458196600026e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/311/ZH/Provisorische Steuerberechnung (kantonal)", "confidence": 2.2151830307848286e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Wertschriftenverzeichnis Details/Ganze Seite & Linke Seite", "confidence": 1.5345234487540438e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/LU/Deckblatt", "confidence": 1.1075338761656894e-06, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2856, "bottom": 4127}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 31, "left": 32, "right": 133, "bottom": 116}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 1, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 218, "left": 2586, "right": 2856, "bottom": 409}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 67, "left": 713, "right": 798, "bottom": 110}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 1, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 33, "right": 197, "bottom": 1148}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 1, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97747, "confidence_formatted": "98%", "confidence_level": "high", "number": 10, "processed_page_uuid": "6c540c81-f507-41f5-a471-cd104740d5e5"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 11, "page_category": {"id": 161, "name": "TAX_DECLARATION_ACCOUNTS_FORM_DETAILS"}, "document_category": {"id": "316", "name": "TAX_LIST_FINANCIAL_ASSETS"}, "confidence_summary": {"value": 0.97777, "value_formatted": "98%", "color": "green"}, "confidence": 0.97777, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Wertschriftenverzeichnis Details/Ganze Seite & Linke Seite', meta_json=None, page_cat=None, parser=None), confidence=0.97777)", "spacy_classifications": [{"classification": "DE/310/ZH/Wertschriftenverzeichnis Details/Ganze Seite & Linke Seite", "confidence": 0.****************, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Wertschriftenverzeichnis Front", "confidence": 0.00023302054614759982, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Schuldenverzeichnis", "confidence": 8.785539830569178e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Wertschriftenverzeichnis Details/Rechte Seite (Zugang | Abgang)", "confidence": 6.467847560998052e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Versicherungsprämien", "confidence": 3.5787928936770186e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 118, "left": 139, "right": 4062, "bottom": 2898}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 291, "left": 3604, "right": 3686, "bottom": 354}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "fh_da<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"<PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON><PERSON>\", \"classifier\": null, \"context\": \"1. Kundenguthaben deren Bruttozins CHF 200.-nicht \\u00fcbersteigt                                                   \\u00dcbertrag Bruttoertrag A in Kolonne Bruttoertrag B                                                 l       +                  539\\n2. <PERSON><PERSON><PERSON>, Ko<PERSON> und Guthaben aller Art ohne Verrechnungssteuerabzug\\n3. Ausl\\u00e4ndische Wertschriften aller Art                                                                                                                                                                                                                                                                              I\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2217, "left": 184, "right": 301, "bottom": 2271}, "page_number": 11, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_nutzniessung", "title": "Nutzniessungs-Thema", "titles": {"de": "Nutzniessungs-Thema", "en": "Nutzniessungs-Thema", "fr": "Nutzniessungs-Thema", "it": "Nutzniessungs-Thema"}, "value": "{\"value\": \"Nutzniessung\", \"value_found\": \"Nutzniessung\", \"classifier\": null, \"context\": \"5. Anteile an Stockwerkeigent\\u00fcmergemeinschaft                                                                                                                                                                                                                                                                \\n* Code: G Gesch\\u00e4ftsverm\\u00f6gen            N Nutzniessungsverm\\u00f6gen    Q Qualifizierte Beteiligung ^                                                                              \\u25ba Zu \\u00fcbertragen in die\\nIhr Verrechnungssteueranspruch                                  davon 35% '            Steuererkl\\u00e4rung\\nE Wertschriften aus Erbschaften      S Schenkung\\n35% von Total Bruttoertrag A                                                                       | Seite 2, <PERSON><PERSON><PERSON> 4.1\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2396, "left": 846, "right": 1030, "bottom": 2449}, "page_number": 11, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "fh_schenkung", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "value": "{\"value\": \"<PERSON><PERSON>kung\", \"value_found\": \"<PERSON>henkung\", \"classifier\": null, \"context\": \"* Code: G Gesch\\u00e4ftsverm\\u00f6gen            N Nutzniessungsverm\\u00f6gen    Q Qualifizierte Beteiligung ^                                                                              \\u25ba Zu \\u00fcbertragen in die\\nIhr Verrechnungssteueranspruch                                  davon 35% '            Steuererkl\\u00e4rung\\nE Wertschriften aus Erbschaften      S Schenkung\\n35% von Total Bruttoertrag A                            0.00                                    | Seite 2, <PERSON><PERSON><PERSON> 4.1\\n540\\n151\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2435, "left": 846, "right": 995, "bottom": 2489}, "page_number": 11, "confidence": 0.01, "confidence_summary": {"value": 0.01, "value_formatted": "1%", "color": "red"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.01, "confidence_formatted": "1%", "confidence_level": "low"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 744, "left": 33, "right": 193, "bottom": 802}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 1, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97777, "confidence_formatted": "98%", "confidence_level": "high", "number": 11, "processed_page_uuid": "3fb02c24-0a43-4529-939c-cc9a409e0d58"}, {"lang": "de", "source_file_path": "TAX_max_maria_2019_4.pdf", "source_page_number": 12, "page_category": {"id": 161, "name": "TAX_DECLARATION_ACCOUNTS_FORM_DETAILS"}, "document_category": {"id": "316", "name": "TAX_LIST_FINANCIAL_ASSETS"}, "confidence_summary": {"value": 0.97725, "value_formatted": "98%", "color": "green"}, "confidence": 0.97725, "confidence_info": {"parsername": "SpacyClassifierPageParser(clazz=ClassificationValueObject(name='DE/310/ZH/Wertschriftenverzeichnis Details Beiblatt', meta_json=None, page_cat=None, parser=None), confidence=0.97725)", "spacy_classifications": [{"classification": "DE/310/ZH/Wertschriftenverzeichnis Details Beiblatt", "confidence": 0.****************, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/CH/Steuerausscheidung", "confidence": 0.00021327173453755677, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Barcodeblatt/Barcodeblatt Fortsetzung", "confidence": 0.0001081037480616942, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/LU/Wertschriftennverzeichnis Details", "confidence": 5.**************8e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}, {"classification": "DE/310/ZH/Berufsorientierte Aus- und Weiterbildungskosten", "confidence": 4.461951175471768e-05, "classifier": "hypodossier-models/hydocs_spacy/hydocs_detail_de_20220407-*************-1328.tar.gz"}]}, "page_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 60, "left": 137, "right": 4062, "bottom": 2898}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "[rest_logos]", "fr": "[[rest_logos]]", "it": "[[rest_logos]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 34, "left": 33, "right": 119, "bottom": 93}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 1, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "[rest_qr_code]", "fr": "[[rest_qr_code]]", "it": "[[rest_qr_code]]"}, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 744, "left": 32, "right": 192, "bottom": 804}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 1, "visible": true, "invisibility_reason": "'rest_*' objects are set to invisible", "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "searchable_pdf": null, "searchable_txt": null, "confidence_value": 0.97725, "confidence_formatted": "98%", "confidence_level": "high", "number": 12, "processed_page_uuid": "25f85ea9-6810-460a-a298-6fd749d1722d"}], "document_category": {"id": "310", "name": "TAX_DECLARATION"}, "document_category_original": {"id": "310", "name": "TAX_DECLARATION"}, "aggregated_objects": [{"key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2855, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2387, "bottom": 414}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3799, "left": 139, "right": 2828, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "address_block", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Address", "fr": "[Address]", "it": "[Address]"}, "value": "<PERSON><PERSON><PERSON> Maria\nBe<PERSON>pielstrasse 42\n8055 Zürich", "type": "ADDRESS_BLOCK", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 781, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "street", "title": "Strasse", "titles": {"de": "Strasse", "en": "Street", "fr": "[Street]", "it": "[Street]"}, "value": "Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 848, "left": 816, "right": 1183, "bottom": 911}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "zip", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "Zip Code", "fr": "[Zip Code]", "it": "[Zip Code]"}, "value": "8055", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 815, "right": 920, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "city", "title": "Ort", "titles": {"de": "Ort", "en": "City", "fr": "[City]", "it": "[City]"}, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 925, "right": 1054, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_fullname", "title": "P1 Name", "titles": {"de": "P1 Name", "en": "P1 Name", "fr": "P1 Nom", "it": "[P1 Name]"}, "value": "<PERSON><PERSON><PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 723, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_firstname", "title": "P2 Vorname", "titles": {"de": "P2 Vorname", "en": "P2 Lastname", "fr": "P2 Prénom", "it": "[P2 Lastname]"}, "value": "<PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1831, "left": 2043, "right": 2156, "bottom": 1894}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "phone_primary", "title": "Telefonnummer", "titles": {"de": "Telefonnummer", "en": "Phone number", "fr": "[Phone number]", "it": "[Phone number]"}, "value": "0763331234      P 07633", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2127, "left": 964, "right": 1539, "bottom": 2197}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "phone_secondary", "title": "Telefonnummer (2. Prio)", "titles": {"de": "Telefonnummer (2. Prio)", "en": "Phone number (2nd Priority)", "fr": "[Phone number (2nd Priority)]", "it": "[Phone number (2nd Priority)]"}, "value": "Telefon G       0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 1769, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_phone_primary", "title": "P2 Telefonnummer", "titles": {"de": "P2 Telefonnummer", "en": "P2 Phone number", "fr": "P2 Téléphone", "it": "[P2 Phone number]"}, "value": "0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 2041, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 633, "left": 2047, "right": 2407, "bottom": 703}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_date_of_birth", "title": "P1 Geburtsdatum", "titles": {"de": "P1 Geburtsdatum", "en": "P1 Date of Birth", "fr": "P1 Date de naissance", "it": "[P1 Date of Birth]"}, "value": "04.09.1967", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 964, "right": 1191, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_marital_status", "title": "P1 Zivilstand", "titles": {"de": "P1 Zivilstand", "en": "P1 Civil Status", "fr": "P1 Etat civil", "it": "[P1 Civil Status]"}, "value": "verheiratet", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1834, "left": 963, "right": 1181, "bottom": 1897}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_profession", "title": "P1 Beruf", "titles": {"de": "P1 Beruf", "en": "P1 Profession", "fr": "P1 Profession", "it": "[P1 Profession]"}, "value": "Informatiker", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 966, "right": 1207, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_employer", "title": "P1 Arbeitgeber", "titles": {"de": "P1 Arbeitgeber", "en": "P1 Employer", "fr": "P1 Employeur", "it": "[P1 Employer]"}, "value": "Beispielfirma AG", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 965, "right": 1300, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_employer_location", "title": "P1 Arbeitsort", "titles": {"de": "P1 Arbeitsort", "en": "P1 Place of work", "fr": "P1 Lieu de travail", "it": "[P1 Place of work]"}, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2074, "left": 963, "right": 1090, "bottom": 2137}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_date_of_birth", "title": "P2 Geburtsdatum", "titles": {"de": "P2 Geburtsdatum", "en": "P2 Date of Birth", "fr": "P2 Date de naissance", "it": "[P2 Date of Birth]"}, "value": "21.10.1976", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 2040, "right": 2267, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_profession", "title": "P2 Beruf", "titles": {"de": "P2 Beruf", "en": "P2 Profession", "fr": "P2 Profession", "it": "[P2 Profession]"}, "value": "Solution Consultant", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 2041, "right": 2435, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_employer", "title": "P2 Arbeitgeber", "titles": {"de": "P2 Arbeitgeber", "en": "P2 Employer", "fr": "P2 Employeur", "it": "[P2 Employer]"}, "value": "ServiceFirma", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 2041, "right": 2308, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "section_children", "title": "Kinder", "titles": {"de": "Kinder", "en": "Children", "fr": "<PERSON><PERSON><PERSON>", "it": "[Children]"}, "value": "<PERSON>", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2534, "left": 137, "right": 572, "bottom": 2656}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_income_employed_main", "title": "P1 Einkommen Haupterwerb", "titles": {"de": "P1 Einkommen Haupterwerb", "en": "P1 Main Income", "fr": "P1 Revenu net dépendante", "it": "[P1 Main Income]"}, "value": "CHF 22'506", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 280, "left": 2662, "right": 2802, "bottom": 336}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_income_employed_main", "title": "P2 Einkommen Haupterwerb", "titles": {"de": "P2 Einkommen Haupterwerb", "en": "P2 Main Income", "fr": "P2 Revenu net dépendante", "it": "[P2 Main Income]"}, "value": "CHF 128'991", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 360, "left": 2640, "right": 2796, "bottom": 427}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_income_employed_side", "title": "P1 Einkommen Nebenverdienst", "titles": {"de": "P1 Einkommen Nebenverdienst", "en": "P1 Additional Income", "fr": "[P1 Additional Income]", "it": "[P1 Additional Income]"}, "value": "CHF 7'502", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 459, "left": 2688, "right": 2802, "bottom": 514}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_income_eo", "title": "P2 Einkünfte Erwerbsausfallentschädigung", "titles": {"de": "P2 Einkünfte Erwerbsausfallentschädigung", "en": "P2 Income EO", "fr": "[P2 Income EO]", "it": "[P2 Income EO]"}, "value": "CHF 4'388", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1939, "left": 2686, "right": 2802, "bottom": 2006}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_income_child_benefits", "title": "P2 Einkünfte Kinder- und Familienzulagen", "titles": {"de": "P2 Einkünfte Kinder- und Familienzulagen", "en": "P2 Income Child Benefits", "fr": "[P2 Income Child Benefits]", "it": "[P2 Income Child Benefits]"}, "value": "CHF 4'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2190, "left": 2686, "right": 2802, "bottom": 2244}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_portfolio", "title": "Wertschriftenerträge", "titles": {"de": "Wertschriftenerträge", "en": "Income Portfolio", "fr": "Revenue mobilier", "it": "[Income Portfolio]"}, "value": "CHF 15", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2341, "left": 2753, "right": 2803, "bottom": 2408}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_real_estate_gross", "title": "Liegenschaftenertrag brutto", "titles": {"de": "Liegenschaftenertrag brutto", "en": "Income Real Estate gross", "fr": "Revenu brut immobilier", "it": "[Income Real Estate gross]"}, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3305, "left": 1971, "right": 2110, "bottom": 3358}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_real_estate_net_primary", "title": "Primärliegenschaft Ertrag netto", "titles": {"de": "Primärliegenschaft Ertrag netto", "en": "Primary Self used Real Estate Income net", "fr": "[Primary Self used Real Estate Income net]", "it": "[Primary Self used Real Estate Income net]"}, "value": "CHF 20'560", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3588, "left": 2662, "right": 2802, "bottom": 3642}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3765, "left": 2640, "right": 2802, "bottom": 3833}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "expense_children_daycare", "title": "Fremdbetreuung von Kindern", "titles": {"de": "Fremdbetreuung von Kindern", "en": "Expenses Daycare Children", "fr": "[Expenses Daycare Children]", "it": "[Expenses Daycare Children]"}, "value": "CHF 19'248", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1819, "left": 2093, "right": 2230, "bottom": 1894}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_contribution_pillar_3a", "title": "P1 Beiträge Säule 3a", "titles": {"de": "P1 Beiträge Säule 3a", "en": "P1 Contribution Pillar 3a", "fr": "[P1 Contribution Pillar 3a]", "it": "[P1 Contribution Pillar 3a]"}, "value": "CHF 3'350", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1026, "left": 2116, "right": 2230, "bottom": 1082}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_contribution_pillar_3a", "title": "P2 Beiträge Säule 3a", "titles": {"de": "P2 Beiträge Säule 3a", "en": "P2 Contribution Pillar 3a", "fr": "[P2 Contribution Pillar 3a]", "it": "[P2 Contribution Pillar 3a]"}, "value": "CHF 6'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1117, "left": 2115, "right": 2230, "bottom": 1173}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p1_expense_employment", "title": "P1 Berufskosten", "titles": {"de": "P1 Berufskosten", "en": "P1 Employment Expenses", "fr": "P1 Déductions liées a l'activite dependante", "it": "[P1 Employment Expenses]"}, "value": "CHF 7'901", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 295, "left": 2116, "right": 2224, "bottom": 351}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "p2_expense_employment", "title": "P2 Berufskosten", "titles": {"de": "P2 Berufskosten", "en": "P2 Employment Expenses", "fr": "P2 Déductions liées a l'activite dependante", "it": "[P2 Employment Expenses]"}, "value": "CHF 8'850", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 385, "left": 2116, "right": 2230, "bottom": 441}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "insurance_premiums_and_interest_on_savings_accounts", "title": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "titles": {"de": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "en": "Insurance Premiums and Interest on Savings Accounts", "fr": "[Insurance Premiums and Interest on Savings Accounts]", "it": "[Insurance Premiums and Interest on Savings Accounts]"}, "value": "CHF 7'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1229, "left": 2116, "right": 2230, "bottom": 1282}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "interest_paid_on_debt", "title": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "en": "Interest paid on Debt", "fr": "[Interest paid on Debt]", "it": "[Interest paid on Debt]"}, "value": "CHF 11'257", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 470, "left": 2093, "right": 2230, "bottom": 545}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "property_maintenance_cost", "title": "Liegenschaft Unterhaltskosten", "titles": {"de": "Liegenschaft Unterhaltskosten", "en": "Maintenance Cost", "fr": "Charges et frais d'entretien d'immeuble", "it": "[Maintenance Cost]"}, "value": "CHF 5'140", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3397, "left": 1996, "right": 2110, "bottom": 3451}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "deductions_total", "title": "Total der Abzüge", "titles": {"de": "Total der Abzüge", "en": "Total Deductions", "fr": "[Total Deductions]", "it": "[Total Deductions]"}, "value": "CHF 71'006", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2112, "left": 2091, "right": 2230, "bottom": 2166}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_net_total", "title": "Total der Einkünfte (netto)", "titles": {"de": "Total der Einkünfte (netto)", "en": "Total Income net", "fr": "[Total Income net]", "it": "[Total Income net]"}, "value": "CHF 117'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2536, "left": 2068, "right": 2230, "bottom": 2610}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_taxable_global", "title": "Steuerbares Einkommen gesamt", "titles": {"de": "Steuerbares Einkommen gesamt", "en": "Taxable Income total", "fr": "[Taxable Income total]", "it": "[Taxable Income total]"}, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3414, "left": 2091, "right": 2230, "bottom": 3488}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "income_taxable_local", "title": "Steuerbares Einkommen im Kanton", "titles": {"de": "Steuerbares Einkommen im Kanton", "en": "Taxable Income in Canton", "fr": "[Taxable Income in Canton]", "it": "[Taxable Income in Canton]"}, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3757, "left": 2091, "right": 2230, "bottom": 3832}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_portfolio", "title": "Wertschriften und Guthaben", "titles": {"de": "Wertschriften und Guthaben", "en": "Portfolio and Accounts", "fr": "[Portfolio and Accounts]", "it": "[Portfolio and Accounts]"}, "value": "CHF 2'458'532", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 319, "left": 2617, "right": 2865, "bottom": 394}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_cars", "title": "Fahrzeuge", "titles": {"de": "Fahrzeuge", "en": "Vehicles", "fr": "[Vehicles]", "it": "[Vehicles]"}, "value": "CHF 1'040", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 841, "left": 2709, "right": 2820, "bottom": 904}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_other", "title": "Übrige Vermögenswerte", "titles": {"de": "Übrige Vermögenswerte", "en": "Further Assets", "fr": "[Further Assets]", "it": "[Further Assets]"}, "value": "CHF 180'288", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1019, "left": 2658, "right": 2820, "bottom": 1082}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_real_estate_main_property", "title": "Liegenschaft EFH oder Stockwerkeigentum", "titles": {"de": "Liegenschaft EFH oder Stockwerkeigentum", "en": "Real Estate (house or appartment", "fr": "[Real Estate (house or appartment]", "it": "[Real Estate (house or appartment]"}, "value": "CHF 1'172'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1235, "left": 2621, "right": 2820, "bottom": 1298}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "address_real_estate_primary", "title": "Primärliegenschaft Adresse", "titles": {"de": "Primärliegenschaft Adresse", "en": "Primary Self used Real Estate Address", "fr": "[Primary Self used Real Estate Address]", "it": "[Primary Self used Real Estate Address]"}, "value": "Gemeinde ZH                    Zürich           Strasse Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1240, "left": 238, "right": 1590, "bottom": 1303}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_gross_total", "title": "Total der Vermögenswerte", "titles": {"de": "Total der Vermögenswerte", "en": "Total Assets Gross", "fr": "Fortune brut", "it": "[Total Assets Gross]"}, "value": "CHF 3'811'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1630, "left": 2619, "right": 2820, "bottom": 1693}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_taxable_global", "title": "Steuerbares Vermögen gesamt", "titles": {"de": "Steuerbares Vermögen gesamt", "en": "Taxable Assets (global)", "fr": "Fortune imposable (globale)", "it": "[Taxable Assets (global)]"}, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1832, "left": 2617, "right": 2820, "bottom": 1892}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "assets_taxable_local", "title": "Steuerbares Vermögen im Kanton", "titles": {"de": "Steuerbares Vermögen im Kanton", "en": "Taxable Assets in Canton", "fr": "Fortune imposable (locale)", "it": "[Taxable Assets in Canton]"}, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2182, "left": 2617, "right": 2820, "bottom": 2245}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "debt_total", "title": "Total Schulden", "titles": {"de": "Total Schulden", "en": "Total Debt", "fr": "[Total Debt]", "it": "[Total Debt]"}, "value": "CHF 1'135'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1727, "left": 2621, "right": 2865, "bottom": 1790}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "debt_detail_lines", "title": "Schulden Details", "titles": {"de": "Schulden Details", "en": "Debt Details", "fr": "[Debt Details]", "it": "[Debt Details]"}, "value": "Credit Suisse, 8070 Zürich, Fix-Hypothek                                                                1 135 000                    11 257", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 926, "left": 142, "right": 2812, "bottom": 1001}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}, {"key": "property_imputed_rental_value", "title": "Liegenschaft Eigenmietwert", "titles": {"de": "Liegenschaft Eigenmietwert", "en": "Imputed Rental Value", "fr": "Valeur locative", "it": "Valore locativo"}, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3111, "left": 1971, "right": 2110, "bottom": 3165}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value": 0.95, "value_formatted": "95%", "color": "green"}, "source": 2, "visible": true, "invisibility_reason": null, "confidence_value": 0.95, "confidence_formatted": "95%", "confidence_level": "high"}], "immutable_file": false, "confidence_value": 1.0, "confidence_formatted": "100%", "confidence_level": "high"}]}