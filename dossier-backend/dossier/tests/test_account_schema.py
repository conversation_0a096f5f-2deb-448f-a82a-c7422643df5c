from dossier.schemas import Do<PERSON>rAccountResponse
from projectconfig.settings import (
    DEFAULT_VALID_UI_LANGUAGES,
    DEFAULT_VALID_DOSSIER_LANGUAGES,
)


# I had to write a pydantic schema for the existing get account api
# Test against captured responses to make sure pydantic can correctly handle serialisation
def test_account_schema_default_user():
    default_account_response = {
        "account_name": "HyppoDossier account",
        "default_language": "De",
        "all_languages": DEFAULT_VALID_DOSSIER_LANGUAGES,
        "valid_dossier_languages": DEFAULT_VALID_DOSSIER_LANGUAGES,
        "valid_ui_languages": DEFAULT_VALID_UI_LANGUAGES,
        "show_document_category_external": True,
        "show_business_case_type": False,
        "enable_button_create": True,
        "enable_feedback_form": False,
        "enable_download_original_file_link": True,
        "enable_show_deleted_elements": False,
        "enable_dossier_sorting": False,
        "enable_error_detail": False,
        "enable_dossier_search": False,
        "enable_zoom_feature": True,
        "enable_debug_document": True,
        "enable_download_dossier": True,
        "enable_download_document": True,
        "enable_icons_on_page_view": True,
        "enable_uploading_files": True,
        "enable_drag_and_drop_in_page_view": True,
        "enable_rendering_structure_tab": True,
        "enable_hovered_section_on_page_view": True,
        "enable_rendering_hurdles_tab": True,
        "enable_area_calculator": True,
        "enable_semantic_document_confidence": False,
        "enable_semantic_document_export": False,
        "enable_bekb_export": False,
        "enable_bekb_automatic_collateral": False,
        "enable_button_open_in_new_tab": True,
        "enable_rendering_photos_tab": True,
        "enable_rendering_structure_details_tab": True,
        "enable_rendering_bekb_mortgage_archiving_tab": False,
        "enable_button_dossier_settings": True,
        "enable_button_dossier_notes": True,
        "enable_button_download": True,
        "enable_document_upload": True,
        "enable_documents_delta_view": True,
        "enable_semantic_page_image_lazy_loading": False,
        "enable_rendering_plans_tab": True,
        "enable_real_estate_properties": True,
        "enable_dossier_assignment": True,
        "enable_dossier_assignment_to_someone_else": False,
        "navigation_strategy": "DEFAULT",
        "is_bekb": True,
        "state_machine": None,
        "frontend_theme": "",
        "photo_album_docx_template": "photo-album-docx-template-default-v01.docx",
        "instructions_menu_key": "default",
        "available_dossier_roles": [
            {
                "uuid": "d973b3d9-9a93-44c4-8b7f-6261ffbb0509",
                "account": "HyppoDossier account",
                "key": "ASSIGNEE",
                "name_de": "Zust\u00e4ndig",
                "name_en": "Assignee",
                "name_fr": "Responsable",
                "name_it": "Assegnatario",
                "user_selectable": True,
                "show_separate_filter": True,
                "users": [
                    {
                        "first_name": "test 2 first",
                        "last_name": "test 2 last",
                        "username": "<EMAIL>",
                    },
                    {
                        "first_name": "alex",
                        "last_name": "Kolesnykov",
                        "username": "newmanager",
                    },
                    {
                        "first_name": "User1",
                        "last_name": "Account2",
                        "username": "<EMAIL>",
                    },
                    {
                        "first_name": "Martin",
                        "last_name": "Oberchef",
                        "username": "<EMAIL>",
                    },
                    {
                        "first_name": "John\u00e4\u00eb\u00f6\u00fc\u00e0\u00e9\u00e8\u00c4\u00d6\u00dc",
                        "last_name": "Doe \u20ac",
                        "username": "<EMAIL>",
                    },
                    {
                        "first_name": "Alexandra",
                        "last_name": "Rodriguez",
                        "username": "<EMAIL>",
                    },
                ],
            }
        ],
        "active_doc_check": None,
        "enable_form_tab": False,
        "enable_semantic_document_splitting": False,
        "dossier_access_check_error_component": "default",
        "enable_custom_semantic_document_date": True,
        "enable_semantic_document_annotations": False,
        "enable_download_extraction_excel": True,
        "enable_dossier_permission": True,
        "enable_download_metadata_json": True,
        "document_download_ui_add_uuid_suffix": True,
    }

    # Should not throw
    DossierAccountResponse(**default_account_response)


# I had to write a pydantic schema for the existing get account api
# Test against captured responses to make sure pydantic can correctly handle serialisation
def test_account_schema_bekbe_manager_user():
    # <EMAIL>
    DossierAccountResponse(
        **{
            "account_name": "BEKB",
            "default_language": "De",
            "all_languages": DEFAULT_VALID_DOSSIER_LANGUAGES,
            "valid_dossier_languages": DEFAULT_VALID_DOSSIER_LANGUAGES,
            "valid_ui_languages": DEFAULT_VALID_UI_LANGUAGES,
            "show_document_category_external": True,
            "show_business_case_type": True,
            "enable_button_create": True,
            "enable_feedback_form": False,
            "enable_download_original_file_link": True,
            "enable_show_deleted_elements": False,
            "enable_dossier_sorting": False,
            "enable_error_detail": True,
            "enable_dossier_search": False,
            "enable_zoom_feature": True,
            "enable_debug_document": True,
            "enable_download_dossier": True,
            "enable_download_document": False,
            "enable_icons_on_page_view": True,
            "enable_uploading_files": True,
            "enable_drag_and_drop_in_page_view": True,
            "enable_rendering_structure_tab": False,
            "enable_hovered_section_on_page_view": True,
            "enable_rendering_hurdles_tab": True,
            "enable_area_calculator": True,
            "enable_semantic_document_confidence": False,
            "enable_semantic_document_export": False,
            "enable_bekb_export": True,
            "enable_bekb_automatic_collateral": True,
            "enable_button_open_in_new_tab": True,
            "enable_rendering_photos_tab": True,
            "enable_rendering_structure_details_tab": True,
            "enable_rendering_bekb_mortgage_archiving_tab": True,
            "enable_button_dossier_settings": True,
            "enable_button_dossier_notes": True,
            "enable_button_download": True,
            "enable_document_upload": True,
            "enable_documents_delta_view": True,
            "enable_semantic_page_image_lazy_loading": False,
            "enable_rendering_plans_tab": True,
            "enable_real_estate_properties": False,
            "enable_dossier_assignment": True,
            "enable_dossier_assignment_to_someone_else": True,
            "navigation_strategy": "DEFAULT",
            "is_bekb": True,
            "state_machine": "********-b4d5-4ef0-ac22-450cc0ed1534",
            "frontend_theme": "",
            "photo_album_docx_template": "photo-album-docx-template-default-v01.docx",
            "instructions_menu_key": "default",
            "available_dossier_roles": [
                {
                    "uuid": "632a1c75-82cc-44b7-95ef-fbe638f77087",
                    "account": "BEKB E",
                    "key": "FICO",
                    "name_de": "FICO",
                    "name_en": "FICO",
                    "name_fr": "FICO",
                    "name_it": "FICO",
                    "user_selectable": False,
                    "show_separate_filter": True,
                    "users": [
                        {
                            "first_name": "Clemens",
                            "last_name": "Sigrist",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Branko",
                            "last_name": "Bernasconi",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "G\u00f6khan",
                            "last_name": "Bauer",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Shqipe",
                            "last_name": "M\u00e4der",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Lutz",
                            "last_name": "Albrecht",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Joris",
                            "last_name": "Lang",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Elizabeth",
                            "last_name": "Schwarz",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Xaver",
                            "last_name": "Iten",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Carla",
                            "last_name": "St\u00f6ckli",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Anabela",
                            "last_name": "Lanz",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Henry",
                            "last_name": "Zehnder",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Vincent",
                            "last_name": "Zimmermann",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Marlyse",
                            "last_name": "Amrein",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Dardan",
                            "last_name": "Sch\u00e4r",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Emilia",
                            "last_name": "R\u00fcegg",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Annamaria",
                            "last_name": "M\u00fcller",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Katherine",
                            "last_name": "Lustenberger",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Aurel",
                            "last_name": "Jost",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Patrizia",
                            "last_name": "Meyer",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Lou",
                            "last_name": "Sutter",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Hamza",
                            "last_name": "Sch\u00e4r",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Jens",
                            "last_name": "Kohler",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Kaan",
                            "last_name": "Lehmann",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "K\u00e4the",
                            "last_name": "Suter",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Ibrahim",
                            "last_name": "Zollinger",
                            "username": "<EMAIL>",
                        },
                    ],
                },
                {
                    "uuid": "03065e06-f936-4579-ae8d-a654903590b4",
                    "account": "BEKB E",
                    "key": "ASSIGNEE",
                    "name_de": "Zust\u00e4ndiger",
                    "name_en": "Assignee",
                    "name_fr": "Responsable",
                    "name_it": "Assegnatario",
                    "user_selectable": True,
                    "show_separate_filter": True,
                    "users": [
                        {
                            "first_name": "Maeva",
                            "last_name": "Wolf",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Bastian",
                            "last_name": "Wehrli",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Armando",
                            "last_name": "Hotz",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Noe",
                            "last_name": "Knecht",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Lia",
                            "last_name": "Keller",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Gabriela",
                            "last_name": "Sp\u00f6rri",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Ayla",
                            "last_name": "Stalder",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Margot",
                            "last_name": "Schoch",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Ja\u00ebl",
                            "last_name": "Beck",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Sabina",
                            "last_name": "Sch\u00e4r",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Jochen",
                            "last_name": "Jenni",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Matilda",
                            "last_name": "Hartmann",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Enis",
                            "last_name": "Steiner",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "B\u00e9atrice",
                            "last_name": "Wirz",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Sean",
                            "last_name": "Marti",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "bekbemgr1first",
                            "last_name": "bekbemgr1last",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Marine",
                            "last_name": "Kuhn",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Steven",
                            "last_name": "M\u00fcller",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Dietmar",
                            "last_name": "Wenger",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Liliane",
                            "last_name": "Steinmann",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Isabelle",
                            "last_name": "Vogel",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Emmanuel",
                            "last_name": "Koch",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Fisnik",
                            "last_name": "Schneider",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Olivia",
                            "last_name": "Portmann",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Silas",
                            "last_name": "Wirth",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Joanne",
                            "last_name": "Senn",
                            "username": "<EMAIL>",
                        },
                    ],
                },
            ],
            "active_doc_check": "b0935bca-fc58-41dd-ae98-8b52f3ad11e9",
            "enable_form_tab": False,
            "enable_semantic_document_splitting": False,
            "dossier_access_check_error_component": "default",
            "enable_custom_semantic_document_date": False,
            "enable_semantic_document_annotations": False,
            "enable_download_extraction_excel": True,
            "enable_dossier_permission": True,
            "enable_download_metadata_json": True,
            "document_download_ui_add_uuid_suffix": True,
        }
    )


def test_account_schema_zkb_manager_user():
    DossierAccountResponse(
        **{
            "account_name": "ZKB",
            "default_language": "De",
            "all_languages": DEFAULT_VALID_DOSSIER_LANGUAGES,
            "valid_dossier_languages": DEFAULT_VALID_DOSSIER_LANGUAGES,
            "valid_ui_languages": DEFAULT_VALID_UI_LANGUAGES,
            "show_document_category_external": True,
            "show_business_case_type": False,
            "enable_button_create": True,
            "enable_feedback_form": False,
            "enable_download_original_file_link": True,
            "enable_show_deleted_elements": False,
            "enable_dossier_sorting": False,
            "enable_error_detail": True,
            "enable_dossier_search": True,
            "enable_zoom_feature": True,
            "enable_debug_document": True,
            "enable_download_dossier": True,
            "enable_download_document": True,
            "enable_icons_on_page_view": True,
            "enable_uploading_files": True,
            "enable_drag_and_drop_in_page_view": True,
            "enable_rendering_structure_tab": True,
            "enable_hovered_section_on_page_view": True,
            "enable_rendering_hurdles_tab": True,
            "enable_area_calculator": True,
            "enable_semantic_document_confidence": False,
            "enable_semantic_document_export": False,
            "enable_bekb_export": False,
            "enable_bekb_automatic_collateral": False,
            "enable_button_open_in_new_tab": True,
            "enable_rendering_photos_tab": False,
            "enable_rendering_structure_details_tab": False,
            "enable_rendering_bekb_mortgage_archiving_tab": False,
            "enable_button_dossier_settings": False,
            "enable_button_dossier_notes": False,
            "enable_button_download": True,
            "enable_document_upload": False,
            "enable_documents_delta_view": True,
            "enable_semantic_page_image_lazy_loading": False,
            "enable_rendering_plans_tab": False,
            "enable_real_estate_properties": True,
            "enable_dossier_assignment": True,
            "enable_dossier_assignment_to_someone_else": False,
            "navigation_strategy": "NO_DOSSIER_LIST_BRANDED",
            "is_bekb": True,
            "state_machine": None,
            "frontend_theme": "",
            "photo_album_docx_template": "",
            "instructions_menu_key": "default",
            "available_dossier_roles": [
                {
                    "uuid": "8e02800b-eb69-4f19-b021-439eea541dc6",
                    "account": "zkb dev",
                    "key": "ASSIGNEE",
                    "name_de": "Zust\u00e4ndiger",
                    "name_en": "Assignee",
                    "name_fr": "Responsable",
                    "name_it": "Assegnatario",
                    "user_selectable": True,
                    "show_separate_filter": True,
                    "users": [
                        {
                            "first_name": "zkbm 1 first",
                            "last_name": "zkbm 1 last",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Jo\u00ebl",
                            "last_name": "Schmucki",
                            "username": "<EMAIL>",
                        },
                    ],
                }
            ],
            "active_doc_check": None,
            "enable_form_tab": False,
            "enable_semantic_document_splitting": False,
            "dossier_access_check_error_component": "default",
            "enable_custom_semantic_document_date": True,
            "enable_semantic_document_annotations": False,
            "enable_download_extraction_excel": True,
            "enable_dossier_permission": True,
            "enable_download_metadata_json": True,
            "document_download_ui_add_uuid_suffix": True,
        }
    )


def test_account_schema_zkb_user():
    DossierAccountResponse(
        **{
            "account_name": "ZKB",
            "default_language": "De",
            "all_languages": DEFAULT_VALID_DOSSIER_LANGUAGES,
            "valid_dossier_languages": DEFAULT_VALID_DOSSIER_LANGUAGES,
            "valid_ui_languages": DEFAULT_VALID_UI_LANGUAGES,
            "show_document_category_external": True,
            "show_business_case_type": False,
            "enable_button_create": True,
            "enable_feedback_form": False,
            "enable_download_original_file_link": True,
            "enable_show_deleted_elements": False,
            "enable_dossier_sorting": False,
            "enable_error_detail": True,
            "enable_dossier_search": True,
            "enable_zoom_feature": True,
            "enable_debug_document": True,
            "enable_download_dossier": True,
            "enable_download_document": True,
            "enable_icons_on_page_view": True,
            "enable_uploading_files": True,
            "enable_drag_and_drop_in_page_view": True,
            "enable_rendering_structure_tab": True,
            "enable_hovered_section_on_page_view": True,
            "enable_rendering_hurdles_tab": True,
            "enable_area_calculator": True,
            "enable_semantic_document_confidence": False,
            "enable_semantic_document_export": False,
            "enable_bekb_export": False,
            "enable_bekb_automatic_collateral": False,
            "enable_button_open_in_new_tab": True,
            "enable_rendering_photos_tab": False,
            "enable_rendering_structure_details_tab": False,
            "enable_rendering_bekb_mortgage_archiving_tab": False,
            "enable_button_dossier_settings": False,
            "enable_button_dossier_notes": False,
            "enable_button_download": True,
            "enable_document_upload": False,
            "enable_documents_delta_view": True,
            "enable_semantic_page_image_lazy_loading": False,
            "enable_rendering_plans_tab": False,
            "enable_real_estate_properties": True,
            "enable_dossier_assignment": True,
            "enable_dossier_assignment_to_someone_else": False,
            "navigation_strategy": "NO_DOSSIER_LIST_BRANDED",
            "is_bekb": True,
            "state_machine": None,
            "frontend_theme": "",
            "photo_album_docx_template": "",
            "instructions_menu_key": "default",
            "available_dossier_roles": [
                {
                    "uuid": "8e02800b-eb69-4f19-b021-439eea541dc6",
                    "account": "zkb dev",
                    "key": "ASSIGNEE",
                    "name_de": "Zust\u00e4ndiger",
                    "name_en": "Assignee",
                    "name_fr": "Responsable",
                    "name_it": "Assegnatario",
                    "user_selectable": True,
                    "show_separate_filter": True,
                    "users": [
                        {
                            "first_name": "zkbm 1 first",
                            "last_name": "zkbm 1 last",
                            "username": "<EMAIL>",
                        },
                        {
                            "first_name": "Jo\u00ebl",
                            "last_name": "Schmucki",
                            "username": "<EMAIL>",
                        },
                    ],
                }
            ],
            "active_doc_check": None,
            "enable_form_tab": False,
            "enable_semantic_document_splitting": False,
            "dossier_access_check_error_component": "default",
            "enable_custom_semantic_document_date": True,
            "enable_semantic_document_annotations": False,
            "enable_download_extraction_excel": True,
            "enable_dossier_permission": True,
            "enable_download_metadata_json": True,
            "document_download_ui_add_uuid_suffix": True,
        }
    )
