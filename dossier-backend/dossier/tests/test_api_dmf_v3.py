import datetime
from typing import List

import pytest
from datetime import timezone as datetime_timezone
from django.contrib.auth import get_user_model
from django.urls import reverse
from pydantic import TypeAdapter
from pytest_mock import MockerFixture

from bekb.models import BEKBDossierProperties
from bekb.services import set_pers
from core.authentication import AuthenticatedClient
from dossier.fakes import add_some_fake_semantic_documents
from dossier.management.commands.dossier_event_consumer_v2 import (
    add_file_exception,
    FileExtractedExceptionV1Schema,
)
from dossier.models import (
    Dossier,
    UserInvolvement,
    BusinessCaseType,
    Account,
    ExtractedFile,
    FileException,
    OriginalFile,
)
from dossier.schemas import DossierAssignableUsersSchema
from dossier.schemas_dmf_v3 import (
    FinHurdleGroup,
    DossierPropertiesResponse,
    DossierPropertiesChange,
    DossierProcessingDetails,
    SemanticDocumentPDFExportRequest,
)
from dossier.conftest import prepare_test_dossier_bekb
from processed_file.models import PageObjectType
from semantic_document.models import SemanticPagePageObject, SemanticDocument
from statemgmt.configurations.semantic_document_state_machine import (
    STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    SemanticDocumentState,
)
from statemgmt.models import Status, StateMachine

from dossier import schemas as dossier_schemas
from workers.models import SemanticDocumentExport
from workers.workers import process_semantic_dossier_pdf_request
from workers import schemas as worker_schemas

User = get_user_model()

pytestmark = pytest.mark.django_db


def test_create_dossier_api(testuser1_client: AuthenticatedClient):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    result = testuser1_client.get(
        reverse("api:get_dossier_hurdles", kwargs={"dossier_uuid": dossier.uuid}),
    )

    assert result.status_code == 200

    finhurdle_page_object_type = PageObjectType.objects.get(name="FINHURDLE")

    semantic_page_page_objects = SemanticPagePageObject.objects.filter(
        page_object__type=finhurdle_page_object_type
    ).filter(semantic_page__dossier=dossier, page_object__visible=True)

    assert (
        len(TypeAdapter(List[FinHurdleGroup]).validate_json(result.content))
        == 9
        == len(semantic_page_page_objects.values("page_object__key").distinct())
    )

    for fin_hurdle_group in TypeAdapter(List[FinHurdleGroup]).validate_json(
        result.content
    ):
        for fin_hurdle_ref in fin_hurdle_group.finhurdle_refs:
            SemanticPagePageObject.objects.get(
                page_object__type=finhurdle_page_object_type,
                semantic_page__uuid=fin_hurdle_ref.semantic_page_uuid,
                page_object__uuid=fin_hurdle_ref.page_object_uuid,
            )


def test_change_dossier_properties_v2(
    bekbuser1_client: AuthenticatedClient, bekbuser1_user, bekbuser2_user
):
    account, dossier, dossier.owner = prepare_test_dossier_bekb()

    involvement = UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE")

    assert involvement.user.user != bekbuser1_user
    assert involvement.user.user != bekbuser2_user

    bekb_property = BEKBDossierProperties.objects.get(
        account=dossier.account, dossier=dossier
    )

    assert bekb_property.pers is False

    business_case_type = BusinessCaseType.objects.create(
        account=dossier.account, key="test business case type"
    )

    dossier.work_status = None
    dossier.save()
    work_status = Status.objects.all().first()

    assert dossier.businesscase_type is None
    assert dossier.work_status is None

    result = bekbuser1_client.patch(
        reverse(
            "api:change-dossier-properties-v2",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=DossierPropertiesChange(
            dossier_name="Attention is all you need",
            expiry_date=datetime.datetime.now(datetime_timezone.utc)
            + datetime.timedelta(days=1),
            businesscase_type_uuid=business_case_type.uuid,
            work_status_uuid=work_status.uuid,
            assignee_username=bekbuser1_user.user.username,
        ).model_dump_json(),
    )

    result = DossierPropertiesResponse.model_validate_json(result.content)

    assert result.assignee.user_username == bekbuser1_user.user.username

    assert (
        UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE").user
        == bekbuser1_user
    )


def test_change_dossier_properties_v2_assigned_user(
    bekbuser1_client: AuthenticatedClient, bekbuser1_user, bekbuser2_user
):
    # Test all the edge cases of changing the assigned user
    account, dossier, dossier.owner = prepare_test_dossier_bekb()

    involvement = UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE")

    assert involvement.user.user != bekbuser1_user
    assert involvement.user.user != bekbuser2_user

    bekb_property = BEKBDossierProperties.objects.get(
        account=dossier.account, dossier=dossier
    )

    assert bekb_property.pers is False

    result = bekbuser1_client.patch(
        reverse(
            "api:change-dossier-properties-v2",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=DossierPropertiesChange(
            assignee_username=bekbuser1_user.user.username
        ).model_dump_json(),
    )

    result = DossierPropertiesResponse.model_validate_json(result.content)

    assert result.assignee.user_username == bekbuser1_user.user.username

    assert (
        UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE").user
        == bekbuser1_user
    )

    # Test that changing the same user, does nothing
    result = bekbuser1_client.patch(
        reverse(
            "api:change-dossier-properties-v2",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=DossierPropertiesChange(
            assignee_username=bekbuser1_user.user.username
        ).model_dump_json(),
    )

    result = DossierPropertiesResponse.model_validate_json(result.content)

    assert result.assignee.user_username == bekbuser1_user.user.username

    assert (
        UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE").user
        == bekbuser1_user
    )

    # Update to pers = true
    bekb_property.pers = True
    bekb_property.save()

    # Set API user to be pers, otherwise auth will fail
    user = User.objects.get(username="<EMAIL>")
    set_pers(dossier.account, user=user, pers=True)

    result = bekbuser1_client.patch(
        reverse(
            "api:change-dossier-properties-v2",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=DossierPropertiesChange(
            assignee_username=bekbuser2_user.user.username
        ).model_dump_json(),
    )

    assert result.status_code == 400
    assert (
        result.content
        == b'{"detail": "Can not assign a user without pers group to dossier which as pers enabled"}'
    )

    set_pers(dossier.account, user=bekbuser2_user.user, pers=True)

    result = bekbuser1_client.patch(
        reverse(
            "api:change-dossier-properties-v2",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=DossierPropertiesChange(
            assignee_username=bekbuser2_user.user.username
        ).model_dump_json(),
    )

    result = DossierPropertiesResponse.model_validate_json(result.content)

    assert result.assignee.user_username == bekbuser2_user.user.username

    assert (
        UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE").user
        == bekbuser2_user
    )


def test_get_dossier_properties(bekbuser1_client: AuthenticatedClient, bekbuser1_user):
    account, dossier, dossier.owner = prepare_test_dossier_bekb()

    result = bekbuser1_client.get(
        reverse(
            "api:dossier-properties",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
    )

    assert result.status_code == 200

    result = DossierPropertiesResponse.model_validate_json(result.content)

    assert result.assignee

    assert result.assignee.user_username

    assert result.max_expiry_date
    assert result.expiry_date

    UserInvolvement.objects.filter(dossier=dossier, role__key="ASSIGNEE").delete()

    result = bekbuser1_client.get(
        reverse(
            "api:dossier-properties",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
    )

    assert result.status_code == 200

    result = DossierPropertiesResponse.model_validate_json(result.content)

    assert result.assignee is None


def test_change_dossier_assignment_end_to_end(
    bekbuser1_manager_client: AuthenticatedClient, bekbuser1_user, bekbuser2_user
):
    account, dossier, initial_dossier_owner = prepare_test_dossier_bekb()

    # Ensure that the dossier owner is the same as the assignement
    dossier.owner = (
        UserInvolvement.objects.filter(dossier=dossier, role__key="ASSIGNEE")
        .first()
        .user.user
    )
    dossier.save()

    # API user should have access as they are manager
    result = bekbuser1_manager_client.get(
        reverse(
            "api:dossier-properties",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
    )

    assert result.status_code == 200

    dossier_properties_result = DossierPropertiesResponse.model_validate_json(
        result.content
    )

    assert dossier_properties_result.assignee

    inital_assignee_username = dossier_properties_result.assignee.user_username

    # Test behaviour of old api
    result_get_dossier = bekbuser1_manager_client.get(
        reverse(
            "api:get-dossier",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
    )

    result_get_dossier = dossier_schemas.Dossier.model_validate_json(
        result_get_dossier.content
    )

    assert result_get_dossier.owner_username
    assert result_get_dossier.owner_first_name
    assert result_get_dossier.owner_last_name
    assert len(result_get_dossier.dossier_roles) > 1

    result_get_dossier_assignee_role = next(
        (role for role in result_get_dossier.dossier_roles if role.key == "ASSIGNEE"),
        None,
    )

    assert result_get_dossier_assignee_role

    result = bekbuser1_manager_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
    )

    assignable_users = DossierAssignableUsersSchema.model_validate_json(result.content)

    assert len(assignable_users.assignable_users_list) == 9

    # Check that assignable_users_list[0] is a different user

    new_assignable_username = assignable_users.assignable_users_list[2].username
    assert dossier_properties_result.assignee.user_username != new_assignable_username

    assert (
        result_get_dossier_assignee_role.users[0]["username"] != new_assignable_username
    )

    assert (
        inital_assignee_username != assignable_users.assignable_users_list[0].username
    )

    result = bekbuser1_manager_client.patch(
        reverse(
            "api:change-dossier-properties-v2",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=DossierPropertiesChange(
            assignee_username=new_assignable_username
        ).model_dump_json(),
    )

    result = DossierPropertiesResponse.model_validate_json(result.content)

    assert result.assignee.user_username == new_assignable_username

    assert (
        UserInvolvement.objects.get(
            dossier=dossier, role__key="ASSIGNEE"
        ).user.user.username
        == new_assignable_username
    )

    dossier.refresh_from_db()

    # assert dossier.owner != initial_dossier_owner
    assert dossier.owner.username == new_assignable_username

    # Test that we get back the same user that we have set
    result = bekbuser1_manager_client.get(
        reverse(
            "api:dossier-properties",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
    )

    assert result.status_code == 200

    dossier_properties_result = DossierPropertiesResponse.model_validate_json(
        result.content
    )

    assert dossier_properties_result.assignee.user_username == new_assignable_username

    # Test old API
    result_get_dossier = bekbuser1_manager_client.get(
        reverse(
            "api:get-dossier",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
    )

    result_get_dossier = dossier_schemas.Dossier.model_validate_json(
        result_get_dossier.content
    )

    assert result_get_dossier.owner_username == new_assignable_username
    assert result_get_dossier.owner_first_name
    assert result_get_dossier.owner_last_name
    assert len(result_get_dossier.dossier_roles) > 1

    result_get_dossier_assignee_role = next(
        (role for role in result_get_dossier.dossier_roles if role.key == "ASSIGNEE"),
        None,
    )

    assert result_get_dossier_assignee_role

    assert (
        result_get_dossier_assignee_role.users[0]["username"] == new_assignable_username
    )


def test_get_dossier_processing_sample_dossier(testuser1_client: AuthenticatedClient):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    response = testuser1_client.get(
        reverse(
            "api:dossier-processing-details", kwargs={"dossier_uuid": str(dossier.uuid)}
        )
    )

    DossierProcessingDetails.model_validate_json(response.content)


def test_get_dossier_processing_details_success(testuser1_client: AuthenticatedClient):
    user = User.objects.get(username="<EMAIL>")

    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier",
    )

    add_some_fake_semantic_documents(num_docs=1, dossier=dossier)

    extracted_file = ExtractedFile.objects.filter(dossier=dossier).first()

    data = {
        "dossier_uuid": str(dossier.uuid),
        "extracted_file_uuid": str(extracted_file.uuid),
        "type": "Processed",
        "de": "German",
        "en": "English",
        "fr": "French",
        "it": "Italian",
        "exception_type": "VIRUS_DETECTED",
        "details": "SomeDetails",
    }

    add_file_exception(FileExtractedExceptionV1Schema(**data).model_dump_json())

    original_file = OriginalFile.objects.filter(dossier=dossier).first()

    FileException.objects.get(
        dossier__uuid=data.get("dossier_uuid"),
        extracted_file__uuid=data.get("extracted_file_uuid"),
    )

    response = testuser1_client.get(
        reverse(
            "api:dossier-processing-details", kwargs={"dossier_uuid": str(dossier.uuid)}
        )
    )

    assert response.status_code == 200

    DossierProcessingDetails.model_validate_json(response.content)

    data = response.json()

    assert data["dossier_uuid"] == str(dossier.uuid)
    assert len(data["original_files"]) == 1

    original_file_data = data["original_files"][0]
    assert original_file_data["uuid"] == str(original_file.uuid)
    assert original_file_data["file_name"] == original_file.file.name
    assert original_file_data["status"] == original_file.status.upper()

    assert len(original_file_data["extracted_files"]) == 1
    extracted_file_data = original_file_data["extracted_files"][0]
    assert extracted_file_data["uuid"] == str(extracted_file.uuid)
    assert (
        extracted_file_data["path_from_original"] == extracted_file.path_from_original
    )
    assert extracted_file_data["status"] == "Error"
    assert extracted_file_data["file_name"] == extracted_file.file.name


def test_set_document_ready_for_export(
    testuser1_client: AuthenticatedClient, dossier_for_export, mocker: MockerFixture
):
    account = dossier_for_export.account

    # Check that we have a state machine enabled
    assert (
        account.active_semantic_document_work_status_state_machine
        == StateMachine.objects.get(name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS)
    )

    # Create a dossier
    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier_for_export, num_docs=1
    )

    # This is the only doc in the dossier
    semdoc: SemanticDocument = semantic_documents[0]

    in_front_office_state = Status.objects.get(
        key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        state_machine__name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    )

    assert semdoc.work_status == in_front_office_state

    def mock_publish_side_effect(*args, **kwargs) -> str:
        request = worker_schemas.SemanticDocumentPDFRequestV1.model_validate_json(
            kwargs["message"]
        )

        # Returns json dump in format SemanticDocumentPDFResponseV1
        return process_semantic_dossier_pdf_request(
            semantic_document_pdf_request=request
        )

    mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect,
    )

    # export all documents (which should be one)
    response = testuser1_client.post(
        reverse(
            "api:dossier-set-semantic-documents-state-ready-for-export",
            kwargs={"dossier_uuid": str(dossier_for_export.uuid)},
        ),
        content_type="application/json",
    )

    response_json = SemanticDocumentPDFExportRequest.model_validate_json(
        response.content
    )

    assert response.status_code == 200
    assert len(response_json.semantic_document_export_request_uuids) == 1

    semdoc.refresh_from_db()

    semdoc_export_request_uuid = response_json.semantic_document_export_request_uuids[0]
    semdoc_export = SemanticDocumentExport.objects.get(uuid=semdoc_export_request_uuid)
    assert semdoc_export.semantic_document == semdoc

    assert semdoc.work_status.key == SemanticDocumentState.READY_FOR_EXPORT.value
