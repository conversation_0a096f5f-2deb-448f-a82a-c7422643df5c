import pytest

from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser

from dossier.fakes import add_some_fake_semantic_documents
from dossier.helpers import get_hypodossier_exception_from_string
from dossier.management.commands.dossier_event_consumer_v2 import (
    add_file_exception,
    add_processing_error,
    OriginalFileProcessingErrorSchema,
    FileExtractedExceptionV1Schema,
)
from dossier.models import (
    Dossier,
    ExtractedFile,
    FileException,
    FileStatus,
    Account,
    OriginalFile,
    ExceptionDetails,
)

User: AbstractUser = get_user_model()


@pytest.mark.django_db
def test_add_file_exception():
    user = User.objects.get(username="<EMAIL>")

    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier",
    )

    add_some_fake_semantic_documents(num_docs=1, dossier=dossier)

    extracted_file = ExtractedFile.objects.filter(dossier=dossier).first()

    data = {
        "dossier_uuid": str(dossier.uuid),
        "extracted_file_uuid": str(extracted_file.uuid),
        "type": "SomeType",
        "de": "German",
        "en": "English",
        "fr": "French",
        "it": "Italian",
        "exception_type": "VIRUS_DETECTED",
        "details": "SomeDetails",
    }

    add_file_exception(FileExtractedExceptionV1Schema(**data).model_dump_json())

    f_exception = FileException.objects.get(
        dossier__uuid=data.get("dossier_uuid"),
        extracted_file__uuid=data.get("extracted_file_uuid"),
    )

    assert isinstance(f_exception, FileException)
    assert f_exception.type == data.get("type")
    assert f_exception.de == data.get("de")
    assert f_exception.en == data.get("en")
    assert f_exception.fr == data.get("fr")
    assert f_exception.it == data.get("it")
    assert f_exception.exception_type == get_hypodossier_exception_from_string(
        data.get("exception_type")
    )
    assert (
        f_exception.exception_type
        == ExceptionDetails.HypoDossierException.VIRUS_DETECTED
    )
    assert f_exception.details == data.get("details")

    f_status = ExtractedFile.objects.get(uuid=data.get("extracted_file_uuid")).status
    assert f_status == FileStatus.ERROR


@pytest.mark.django_db
def test_add_processing_error_success():
    user = User.objects.get(username="<EMAIL>")

    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier",
    )

    add_some_fake_semantic_documents(num_docs=1, dossier=dossier)

    original_file = OriginalFile.objects.filter(dossier=dossier).first()

    data = {
        "original_file_uuid": str(original_file.uuid),
        "exception_de": "Test Exception DE",
        "exception_en": "Test Exception EN",
    }

    add_processing_error(OriginalFileProcessingErrorSchema(**data).model_dump_json())

    original_file.refresh_from_db()

    assert original_file.status == FileStatus.ERROR
    original_file.exception_de = data.get("exception_de")
    original_file.exception_en = data.get("exception_en")
