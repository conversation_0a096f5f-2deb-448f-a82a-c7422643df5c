import math
from datetime import datetime
from typing import Optional
from django.utils import timezone

from django.contrib import admin, messages

import structlog
from adminsortable.admin import SortableAdmin
from django import forms
from django.conf import settings
from django.contrib.admin.models import LogEntry
from django.db.models import Count
from django.http import HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from django.urls import reverse, path
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.timezone import now
from django.contrib.admin.views.decorators import staff_member_required
from import_export import resources, fields, widgets
from import_export.admin import ImportExportModelAdmin, ExportMixin
from import_export.formats.base_formats import CSV, XLS
from rangefilter.filters import DateRangeFilter

from dossier.admin_helpers import (
    delete_processing_results_for_original_file,
    recreate_processing_results_for_original_file,
)
from dossier.formats import FormattedJSON
from dossier.models import (
    Dossier,
    OriginalFile,
    ExtractedFile,
    PageCategory,
    DocumentCategory,
    FileException,
    DossierFile,
    DossierExport,
    Account,
    DossierUser,
    BusinessCaseType,
    DossierRole,
    UserInvolvement,
    WhiteListAccess,
    AccessDelegation,
    JWK,
    RealestateProperty,
    AssignedRealestatePropertyOriginalFile,
    ProcessedPageCount,
    DossierCopyStatus,
    CopyDossierHistory,
    DossierAccessGrant,
    DossierAccessCheckProvider,
)
from processed_file.models import (
    ProcessedFile,
    ProcessedPage,
    PageObject,
    PageObjectType,
    PageObjectTitle,
)
from semantic_document.models import (
    SemanticDocument,
    SemanticPage,
    SemanticDocumentPageObject,
    SemanticPagePageObject,
)
from semantic_document.services import reset_semantic_document_work_status
from statemgmt.services import check_work_status_transition_consistency
from django.utils.translation import gettext_lazy as _

logger = structlog.get_logger()

admin.site.site_header = "Dossier Manager Admin"

if settings.ENABLE_ADMIN_KEYCLOAK_LOGIN:
    admin.site.login = staff_member_required(
        admin.site.login, login_url=settings.LOGIN_URL
    )


class OriginalFileInline(admin.TabularInline):
    model = OriginalFile
    extra = 0
    show_change_link = True
    fields = ["file", "status"]
    ordering = ["-status"]
    readonly_fields = [
        "file",
    ]


class ExtractedFileInline(admin.TabularInline):
    model = ExtractedFile
    extra = 0
    show_change_link = True
    can_delete = False
    ordering = ["-status"]

    fields = ["file", "status", "original_file"]
    readonly_fields = ["file", "status", "original_file"]


class ExtractedFileExceptionInline(admin.TabularInline):
    model = FileException
    extra = 0
    show_change_link = True
    can_delete = False
    fields = ["extracted_file", "de", "en", "type"]

    def get_readonly_fields(self, request, obj=None):
        return self.fields


class ProcessedFileInline(admin.TabularInline):
    model = ProcessedFile
    extra = 0
    show_change_link = True
    can_delete = False
    fields = ("extracted_file",)
    readonly_fields = ["extracted_file"]


class DossierSoftDeletedFilter(admin.SimpleListFilter):
    title = "Expiry Date"

    # This is mandatory but not used
    parameter_name = "expiry_date_doesnotmatter"

    def lookups(self, request, model_admin):
        return [("yes", "Expired"), ("no", "Not expired")]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(expiry_date__lte=now())
        elif self.value() == "no":
            return queryset.filter(expiry_date__gt=now())
        return queryset


class DossierFileForm(forms.ModelForm):
    class Meta:
        model = DossierFile
        fields = ["data", "dossier", "bucket"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Check if we're being opened from an OriginalFile change form
        referer = (
            self.request.META.get("HTTP_REFERER", "")
            if hasattr(self, "request")
            else ""
        )
        try:
            if "originalfile/change" in referer and "_popup=1" in referer:
                # Extract original_file_id from referer
                parts = referer.split("/")
                for i, part in enumerate(parts):
                    if (
                        part == "originalfile"
                        and i + 1 < len(parts)
                        and parts[i + 1] == "change"
                    ):
                        if i + 2 < len(parts) and parts[i + 2]:
                            original_file_id = parts[i + 2].split("?")[0]
                            try:
                                original_file = OriginalFile.objects.get(
                                    uuid=original_file_id
                                )
                                # Pre-fill dossier and bucket with original file values
                                self.fields["dossier"].initial = original_file.dossier
                                self.fields["bucket"].initial = (
                                    original_file.file.bucket
                                )
                                self.fields["dossier"].widget.attrs["readonly"] = True
                                # Add help text
                                self.fields["data"].help_text = (
                                    "Upload the fixed version of the file that will replace the original"
                                )
                            except OriginalFile.DoesNotExist:
                                pass
        except Exception:
            # If anything goes wrong, just continue without pre-filling
            pass


@admin.register(DossierFile)
class DossierFileAdmin(admin.ModelAdmin):
    form = DossierFileForm
    list_display = ["uuid", "name", "dossier", "bucket", "created_at", "updated_at"]
    ordering = ("-created_at",)
    search_fields = ["uuid", "dossier__uuid", "dossier__name"]

    # Do not put a filter on dossier as this can be > 1000. Instead search for dossier uuid or name
    list_filter = (
        "dossier__account",
        "bucket",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing an existing object
            return [
                "uuid",
                "created_at",
                "updated_at",
                "name",
                "data",
                "dossier",
                "bucket",
            ]
        return ["uuid", "created_at", "updated_at"]  # For new objects

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        form.request = request  # Pass request to form to access HTTP_REFERER
        return form


@admin.register(FileException)
class FileExceptionAdmin(admin.ModelAdmin):
    search_fields = [
        "uuid",
        "en",
        "extracted_file__uuid",
        "extracted_file__dossier__uuid",
        "extracted_file__dossier__name",
        "extracted_file__original_file__uuid",
    ]
    list_display = [
        "uuid",
        "type",
        "exception_type",
        "en",
        "dossier",
        "created_at",
        "updated_at",
    ]
    ordering = ["-created_at"]
    list_filter = [
        "type",
        "exception_type",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        "dossier",
        "dossier__owner",
    ]

    readonly_fields = [
        "dossier",
        "extracted_file",
        "exception_type",
        "en",
        "de",
        "fr",
        "it",
        "details",
        "type",
        "created_at",
        "updated_at",
    ]

    actions = ["recreate_processing_results_for_related_original_file_with_replacement"]

    @admin.action(
        description="Reprocess related original file for selected exceptions (use file replacement if available)"
    )
    def recreate_processing_results_for_related_original_file_with_replacement(
        modeladmin, request, queryset
    ):
        # There could be multiple exceptions in a single original file (e.g. zip with 2 errors).
        # Reprocess every original file exactly once.
        ofs = set()
        for file_ex in queryset:
            file_exception: FileException = file_ex
            original_file = file_exception.extracted_file.original_file
            ofs.add(original_file)

        for original_file in ofs:
            delete_processing_results_for_original_file(original_file)
            recreate_processing_results_for_original_file(original_file)


class BusinessCaseTypeInline(admin.TabularInline):
    model = BusinessCaseType
    extra = 0
    show_change_link = True

    fields = ("key", "name_de", "name_en", "name_fr", "name_it")


class WhiteListAccessInline(admin.TabularInline):
    model = WhiteListAccess
    extra = 0


class JWKInline(admin.TabularInline):  # or admin.StackedInline
    model = JWK
    extra = 0  # how many rows to show


@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    list_display = [
        "key",
        "name",
        "default_bucket_name",
        "default_dossier_expiry_duration_days",
        "document_export_strategy",
        "max_num_pages_allowed_input",
        "created_at",
        "updated_at",
    ]

    inlines = (BusinessCaseTypeInline, WhiteListAccessInline, JWKInline)
    readonly_fields = ["created_at", "updated_at"]

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        form.base_fields["max_num_pages_allowed_input"].required = False
        return form


@admin.register(JWK)
class JWKAdmin(admin.ModelAdmin):
    list_display = ["account", "jwk", "enabled", "created_at", "updated_at"]
    readonly_fields = ["created_at", "updated_at"]

    # form = JWKForm


class ProcessedPageInline(admin.TabularInline):
    model = ProcessedPage
    extra = 0
    fields = ["document_category", "page_category", "number", "lang", "searchable_pdf"]
    ordering = ("number",)
    show_change_link = True

    def get_readonly_fields(self, request, obj=None):
        return self.fields


@admin.register(ProcessedFile)
class ProcessedFileAdmin(admin.ModelAdmin):
    model = ProcessedFile
    readonly_fields = ["dossier", "extracted_file"]

    def get_model_perms(self, request):
        """
        Return empty perms dict thus hiding the model from admin index.
        """
        return {}

    inlines = [
        ProcessedPageInline,
    ]


@admin.register(ExtractedFile)
class ExtractedFileAdmin(admin.ModelAdmin):
    model = ExtractedFile
    readonly_fields = ["dossier", "original_file", "file", "created_at", "updated_at"]
    list_display = [
        "uuid",
        "path_from_original",
        "dossier",
        "status",
        "created_at",
        "updated_at",
    ]
    ordering = ("-created_at",)
    list_filter = [
        "status",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]
    search_fields = [
        "uuid",
        "dossier__uuid",
        "file__data",
        "original_file__uuid",
        "original_file__file__data",
    ]
    inlines = (ProcessedFileInline,)

    actions = ["recreate_processing_results_for_related_original_file_with_replacement"]

    @admin.action(
        description="Reprocess related original file for selected extracted files (use file replacement if available)"
    )
    def recreate_processing_results_for_related_original_file_with_replacement(
        modeladmin, request, queryset
    ):
        # There could be multiple extracted files in a single original file (e.g. zip with 2 files).
        # Reprocess every original file exactly once.
        ofs = set()
        for ex in queryset:
            extracted_file: ExtractedFile = ex
            original_file = extracted_file.original_file
            ofs.add(original_file)

        for original_file in ofs:
            delete_processing_results_for_original_file(original_file)
            recreate_processing_results_for_original_file(original_file)


class SemanticDocumentInline(admin.TabularInline):
    model = SemanticDocument
    show_change_link = True
    extra = 0
    ordering = ["document_category__id", "title_suffix"]
    fields = [
        "uuid",
        "title",
        "document_category",
        "confidence_level",
        "confidence_formatted",
    ]

    def get_readonly_fields(self, request, obj=None):
        return self.fields


class DossierExportInline(admin.TabularInline):
    model = DossierExport
    show_change_link = True
    extra = 0
    fields = ["file", "created_at", "done"]

    def get_readonly_fields(self, request, obj=None):
        return self.fields


@admin.register(BusinessCaseType)
class BusinessCaseTypeAdmin(SortableAdmin):
    list_display = (
        "uuid",
        "account",
        "key",
        "name_de",
        "name_fr",
        "name_it",
        "name_en",
        "created_at",
        "updated_at",
    )
    list_filter = [
        "account",
        "key",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]
    readonly_fields = ["created_at", "updated_at"]


class DossierAdminForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # self.fields['dossier_status'].queryset = Status.objects.filter(state_machine_id=self.instance.account_id)


class RealestatePropertyInline(admin.TabularInline):
    model = RealestateProperty
    readonly_fields = ["dossier"]
    extra = 0


class ExcelDateWidget(widgets.DateWidget):
    def render(self, value, obj=None):
        if value:
            # If it's a datetime, extract the date part.
            if isinstance(value, datetime):
                return value.date()
            return value
        return None


class DossierResourceForExport(resources.ModelResource):
    account_uuid: Optional[str] = None

    created_at = fields.Field(
        attribute="created_at", column_name="created_at", widget=ExcelDateWidget()
    )
    updated_at = fields.Field(
        attribute="updated_at", column_name="updated_at", widget=ExcelDateWidget()
    )

    def get_queryset(self):
        qs = super().get_queryset().order_by("created_at")
        qs = qs.filter(expiry_date__gt=now())
        if self.account_uuid:
            qs = qs.filter(account__uuid=self.account_uuid)
        return qs

    def export(self, queryset=None, *args, **kwargs):
        if queryset is not None:
            queryset = queryset.iterator(chunk_size=1000)
        return super().export(queryset=queryset, *args, **kwargs)

    class Meta:
        model = Dossier
        fields = (
            "uuid",
            "created_at",
            "updated_at",
            "owner__username",
        )


class DossierResource(resources.ModelResource):

    class Meta:
        model = Dossier
        fields = (
            "uuid",
            "created_at",
            "updated_at",
            "name",
            "owner__username",
        )


@admin.register(Dossier)
class DossierAdmin(ExportMixin, admin.ModelAdmin):
    resource_class = DossierResource
    form = DossierAdminForm
    list_display = (
        "uuid",
        "external_id",
        "name",
        # 231025 mt: These have been removed for performance reasons.
        # "original_files",
        # Does not load for 8k dossiers
        # "semantic_documents",
        # "extracted_files",
        "file_exceptions",
        "owner",
        "created_at",
        "expiry_date",
        "created_at",
        "updated_at",
    )
    list_per_page = 25
    ordering = ("-created_at",)
    search_fields = ["uuid", "name", "external_id"]

    actions = ["display_work_status_transitions", "reset_semantic_document_export"]

    @admin.action(description="Display work status transitions")
    def display_work_status_transitions(self, request, queryset):
        uuids = []
        for obj in queryset:
            uuids.append(obj.uuid)

        self.message_user(
            request, f"Show work status transitions for these uuids={uuids}"
        )
        self.message_user(request, "--")

        for dossier_uuid in uuids:
            list_of_transitions, events, e = check_work_status_transition_consistency(
                dossier_uuid
            )
            logger.info("transition of states", transitions=list_of_transitions)
            self.message_user(
                request,
                mark_safe(
                    f"Transition summary for dossier_uuid={dossier_uuid}: {list_of_transitions}"
                ),
            )

            for e in events:
                self.message_user(
                    request,
                    mark_safe(
                        f'Transition detail for dossier_uuid={dossier_uuid}: from_state_key={e.details["from_state_key"]}, to_state_key={e.details["to_state_key"]}, username={e.details["username"]}, created_at={e.created_at.strftime("%Y-%m-%d %H:%M:%S")}'
                    ),
                )

            self.message_user(request, "--")

    @admin.action(description="Reset semantic document exports in Dossier")
    def reset_semantic_document_export(modeladmin, request, queryset):
        for dossier in queryset:
            for semantic_document in dossier.semantic_documents.all():
                reset = reset_semantic_document_work_status(semantic_document)

                if reset:
                    logger.info(
                        f"Reset semantic document export for {semantic_document} and set to work status "
                        f"{semantic_document.dossier.account.active_semantic_document_work_status_state_machine.start_status.key}"
                    )

    inlines = [
        OriginalFileInline,
        ExtractedFileInline,
        ExtractedFileExceptionInline,
        ProcessedFileInline,
        SemanticDocumentInline,
        DossierExportInline,
        RealestatePropertyInline,
    ]

    list_filter = [
        "account",
        "work_status",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        ("expiry_date", DateRangeFilter),
        DossierSoftDeletedFilter,
        "owner",
    ]
    raw_id_fields = ["owner", "doccheck_case"]
    readonly_fields = ["created_at", "updated_at"]

    def get_queryset(self, request):
        return Dossier.objects.all_dossiers.annotate(
            # _count_original_files=Count("original_files", distinct=True),
            # _count_extracted_files=Count("extractedfile", distinct=True),
            _count_fileexceptions=Count("extractedfile__fileexception", distinct=True),
            # _count_semantic_documents=Count("semantic_documents", distinct=True),
        )

    # def extracted_files(self, obj):
    #     return obj._count_extracted_files

    def file_exceptions(self, obj):
        return obj._count_fileexceptions

    file_exceptions.admin_order_field = "_count_fileexceptions"

    # def original_files(self, obj):
    #     return obj._count_original_files
    #
    # original_files.admin_order_field = "_count_original_files"
    #
    # def semantic_documents(self, obj):
    #     return obj._count_semantic_documents


@admin.register(PageCategory)
class PageCategoryAdmin(admin.ModelAdmin):
    list_display = ["id", "name", "created_at", "updated_at"]
    search_fields = ["name", "id"]
    ordering = ["id"]
    readonly_fields = ["created_at", "updated_at"]

    list_filter = [
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]


bbox_fields = ("ref_height", "ref_width", "top", "left", "right", "bottom")


class CoreDocumentCategoryExportResource(resources.ModelResource):
    account_uuid: Optional[str] = None

    def get_queryset(self, request=None):
        qs = super().get_queryset().order_by("id")
        if self.account_uuid:
            qs = qs.filter(account__uuid=self.account_uuid)
        return qs

    class Meta:
        model = DocumentCategory
        exclude = (
            "uuid",
            "created_at",
            "updated_at",
            "account",
            "description_de",
            "description_en",
            "description_fr",
            "description_it",
            "de_external",
            "en_external",
            "fr_external",
            "it_external",
            "additional_search_terms_de",
            "additional_search_terms_en",
            "additional_search_terms_fr",
            "additional_search_terms_it",
        )


class MediumDocumentCategoryExportResource(resources.ModelResource):
    """
    Same as FullDocumentCategoryExportResource but without all fields that are account specific
    """

    account_uuid: Optional[str] = None

    def get_queryset(self, request=None):
        qs = super().get_queryset().order_by("id")
        if self.account_uuid:
            qs = qs.filter(account__uuid=self.account_uuid)
        return qs

    class Meta:
        model = DocumentCategory
        exclude = ("uuid", "created_at", "updated_at", "account")


class FullDocumentCategoryExportResource(resources.ModelResource):
    account_uuid: Optional[str] = None

    def get_queryset(self, request=None):
        qs = super().get_queryset().order_by("id")
        if self.account_uuid:
            qs = qs.filter(account__uuid=self.account_uuid)
        return qs

    class Meta:
        model = DocumentCategory
        # No fields excluded for full export


class ExportForm(forms.Form):
    EXPORT_TYPE_CHOICES = (
        ("full", "Full Export"),
        ("medium", "Medium Export (without account specific fields / ids"),
        ("core", "Core Export"),
    )
    EXPORT_FORMAT_CHOICES = (
        ("csv", "CSV"),
        ("json", "JSON"),
        ("xls", "XLS"),
    )
    export_type = forms.ChoiceField(choices=EXPORT_TYPE_CHOICES, label="Export Type")
    export_format = forms.ChoiceField(
        choices=EXPORT_FORMAT_CHOICES, label="Export Format"
    )


class IdExternalExistsFilter(admin.SimpleListFilter):
    title = _("ID External Exists")  # The title of the filter
    parameter_name = "id_external_exists"  # The URL parameter for the filter

    def lookups(self, request, model_admin):
        """Options for the filter."""
        return (
            ("yes", _("Yes")),
            ("no", _("No")),
        )

    def queryset(self, request, queryset):
        """Filter the queryset based on the user's selection."""
        if self.value() == "yes":
            return queryset.filter(id_external__isnull=False)
        if self.value() == "no":
            return queryset.filter(id_external__isnull=True)
        return queryset


@admin.register(DocumentCategory)
class DocumentCategoryAdmin(ImportExportModelAdmin):
    list_display = [
        "id",
        "name",
        "exclude_for_recommendation",
        "de",
        "en",
        "fr",
        "it",
        "id_external",
        "account",
        "created_at",
        "updated_at",
    ]
    search_fields = ["name", "id", "de", "en", "fr", "it"]
    ordering = ["id"]
    list_filter = [
        "account",
        IdExternalExistsFilter,
        "exclude_for_recommendation",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]
    readonly_fields = ["created_at", "updated_at"]

    change_list_template = "admin/documentcategory_changelist.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                "export/",
                self.admin_site.admin_view(self.export_view),
                name="documentcategory_export",
            ),
        ]
        return custom_urls + urls

    def export_view(self, request):
        if request.method == "POST":
            form = ExportForm(request.POST)
            account_uuid = request.GET.get("account__uuid__exact")
            if form.is_valid():
                export_type = form.cleaned_data["export_type"]
                export_format = form.cleaned_data["export_format"]

                if export_type == "full":
                    resource = FullDocumentCategoryExportResource()
                    resource.account_uuid = account_uuid
                    filename_prefix = "FullDocumentCategory"
                elif export_type == "medium":
                    resource = MediumDocumentCategoryExportResource()
                    resource.account_uuid = account_uuid
                    filename_prefix = "MediumDocumentCategory"
                else:
                    resource = CoreDocumentCategoryExportResource()
                    resource.account_uuid = account_uuid
                    filename_prefix = "CoreDocumentCategory"

                dataset = resource.export()
                if export_format == "csv":
                    export_format = CSV()
                elif export_format == "json":
                    export_format = FormattedJSON()
                elif export_format == "xls":
                    export_format = XLS()
                else:
                    export_format = CSV()  # Default to CSV

                current_date = timezone.now().strftime("%Y-%m-%d")
                filename = (
                    f"{filename_prefix}-{current_date}.{export_format.get_extension()}"
                )

                content = export_format.export_data(dataset)

                response = HttpResponse(
                    content, content_type=export_format.get_content_type()
                )

                response["Content-Disposition"] = f'attachment; filename="{filename}"'

                return response
        else:
            form = ExportForm()

        context = {
            "form": form,
            "opts": self.model._meta,
            "app_label": self.model._meta.app_label,
        }
        return TemplateResponse(
            request, "admin/documentcategory_export_form.html", context
        )

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context["export_form"] = ExportForm()
        return super().changelist_view(request, extra_context=extra_context)


@admin.register(PageObjectType)
class PageObjectTypeAdmin(admin.ModelAdmin):
    list_display = ["name"]
    search_fields = ["name"]
    ordering = ("name",)


@admin.register(PageObjectTitle)
class PageObjectTitleAdmin(admin.ModelAdmin):
    list_display = ["key", "de", "en", "fr", "it"]
    ordering = ("key",)
    search_fields = ["key", "de", "en", "fr", "it"]


page_object_fields = (
    "processed_page",
    "key",
    "value",
    "type",
    "visible",
    "confidence_value",
    "confidence_formatted",
    "confidence_level",
) + bbox_fields


@admin.register(PageObject)
class PageObjectAdmin(admin.ModelAdmin):
    list_display = ("uuid",) + page_object_fields + ("created_at", "updated_at")

    # This does not work for performance reasons
    # list_filter = ["processed_page", "type", "visible"]

    list_filter = [
        "visible",
        "type",
        "confidence_level",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]

    search_fields = [
        "uuid",
        "key__key",
        "processed_page__uuid",
        "processed_page__processed_file__uuid",
        "processed_page__processed_file__dossier__uuid",
    ]

    fields = page_object_fields + ("created_at", "updated_at")

    raw_id_fields = ["processed_page", "key"]
    # def get_readonly_fields(self, request, obj=None):
    #     data = [*self.fields]
    #     read_write_fields = ["visible"]
    #     for name in read_write_fields:
    #         data.pop(data.index(name))
    #     return data
    readonly_fields = ["created_at", "updated_at"]


class PageObjectInline(admin.TabularInline):
    model = PageObject
    extra = 0
    fields = page_object_fields
    readonly_fields = page_object_fields
    show_change_link = True


@admin.register(SemanticPagePageObject)
class SemanticPagePageObjectAdmin(admin.ModelAdmin):
    ordering = ["-created_at"]
    list_display = [
        "semantic_page",
        "page_object",
        "get_page_object_uuid",
        "get_page_object_type",
        "created_at",
        "updated_at",
    ]
    list_filter = [
        "semantic_page__dossier__account",
        "page_object__type",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]

    search_fields = [
        "semantic_page__uuid",
        "page_object__uuid",
        "semantic_page__dossier__uuid",
        "semantic_page__dossier__name",
    ]

    def get_page_object_type(self, obj):
        return obj.page_object.type  # Accessing related model's type attribute

    get_page_object_type.admin_order_field = (
        "page_object__type"  # Allows sorting by this field
    )
    get_page_object_type.short_description = (
        "Page Object Type"  # Display name in the admin
    )

    def get_page_object_uuid(self, obj):
        return obj.page_object.uuid

    get_page_object_uuid.admin_order_field = (
        "page_object__uuid"  # Allows sorting by this field
    )
    get_page_object_uuid.short_description = (
        "Page Object UUID"  # Display name in the admin
    )

    fields = ["semantic_page", "page_object", "created_at", "updated_at"]
    raw_id_fields = ["semantic_page", "page_object"]
    readonly_fields = ["created_at", "updated_at"]


class SemanticPagePageObjectInline(admin.TabularInline):
    model = SemanticPagePageObject
    show_change_link = True
    extra = 0

    # Caution: this must be readonly because extremely demanding query to database
    # (for large amounts of page objects in db)
    readonly_fields = ["page_object"]


@admin.register(SemanticPage)
class SemanticPageAdmin(admin.ModelAdmin):
    def get_model_perms(self, request):
        return {}

    def get_readonly_fields(self, request, obj=None):
        fields = list(
            set(
                [field.name for field in self.opts.local_fields]
                + [field.name for field in self.opts.local_many_to_many]
            )
        )
        fields.remove("page_category")
        fields.remove("document_category")
        return fields

    inlines = (SemanticPagePageObjectInline,)


class SemanticPageInline(admin.TabularInline):
    model = SemanticPage
    show_change_link = True
    extra = 0
    ordering = ("number",)

    fields = [
        "number",
        "processed_page",
        "preview_image",
        "page_category",
        "document_category",
        "lang",
        "confidence_formatted",
        "confidence_level",
        "created_at",
    ]

    def preview_image(self, obj: SemanticPage):
        return mark_safe(
            f'<img width="150px" src="{obj.processed_page.image.fast_url}" />'
        )

    def get_readonly_fields(self, request, obj=None):
        return self.fields


@admin.register(ProcessedPage)
class ProcessedPageAdmin(admin.ModelAdmin):
    list_display = [
        "uuid",
        "processed_file",
        "dossier",
        # , "extracted_file"
    ]
    list_filter = [
        "processed_file__dossier__account",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]

    def dossiers(self, obj):
        return obj.processed_file.dossier

    # def extracted_file(self, x):
    #     return x.processed_file.extracted_file
    # extracted_file.short_description = "ex"

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .prefetch_related("processed_file", "processed_file__dossier")
        )

    inlines = (PageObjectInline, SemanticPageInline)
    # list_display = ["key", "de", "en", "fr", "it"]
    # ordering = ('key',)
    search_fields = [
        "uuid",
        "processed_file__uuid",
        "processed_file__dossier__name",
        "processed_file__dossier__uuid",
    ]
    raw_id_fields = ["processed_file", "document_category", "page_category", "dossier"]
    readonly_fields = [
        "image",
        "searchable_pdf",
        "searchable_txt",
        "created_at",
        "updated_at",
    ]


@admin.register(SemanticDocumentPageObject)
class AggregatedPageObjectsAdmin(admin.ModelAdmin):
    list_display = ["uuid", "created_at", "updated_at", "semantic_document"]
    search_fields = [
        "uuid",
        "semantic_document__uuid",
        "semantic_document__title_suffix",
        "semantic_document__dossier__name",
        "semantic_document__dossier__uuid",
    ]
    ordering = ("-created_at",)
    raw_id_fields = ["semantic_document", "page_object"]
    readonly_fields = ["created_at", "updated_at"]


class AggregatedPageObjects(admin.TabularInline):
    model = SemanticDocumentPageObject
    extra = 0
    readonly_fields = ["page_object"]


class OriginalFileResourceForExport(resources.ModelResource):
    account_uuid: Optional[str] = None

    created_at = fields.Field(
        attribute="created_at", column_name="created_at", widget=ExcelDateWidget()
    )
    updated_at = fields.Field(
        attribute="updated_at", column_name="updated_at", widget=ExcelDateWidget()
    )

    def get_queryset(self):
        qs = super().get_queryset().order_by("created_at")
        qs = qs.filter(dossier__expiry_date__gt=now())
        if self.account_uuid:
            qs = qs.filter(dossier__account__uuid=self.account_uuid)
        return qs

    def export(self, queryset=None, *args, **kwargs):
        if queryset is not None:
            queryset = queryset.iterator(chunk_size=1000)
        return super().export(queryset=queryset, *args, **kwargs)

    class Meta:
        model = OriginalFile
        fields = (
            "uuid",
            "dossier__uuid",
            "created_at",
            "updated_at",
        )


class OriginalFileResource(resources.ModelResource):
    class Meta:
        model = OriginalFile
        fields = (
            "uuid",
            "file__data",
            "dossier__uuid",
            "dossier__external_id",
            "status",
            "created_at",
            "updated_at",
        )


class OriginalFileForm(forms.ModelForm):
    class Meta:
        model = OriginalFile
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if "create_user" in self.fields:
            self.fields["create_user"].required = False


class HasFileReplacementFilter(admin.SimpleListFilter):
    title = "has file replacement"
    parameter_name = "has_replacement"

    def lookups(self, request, model_admin):
        return (
            ("yes", "Yes"),
            ("no", "No"),
        )

    def queryset(self, request, queryset):
        value = self.value()
        if value == "yes":
            return queryset.exclude(file_replacement__isnull=True)
        if value == "no":
            return queryset.filter(file_replacement__isnull=True)
        return queryset


@admin.register(OriginalFile)
class OriginalFileAdmin(ExportMixin, admin.ModelAdmin):
    form = OriginalFileForm
    resource_class = OriginalFileResource
    list_display = [
        "uuid",
        "file_name",
        "dossier",
        "status",
        "created_at",
        "updated_at",
    ]
    list_filter = [
        "status",
        ("exception_de", admin.EmptyFieldListFilter),
        HasFileReplacementFilter,
        "dossier__account",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]
    ordering = ("-created_at",)
    search_fields = [
        "uuid",
        "dossier__name",
        "dossier__uuid",
        "dossier__external_id",
        "file__data",
    ]
    readonly_fields = [
        "dossier",
        "created_at",
        "updated_at",
        "download_link",
        "file_replacement_upload_section",
    ]
    raw_id_fields = ["create_user", "file_replacement"]

    actions = (
        "debuglog",
        "delete_processing_results",
        "recreate_processing_results_with_replacement",
        "recreate_processing_results_exclude_replacements",
    )

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                "<uuid:uuid>/upload_replacement/",
                self.admin_site.admin_view(self.upload_replacement_view),
                name="originalfile_upload_replacement",
            ),
        ]
        return custom_urls + urls

    def upload_replacement_view(self, request, uuid):
        original_file = self.get_object(request, uuid)

        if request.method == "POST" and request.FILES.get("replacement_file"):
            uploaded_file = request.FILES["replacement_file"]

            # Create a new DossierFile
            replacement = DossierFile.objects.create(
                data=uploaded_file,
                dossier=original_file.dossier,
                bucket=original_file.file.bucket,
            )

            # Assign it as the replacement
            original_file.file_replacement = replacement
            original_file.save()

            messages.success(
                request,
                f'Replacement file "{uploaded_file.name}" successfully uploaded',
            )
            return HttpResponseRedirect(
                reverse("admin:dossier_originalfile_change", args=[uuid])
            )

        context = {
            "opts": self.model._meta,
            "original_file": original_file,
            "title": f"Upload Replacement File for {original_file.file.name}",
            "app_label": self.model._meta.app_label,
        }
        return TemplateResponse(
            request,
            "admin/dossier/originalfile/upload_replacement.html",
            context,
        )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing an existing object
            return self.readonly_fields + ["file"]
        return self.readonly_fields

    def get_fields(self, request, obj=None):
        fields = super().get_fields(request, obj)
        if obj:  # Only show file_replacement and download_link in detail view
            fields = list(fields)
            fields_after_file = [
                "file_replacement",
                "file_replacement_upload_section",
                "download_link",
            ]
            for field_name in fields_after_file:
                if field_name in fields:
                    fields.remove(field_name)

            # Find the position of the file field
            file_index = fields.index("file")
            # Insert file_replacement right after file
            if file_index >= len(fields) - 1:
                fields += fields_after_file
            else:
                fields[file_index:file_index] = fields_after_file
        return fields

    def file_replacement_upload_section(self, obj):
        """Display an upload form to upload a file_replacement which will be used instead of file"""
        replace_url = reverse("admin:originalfile_upload_replacement", args=[obj.uuid])
        return format_html(
            "<div>"
            "<p><strong>Use this upload form to upload a 'file replacement' which can be used in reprocessing</strong><br/>(instead of the original file uploaded by the user):</p>"
            '<a href="{}" class="button">Upload file replacement</a>'
            "</div>",
            replace_url,
        )

    file_replacement_upload_section.short_description = "File replacement upload"

    def download_link(self, obj):
        """Generate download links for the original and replacement files"""
        links = []

        # Original file link
        links.append(
            format_html(
                '<strong>Original File:</strong> <a href="{}" target="_blank">Download</a> | <a href="{}" target="_blank">View</a>',
                obj.file.download_url,
                obj.file.fast_url,
            )
        )

        # Replacement file link if available
        if obj.file_replacement:
            links.append(
                format_html(
                    '<br/><strong>Replacement File:</strong> <a href="{}" target="_blank">Download</a> | <a href="{}" target="_blank">View</a>',
                    obj.file_replacement.download_url,
                    obj.file_replacement.fast_url,
                )
            )

        return format_html("".join(links))

    download_link.short_description = "File downloads"

    @admin.action(description="show ids in the log")
    def debuglog(modeladmin, request, queryset):
        for obj in queryset:
            print(obj.uuid)
            messages.info(request, f"Found this id: {obj.uuid}")

    @admin.action(description="Delete processing results")
    def delete_processing_results(modeladmin, request, queryset):
        for original_file in queryset:
            delete_processing_results_for_original_file(original_file)

    @admin.action(description="Reprocess original file (ignore file replacement)")
    def recreate_processing_results_exclude_replacements(modeladmin, request, queryset):
        for original_file in queryset:
            delete_processing_results_for_original_file(original_file)
            recreate_processing_results_for_original_file(
                original_file, use_replacement=False
            )

    @admin.action(
        description="Reprocess original file (use file replacement if available)"
    )
    def recreate_processing_results_with_replacement(modeladmin, request, queryset):
        for original_file in queryset:
            delete_processing_results_for_original_file(original_file)
            recreate_processing_results_for_original_file(
                original_file, use_replacement=True
            )


@admin.register(DossierUser)
class DossierUserAdmin(admin.ModelAdmin):
    list_display = ("uuid", "user", "account", "created_at", "updated_at")
    ordering = ("user__username",)
    list_filter = ("account",)
    search_fields = ("user__username",)
    readonly_fields = ["created_at", "updated_at"]


@admin.register(LogEntry)
class LogEntryAdmin(admin.ModelAdmin):
    # to have a date-based drilldown navigation in the admin page
    date_hierarchy = "action_time"

    # to filter the resultes by users, content types and action flags
    list_filter = ["user", "content_type", "action_flag"]

    # when searching the user will be able to search in both object_repr and change_message
    search_fields = ["object_repr", "change_message"]

    list_display = [
        "action_time",
        "user",
        "content_type",
        "action_flag",
    ]


@admin.register(DossierRole)
class DossierRoleAdmin(SortableAdmin):
    list_display = (
        "account",
        "key",
        "name_de",
        "name_en",
        "name_fr",
        "name_it",
        "user_selectable",
        "show_separate_filter",
        "created_at",
        "updated_at",
    )
    readonly_fields = ["created_at", "updated_at"]
    list_filter = ["account", "key", "created_at"]


@admin.register(UserInvolvement)
class UserInvolvementAdmin(ExportMixin, admin.ModelAdmin):
    list_display = (
        "dossier",
        "username",
        "role_assignment",
        "created_at",
        "updated_at",
    )
    ordering = ("-created_at",)
    list_filter = (
        "user__account",
        "role",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        "user__user__username",
    )
    search_fields = (
        "user__user__username",
        "role__uuid",
        "role__key",
        "dossier__uuid",
        "dossier__name",
    )
    readonly_fields = ["created_at", "updated_at"]
    raw_id_fields = ["dossier", "user", "role"]

    def username(self, obj):
        return obj.user.user.username

    def role_assignment(self, obj):
        return obj.role.key

    # get_realestate_key.short_description = "Real Estate Key"


@admin.register(AccessDelegation)
class AccessDelegationAdmin(admin.ModelAdmin):
    list_display = (
        "uuid",
        "delegator",
        "delegate",
        "expire_time",
        "created_at",
        "updated_at",
    )
    list_filter = ("delegate", "delegator")
    readonly_fields = ["created_at", "updated_at"]


@admin.register(RealestateProperty)
class RealestatePropertyAdmin(admin.ModelAdmin):
    list_display = (
        "key",
        "title",
        "floor",
        "street",
        "street_nr",
        "zipcode",
        "city",
        "created_at",
        "updated_at",
    )
    search_fields = (
        "key",
        "title",
        "street",
        "zipcode",
        "city",
        "dossier__name",
        "dossier__uuid",
    )
    list_filter = [
        "dossier__account",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]
    ordering = ("-created_at",)
    raw_id_fields = ["dossier"]
    readonly_fields = ["created_at", "updated_at"]


@admin.register(AssignedRealestatePropertyOriginalFile)
class AssignedRealestatePropertyOriginalFileAdmin(admin.ModelAdmin):
    list_display = (
        "originalfile",
        "get_dossier_name",
        "get_realestate_key",
        "get_realestate_title",
        "created_at",
        "updated_at",
    )
    search_fields = (
        "originalfile__uuid",
        "originalfile__dossier__name",
        "realestate_property__key",
        "realestate_property__title",
    )
    raw_id_fields = ["originalfile", "realestate_property"]

    def get_realestate_key(self, obj):
        return obj.realestate_property.key

    get_realestate_key.short_description = "Real Estate Key"

    def get_realestate_title(self, obj):
        return obj.realestate_property.title

    def get_dossier_name(self, obj):
        return obj.originalfile.dossier.name

    get_realestate_title.short_description = "Real Estate Title"
    list_filter = ("originalfile__dossier__account", "created_at", "updated_at")
    ordering = ("-created_at",)


@admin.register(ProcessedPageCount)
class ProcessedPageCountAdmin(ExportMixin, admin.ModelAdmin):
    list_display = (
        "account",
        "dossier_uuid",
        "extracted_file_path_from_original",
        "processed_pages",
        "created_at",
        "owner",
    )
    ordering = ("-created_at",)

    readonly_fields = (
        "account",
        "dossier_uuid",
        "extracted_file_path_from_original",
        "processed_pages",
        "extracted_file_uuid",
        "original_file_uuid",
        "external_dossier_id",
        "owner",
    )

    search_fields = [
        "dossier_uuid",
        "external_dossier_id",
        "extracted_file_path_from_original",
        "owner",
    ]

    list_filter = (
        "account",
        "created_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    )


@admin.register(DossierCopyStatus)
class DossierCopyStatusAdmin(admin.ModelAdmin):
    # Table used to keep track of dossier copy process when it's run in the background
    # used by swissfex, hence we keep track of the external id
    list_display = [
        "uuid",
        "source_dossier_uuid",
        "target_external_id",
        "account",
        "done",
        "created_at",
        "updated_at",
    ]
    search_fields = [
        "uuid",
        "source_dossier_uuid",
        "target_external_id",
        "account",
        "done",
    ]
    list_filter = (
        "account",
        "created_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        ("done", DateRangeFilter),
    )
    raw_id_fields = ["target_dossier"]

    readonly_fields = (
        "uuid",
        "created_at",
        "updated_at",
    )


@admin.register(CopyDossierHistory)
class DossierCopyHistoryAdmin(admin.ModelAdmin):
    list_display = [
        "uuid",
        "source_dossier_uuid",
        "source_dossier_external_id",
        "new_dossier_uuid",
        "new_dossier_external_id",
        "account",
        "created_at",
        "updated_at",
        "status",
        "duration",
    ]
    search_fields = [
        "uuid",
        "source_dossier_uuid",
        "source_dossier_external_id",
        "new_dossier_uuid",
        "new_dossier_external_id",
        "status",
    ]

    list_filter = (
        "account",
        "created_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    )

    readonly_fields = (
        "uuid",
        "created_at",
        "updated_at",
    )


class DossierCreatedAtFilter(DateRangeFilter):
    title = "Dossier Created At xxx TITLE DOES NOT WORK"


class DossierExportResource(resources.ModelResource):
    dossier_uuid = fields.Field(attribute="dossier__uuid", column_name="Dossier UUID")
    dossier_name = fields.Field(attribute="dossier__name", column_name="Dossier Name")
    dossier_created_at = fields.Field(
        attribute="dossier__created_at", column_name="Dossier Created At"
    )
    dossier_age_days = fields.Field(
        attribute="dossier__created_at",
        column_name="Dossier Age (Days)",
        widget=widgets.IntegerWidget(),
    )

    def get_queryset(self):
        return super().get_queryset().order_by("created_at")

    class Meta:
        model = DossierExport
        fields = (
            "uuid",
            "done",
            "file",
            "created_at",
            "updated_at",
            "dossier_uuid",
            "dossier_name",
            "dossier_created_at",
            "dossier_age_days",
        )
        export_order = (
            "uuid",
            "done",
            "file",
            "created_at",
            "updated_at",
            "dossier_uuid",
            "dossier_name",
            "dossier_created_at",
            "dossier_age_days",
        )

    def dehydrate_dossier_uuid(self, export):
        return str(export.dossier.uuid)

    def dehydrate_uuid(self, export):
        return str(export.uuid)

    def dehydrate_file(self, export):
        return str(export.file.uuid)

    def dehydrate_dossier_age_days(self, export):
        return (export.created_at - export.dossier.created_at).days + 1


@admin.register(DossierExport)
class DossierExportAdmin(ExportMixin, admin.ModelAdmin):
    resource_class = DossierExportResource

    def get_dossier_url(self, obj):
        if obj.done and obj.file:
            url = obj.file.get_fast_url()
            return format_html('<a href="{}" target="_blank">Dossier URL</a>', url)
        return "N/A"

    get_dossier_url.short_description = "Dossier URL"

    list_display = (
        "uuid",
        "done",
        "file",
        "created_at",
        "updated_at",
        "dossier_uuid",
        "dossier_name",
        "dossier_created_at",
        "dossier_age_days",
        "get_dossier_url",
    )
    ordering = ["-created_at"]

    search_fields = ("uuid", "done", "dossier__uuid")

    list_filter = (
        "done",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        ("dossier__created_at", DossierCreatedAtFilter),
    )

    readonly_fields = ("uuid", "created_at", "updated_at")

    raw_id_fields = ["file", "dossier"]

    def dossier_uuid(self, obj):
        return obj.dossier.uuid

    def dossier_name(self, obj):
        return obj.dossier.name

    def dossier_created_at(self, obj):
        return obj.dossier.created_at

    def dossier_age_days(self, obj):
        # .days rounds down
        return math.ceil((obj.created_at - obj.dossier.created_at).days + 1)

    dossier_uuid.short_description = (
        "Dossier UUID"  # This sets the column header in the admin list view
    )

    def dossier_link(self, obj):
        link = reverse("admin:dossier_dossier_change", args=[obj.dossier.uuid])
        return format_html('<a href="{}">{}</a>', link, obj.dossier.__str__())

    dossier_link.short_description = "Dossier"

    def file_link(self, obj):
        link = reverse("admin:dossier_dossierfile_change", args=[obj.file.uuid])
        return format_html('<a href="{}">{}</a>', link, obj.file.name)

    file_link.short_description = "File"

    class Meta:
        model = DossierExport
        verbose_name = "Dossier Export"
        verbose_name_plural = "Dossier Exports"
        help_text = (
            "Model to keep track of Dossier Export process via Dossier Zipper Worker. "
            "When a request is dispatched to dossier zipper worker, a dossier export is created"
            "Once the processing is completed, the Dossier Export .done field is set via a timetamp."
            "This way we can keep track of progress dossierfiles that are being processed by Dossier Zipper"
        )


@admin.register(DossierAccessCheckProvider)
class DossierAccessCheckProviderAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "created_at",
        "updated_at",
    ]
    search_fields = ("name",)
    ordering = ["name"]
    readonly_fields = ("uuid", "created_at", "updated_at")


@admin.register(DossierAccessGrant)
class DossierAccessGrantAdmin(admin.ModelAdmin):
    list_display = [
        "user",
        "dossier",
        "get_external_dossier_id",
        "get_account",
        "expires_at",
        "has_access",
        "created_at",
        "updated_at",
    ]
    search_fields = ("user", "dossier")
    ordering = ["-created_at"]
    raw_id_fields = ["user", "dossier"]
    list_filter = [
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        "has_access",
        "dossier__account",
    ]

    @admin.display(description="External Dossier ID")
    def get_external_dossier_id(self, obj):
        return obj.dossier.external_id

    @admin.display(description="Account")
    def get_account(self, obj):
        return obj.dossier.account

    def save_model(self, request, obj, form, change):
        try:
            user_accounts = DossierUser.objects.filter(
                user=obj.user, account=obj.dossier.account
            ).first()

            if user_accounts is None:
                self.message_user(
                    request,
                    "The dossier's account must match the user's account.",
                    level=messages.ERROR,
                )
                return
            super().save_model(request, obj, form, change)
        except Exception as e:
            self.message_user(request, str(e), level=messages.ERROR)
