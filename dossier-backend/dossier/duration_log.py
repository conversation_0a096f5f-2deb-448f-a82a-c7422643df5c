import structlog
import time
from dataclasses import dataclass, field
from typing import Dict

logger = structlog.get_logger()


@dataclass
class DurationLog:
    name: str
    start_time: time
    current_time: time
    silent: bool  # If this is True skip all logging
    times: Dict[str, float] = field(default_factory=dict)
    skip_event_duration_threshold_default: float = 0.001

    def __init__(self, name, silent: bool = False):
        self.name = name
        self.silent = silent
        self.start()

    def start(self):
        """
        Optional to restart the log empty
        @return:
        """
        self.start_time = time.time()
        self.current_time = time.time()
        self.times = {}
        self.set_duration_full_now()
        return self

    def add_event(self, type: str, log=False):
        duration = self.get_duration_now()
        self.set_duration_full_now()

        now = time.time()
        self.current_time = now
        if type not in self.times:
            self.times[type] = 0

        self.times[type] += round(duration, 3)
        if not self.silent and log:
            duration = int(now - self.start_time)
            logger.info(f"duration={duration}sec, this={self.times[type]}sec: {type}")

    def log_all_events(
        self, skip_event_duration_threshold=-1, duration_threshold_for_slow_warning=-1
    ):
        if not self.silent:
            self.log_msg(
                f"DurationLog({self.name}): full duration={self.get_duration_full()}",
                duration_threshold_for_slow_warning,
            )
            for key, val in self.times.items():
                custom_threshold_ok = (
                    skip_event_duration_threshold < 0
                    or val > skip_event_duration_threshold
                )
                default_threshold_ok = (
                    self.skip_event_duration_threshold_default < 0
                    or val > self.skip_event_duration_threshold_default
                )

                if custom_threshold_ok or (
                    skip_event_duration_threshold < 0 and default_threshold_ok
                ):
                    self.log_msg(
                        f'    {val} for type "{key}"',
                        duration_threshold_for_slow_warning,
                    )

    def log_msg(self, msg: str, duration_threshold_for_slow_warning=-1):
        if self.is_slow(duration_threshold_for_slow_warning):
            logger.warning("SLOW: " + msg)
        else:
            logger.info(msg)

    def is_slow(self, duration_threshold_for_slow_warning=-1):
        return not (
            duration_threshold_for_slow_warning < 0
            or self.get_duration_now() < duration_threshold_for_slow_warning
        )

    def get_duration_now(self) -> float:
        now = time.time()
        duration = now - self.start_time
        return duration

    def set_duration_full_now(self):
        duration = self.get_duration_now()
        self.times["duration_full"] = round(duration, 3)

    def get_duration_full(self, round_digits: int = 4) -> float:
        """

        @return: Number of seconds between creation of DurationLog and last event
        """
        return round(self.times["duration_full"], round_digits)
