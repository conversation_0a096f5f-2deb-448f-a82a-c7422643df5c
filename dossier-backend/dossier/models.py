import hashlib
from datetime import timed<PERSON>ta
from pathlib import Path
from typing import Callable

import minio
import requests
from adminsortable.models import SortableMixin
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import User
from django.contrib.postgres.fields import ArrayField
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import (
    CASCADE,
    Case,
    DO_NOTHING,
    F,
    OuterRef,
    PROTECT,
    QuerySet,
    SET_NULL,
    Subquery,
    Value,
    When,
)
from django.db.models import Q
from django.db.models.signals import post_delete
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from core.behaviors import Timestampable
from doccheck import models as doccheck_models
from dossier.s3_storage import HyS3Storage, split_bucket_objectname
from dossier.statemachine_types import DOSSIER_READ_ONLY_WORK_STATUS
from projectconfig.jwk import validate_jwk
from projectconfig.settings import DEFAULT_DOSSIER_EXPIRY_DURATION_DAYS
from projectconfig.settings import DEFAULT_VALID_DOSSIER_LANGUAGES
from projectconfig.settings import DEFAULT_VALID_UI_LANGUAGES
from projectconfig.settings import MAX_DOSSIER_EXPIRY_DURATION_DAYS
from statemgmt.models import StateMachine, Status


account_specific_access_checks: dict[str, Callable] = {}


class DossierAccessCheckProvider(Timestampable):
    name = models.CharField(max_length=255, unique=True)

    def __str__(self):
        return self.name


class DossierCustomQuerySet(models.Manager):
    def get_queryset(self):
        filter_date = Q(expiry_date__gte=timezone.now())
        filter_date |= Q(expiry_date__isnull=True)
        qs = QuerySet(self.model).filter(filter_date)
        # There used to be a check for annotate_with_calculated_access_mode
        # however this was removed as to do this correctly we would need to know the user requesting the queryset
        # so we can take DossierAccessGrant into account
        return qs

    @property
    def all_dossiers(self):
        return QuerySet(self.model).all()


def get_default_dossiers_date():
    return timezone.now() + timedelta(days=DEFAULT_DOSSIER_EXPIRY_DURATION_DAYS)


def get_max_dossiers_date():
    return timezone.now() + timedelta(days=MAX_DOSSIER_EXPIRY_DURATION_DAYS)


def get_valid_dossier_languages_default():
    return DEFAULT_VALID_DOSSIER_LANGUAGES


def get_valid_ui_languages_default():
    return DEFAULT_VALID_UI_LANGUAGES


class FileStatus(models.TextChoices):
    PROCESSING = "Processing", _("Processing")
    PROCESSED = "Processed", _("Processed")
    ERROR = "Error", _("Error")


class OriginalFileSource(models.TextChoices):
    API = "API", _("API")
    DMF = "DMF", _("Dossier Management Frontend UI")


class ConfidenceLevel(models.TextChoices):
    LOW = "low", "low"
    MEDIUM = "medium", "medium"
    HIGH = "high", "high"


class Languages(models.TextChoices):
    GERMAN = "DE", _("German")
    ENGLISH = "EN", _("English")
    FRENCH = "FR", _("French")
    ITALIAN = "IT", _("Italian")


class SemanticDocumentSplittingStyle(models.TextChoices):
    DEFAULT = "Default", _("Default")
    NO_SPLITTING = "NoSplitting", _("No Splitting")


class ExportStrategy(models.TextChoices):
    SINGLEPDF = "SINGLEPDF", _(
        "Merge Documents into single PDF  for entire dossier, Export single PDF"
    )
    MULTIPLEPDF = "MULTIPLEPDF", _(
        "Single PDF per Semantic Document, Export multiple PDFs"
    )


class NavigationStrategy(models.TextChoices):
    DEFAULT = "DEFAULT", _(
        "Standard navigation dossier list view / dossier view / dossier details"
    )
    NO_DOSSIER_LIST_BRANDED = "NO_DOSSIER_LIST_BRANDED", _(
        "Only access to dossier view, not to the dossier list view. Application is Hypodossier branded"
    )
    NO_DOSSIER_LIST_UNBRANDED = "NO_DOSSIER_LIST_UNBRANDED", _(
        "Only access to dossier view, not to the dossier list view. Application is whitelabelled (not Hypodossier branded)"
    )


class ProcessingStrategy(models.TextChoices):
    DEFAULT = "DEFAULT", _("DEFAULT: Documents are processed directly after upload")
    SUSPENDED_ALL = "SUSPENDED_ALL", _(
        "SUSPENDED_ALL: Automatic processing after upload is disabled"
    )
    # Later additional options to e.g. allow upload via API but not UI can be added


class DossierCloseStrategy(models.TextChoices):
    # This is the base case if nothing needs to be checked
    DEFAULT = "DEFAULT", _("DEFAULT: Basic strategy, no requirements")

    # This means the dossier close operation is only valid if all semantic documents have been
    # successfully exported before
    REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE = "REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE", _(
        "REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE"
    )

    # This means that just before closing the dossier all exports need to be successfully triggered
    # but we do not require them to have finished.
    EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE = (
        "EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE",
        _("EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE"),
    )


# Strategy for exporting semantic documents from a dossier via export_semantic_documents endpoint
# and via UI button
class SemanticDocumentExportStrategy(models.TextChoices):
    DEFAULT = "DEFAULT", _("DEFAULT: export as pdf")
    SWISSCOM_EDOSSIER_XML_ZIP = "SWISSCOM_EDOSSIER_XML_ZIP", _(
        "SWISSCOM_EDOSSIER_XML_ZIP: Export as zip with pdf and SwissCom xml"
    )


class DossierAccessCheckErrorComponent(models.TextChoices):
    DEFAULT = "default", "default"
    BCGE_ACCESS_CHECK_INSTRUCTIONS = (
        "bcge_access_check_instructions",
        "bcge_access_check_instructions",
    )
    FINNOVA_ACCESS_CHECK_INSTRUCTIONS = (
        "finnova_access_check_instructions",
        "finnova_access_check_instructions",
    )


class Account(Timestampable):
    key = models.CharField(
        max_length=255,
        unique=True,
        default="default",
        help_text="Unique identifier for the account which should be used to identify the account in code. Must be URL compatible.",
    )

    dmf_endpoint = models.CharField(max_length=255, blank=True, null=True)

    name = models.CharField(
        max_length=255,
        unique=True,
        help_text="Pretty name of account. Must not be used as unique identifier in code (use key instead).",
    )

    default_bucket_name = models.CharField(
        max_length=255,
        help_text="Name of S3 bucket in which all files related to this account will be stored.",
    )
    default_dossier_expiry_duration_days = models.PositiveIntegerField(
        default=DEFAULT_DOSSIER_EXPIRY_DURATION_DAYS,
        help_text="Number of days after the created_at that the expiry date will be set to by default after a dossier is created.",
    )
    max_dossier_expiry_duration_days = models.PositiveIntegerField(
        default=MAX_DOSSIER_EXPIRY_DURATION_DAYS,
        help_text="Maximum number of days that the expiry date can be (manually) set to after the created_at. Dates later than that cannot be selected by the user.",
    )

    frontend_theme = models.CharField(
        max_length=20,
        default="",
        blank=True,
        # Currently supported values are "" == "DEFAULT", "BCGE", "FINNOVA"
        help_text="If empty, the standard theme is used in the frontend. Else custom theme with individual styling will be applied.",
    )

    photo_album_docx_template = models.CharField(
        max_length=255,
        default="photo-album-docx-template-default-v01.docx",
        blank=True,
        help_text="If empty, no Word-Template based photo album download is available. Else set name of deployed template. Default is 'photo-album-docx-template-default-v01.docx', alternative option is 'photo-album-docx-template-hbl-v01.docx'.",
    )

    instructions_menu_key = models.CharField(
        max_length=20,
        default="default",
        blank=True,
        help_text="If empty, no instructions '?' menu is shown. Alternative options for different menu content are 'default' (regular instructions), 'zkb' (custom zkb menu, German only)",
    )

    valid_dossier_languages = ArrayField(
        models.TextField(
            max_length=255,
            help_text="Comma separated list of languages that are available to name the documents in the dossier. If only one language is given, the field is invisible. Default: 'De,En,Fr,It'",
        ),
        default=get_valid_dossier_languages_default,
    )
    valid_ui_languages = ArrayField(
        models.TextField(
            max_length=255,
            help_text="Comma separated list of user interface languages for the application. If only one language is given, the language selection is invisible and instructions, video are only shown for this language (else all languages). Default: 'de,en,fr,it'",
        ),
        default=get_valid_ui_languages_default,
    )

    show_document_category_external = models.BooleanField(
        default=False,
        help_text="If True, value 'de_external' of document category will be shown in addition to 'de' in certain contexts (e.g. rename dialog).",
    )

    show_business_case_type = models.BooleanField(
        default=False,
        help_text="If True the field will be shown on dossier creation and in dossier properties and available as filtering option in dossier list view.",
    )

    document_export_strategy = models.CharField(
        choices=ExportStrategy.choices,
        default=ExportStrategy.MULTIPLEPDF,
        max_length=12,
        verbose_name="Document Export strategy",
        help_text="Should documents be exported as multiple PDFs or as a single merged PDF?",
    )

    navigation_strategy = models.CharField(
        choices=NavigationStrategy.choices,
        default=NavigationStrategy.DEFAULT,
        max_length=40,
        verbose_name="Navigation strategy",
        help_text="Controls the look and feel of the application and decides if the dossier list view is accessible",
    )

    processing_strategy = models.CharField(
        choices=ProcessingStrategy.choices,
        default=ProcessingStrategy.DEFAULT,
        max_length=20,
        verbose_name="Processing strategy",
        help_text="Configures the automatic processing of uploaded files. Can be used to e.g. disable the processing temporarily. For SUSPENDED_ALL:  No messages are sent to processing queue, documents stay in status 'Processing' until processed manually.",
    )

    enable_virus_scan = models.BooleanField(
        default=False,
        help_text="If True every input file is scanned for viruses in processing. If a virus is found, the processing of this file is aborted.",
    )

    enable_button_create = models.BooleanField(
        default=True,
        help_text="If True show 'Create Dossier' button in the 'dossier list view'. Else creation of dossiers in UI is not possible.",
    )
    enable_feedback_form = models.BooleanField(default=False)
    enable_download_original_file_link = models.BooleanField(
        default=True,
        help_text="If True show link to download the original (uploaded) files. Else no link is shown.",
    )
    enable_show_deleted_elements = models.BooleanField(default=False)
    enable_dossier_sorting = models.BooleanField(default=False)
    enable_error_detail = models.BooleanField(default=True)
    enable_dossier_search = models.BooleanField(default=False)
    enable_zoom_feature = models.BooleanField(default=True)
    enable_debug_document = models.BooleanField(default=True)

    # TODO: is this a duplicate flag with account.enable_button_download?
    enable_download_dossier = models.BooleanField(default=True)

    # To be removed in separate ticket.
    enable_download_document = models.BooleanField(default=False)

    enable_icons_on_page_view = models.BooleanField(default=True)

    # TODO: check if this is duplicate with 'enable_document_upload'
    enable_uploading_files = models.BooleanField(default=True)

    enable_drag_and_drop_in_page_view = models.BooleanField(default=True)
    enable_rendering_structure_tab = models.BooleanField(
        default=True,
        help_text="If True show the 'simple structure tab' without document view functionality. Else tab is not visible.",
    )
    enable_hovered_section_on_page_view = models.BooleanField(default=True)
    enable_rendering_hurdles_tab = models.BooleanField(default=True)
    enable_area_calculator = models.BooleanField(
        default=True,
        help_text="If True show actions to calculator (detail view and 'plans tab'. Else actions are not visible.",
    )
    enable_semantic_document_confidence = models.BooleanField(
        default=False,
        help_text="If True show the confidence indicator (circles) for semantic documents. Else no information about confidence is shown.",
    )
    enable_semantic_document_export = models.BooleanField(
        default=False,
        help_text="If true show an export button 'Transfer to archive' with customized wording based on frontend_theme. Else no export button is shown.",
    )
    enable_semantic_document_export_unknown_documents = models.BooleanField(
        default=False,
        help_text="If true unknown semantic documents (all languages) are exported in the export process. Else only semantic documents classified as not-unknown are exported.",
    )

    dossier_close_strategy = models.CharField(
        choices=DossierCloseStrategy.choices,
        default=DossierCloseStrategy.DEFAULT,
        max_length=50,
        verbose_name="Dossier Close Strategy",
        help_text="Defines which requirements must be met before a dossier can be closed.",
    )
    dossier_close_expiry_days = models.FloatField(
        default=7.0,
        verbose_name="Dossier Expiry after 'dossier close' event in days (float)",
        help_text="Expiry after dossier close in days. E.g. 1.5 means 36 hours into the future.",
    )

    enable_bekb_export = models.BooleanField(default=False)
    enable_bekb_automatic_collateral = models.BooleanField(default=False)
    enable_download_extraction_excel = models.BooleanField(
        default=True,
        help_text="If True '000 Hypodossier Datenextraktion.xlsx' is part of the ZIP file downloadable as dossier download in the UI. Else this file is not added to ZIP.",
    )
    enable_dossier_permission = models.BooleanField(
        default=False,
        help_text="If True the access delegation feature is activated for the account and users can delegate access to other users. Else users have only access to their own dossiers. Better name would be 'enable_access_delegation'.",
    )
    enable_button_open_in_new_tab = models.BooleanField(
        default=True,
        help_text="If True the action 'Open in new tab' per semantic document is available in the document menu. Else action is not visible.",
    )
    enable_rendering_photos_tab = models.BooleanField(
        default=True,
        help_text="If True show the 'photos tab' that shows all photo page objects in dossier. Else tab is not visible.",
    )
    enable_rendering_structure_details_tab = models.BooleanField(
        default=False,
        help_text="If True show the 'extended structure with details tab' that allows browsing the documents. Else tab is not visible.",
    )
    enable_rendering_bekb_mortgage_archiving_tab = models.BooleanField(
        default=False,
        help_text="If True show the 'Archiving tab' for the BEKB mortgage process, everything related to BEKB collaterals and the BEKB business case selection. Else tab is not visible and BEKB business case also not visible. This is True for BEKB mortgage and False for BEKB Fipla and all non-BEKB accounts.",
    )
    enable_button_dossier_settings = models.BooleanField(
        default=True,
        help_text="If True show the Settings button on the 'dossier view'. Else settings are still accessible by menu next to dossier name.",
    )
    enable_button_dossier_notes = models.BooleanField(
        default=True, help_text="If True show the Notes button on the 'dossier view'"
    )
    enable_button_download = models.BooleanField(
        default=True,
        help_text="If True show the Download button for a single semantic document on the 'dossier view' (in context menu). This is NOT the button to download the whole dossier.",
    )
    enable_document_upload = models.BooleanField(
        default=True,
        help_text=_(
            "If True show the upload dropzone in 'dossier view'. Else do not show upload zone and move buttons for settings, notes, download to the header line."
        ),
    )
    enable_documents_delta_view = models.BooleanField(
        default=False,
        help_text=_(
            "If True show document filter by date drop-down  in 'dossier view'"
        ),
    )

    enable_semantic_page_image_lazy_loading = models.BooleanField(
        default=False,
        help_text=_(
            "If True use the lazy loading feature for page images. Else all images are loaded directly. Applies to dossier view and detail view."
        ),
    )

    # This is mostly a ZKB feature - they want this enabled initially and disabled later
    # https://gitlab.com/hypodossier/document-universe/-/issues/351
    document_download_ui_add_uuid_suffix = models.BooleanField(
        default=False,
        help_text=(
            _(
                "Whether semantic pdfs generated by dossier zipper worker should have uuid added as a suffix when "
                "downloaded via UI."
            ),
        ),
    )

    enable_download_metadata_json = models.BooleanField(
        default=True,
        help_text=_(
            "Whether HypoDossierData.json will be included in downloaded dossier ZIP file"
        ),
    )

    enable_rendering_plans_tab = models.BooleanField(
        default=False,
        help_text="If True show the 'plans tab' that shows all plans page objects in dossier. Else tab is not visible.",
    )

    enable_real_estate_properties = models.BooleanField(
        default=False,
        help_text="If True show the real estate property selection.",
    )

    enable_dossier_assignment = models.BooleanField(
        default=True,
        help_text="If True show the user can reassign the 'ASSIGNEE' in the dossier properties dialog. Else the 'assign to myself' option is not shown.",
    )

    enable_dossier_assignment_to_someone_else = models.BooleanField(
        default=False,
        help_text="If True show the user can reassign the 'ASSIGNEE' in the dossier properties dialog - even to another person. Else the 'assign' dropdown is not shown.",
    )

    enable_doccheck_in_statemgmt = models.BooleanField(
        default=True,
        help_text="If True the result of the document completeness check is used in state transitions of the state machine. Else the 'is_doccheck_fulfilled' state condition is always true.",
    )

    # Defines state machine for all Dossiers in account
    active_work_status_state_machine = models.ForeignKey(
        StateMachine,
        on_delete=SET_NULL,
        blank=True,
        null=True,
        help_text="State machine for all dossiers in an account.",
        related_name="accounts_with_active_work_status",
    )

    # https://gitlab.com/hypodossier/document-universe/-/issues/537
    # State machine for all semantic documents in an account. Currently used by BCGE
    active_semantic_document_work_status_state_machine = models.ForeignKey(
        StateMachine,
        on_delete=SET_NULL,
        blank=True,
        null=True,
        help_text="State machine for all semantic documents in an account.",
        related_name="accounts_with_active_semantic_document_status",
    )

    active_doc_check = models.ForeignKey(
        doccheck_models.DocCheck, on_delete=SET_NULL, blank=True, null=True
    )

    allow_dossier_listing = models.BooleanField(
        default=True,
        help_text="If True allow to query the dossier list else it is not allowed by default (there can be exceptions e.g dossier manager role)",
    )

    dossier_access_check_provider = models.ForeignKey(
        DossierAccessCheckProvider, on_delete=SET_NULL, blank=True, null=True
    )

    cdp_field_set = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Name of the field set used for the CDP integration.",
    )

    enable_form_tab = models.BooleanField(
        default=False,
        help_text="If True show the form tab (cdp) in the DMF.",
    )
    enable_semantic_document_splitting = models.BooleanField(
        default=False,
        help_text="If True give access to the document splitting tool for a semantic document on the 'dossier view' (in context menu).",
    )

    dossier_access_check_error_component = models.CharField(
        max_length=64,
        choices=DossierAccessCheckErrorComponent.choices,
        default=DossierAccessCheckErrorComponent.DEFAULT,
        help_text="Custom error message (with instructions) provided by frontend in case of access check error.",
    )

    enable_custom_semantic_document_date = models.BooleanField(
        default=False,
        help_text="If True the user can set the date of a semantic document (e.g in the document rename modal). If it hasn't been set, a dynamically computed value is used instead.",
    )

    enable_pdfa_conversion_for_export = models.BooleanField(
        default=False,
        help_text="Whether pdfs should be converted to pdf-a format when exporting a dossier or semantic document",
    )
    enable_semantic_document_annotations = models.BooleanField(
        default=False,
        help_text="Whether the user has the option to add highlights and comments to the semantic documents in the detail view, and whether these annotations get exported with the document.",
    )

    # Strategy for exporting semantic documents from a dossier via export_semantic_documents endpoint
    # and via UI button
    semantic_document_export_strategy = models.CharField(
        choices=SemanticDocumentExportStrategy.choices,
        default=SemanticDocumentExportStrategy.DEFAULT,
        max_length=50,
        verbose_name="Semantic Document Export Strategy",
        help_text="Strategy for exporting semantic documents from a dossier. E.g export as a pdf, or as a zip with xml etc.",
    )

    max_num_pages_allowed_input = models.IntegerField(
        default=None,
        null=True,
        help_text="Maximum number of pages that are accepted in a single document for processing. If longer documents are received processing will abort. If None, the global setting inside the processing system is used.",
    )

    def __str__(self):
        return self.name


class JWK(Timestampable):
    """Model to store the JWK and associated with an account"""

    account = models.ForeignKey(Account, on_delete=CASCADE, related_name="jwks")
    # Store a single JWK
    jwk = models.JSONField(validators=[validate_jwk])
    enabled = models.BooleanField(default=True)
    description = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = "JWK"
        verbose_name_plural = "JWK"


class WhiteListAccess(Timestampable):
    account = models.ForeignKey(
        Account, on_delete=CASCADE, related_name="whitelist_accesses"
    )
    jwt_attribute = models.CharField(max_length=255)
    regex = models.CharField(max_length=255)


class DossierUser(Timestampable):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["account", "user"], name="unique_user_per_account"
            ),
        ]

    account = models.ForeignKey(Account, on_delete=CASCADE)
    user = models.ForeignKey(User, on_delete=CASCADE)

    def __str__(self):
        return f"{self.user} {self.account}"


class DossierRole(Timestampable, SortableMixin):
    class Meta:
        ordering = ["order"]
        constraints = [
            models.UniqueConstraint(
                fields=["account", "key"], name="unique_dossier_roles_per_account"
            ),
        ]

    account = models.ForeignKey(Account, on_delete=CASCADE)
    key = models.CharField(max_length=255)
    name_de = models.CharField(max_length=255, blank=True, null=True)
    name_en = models.CharField(max_length=255, blank=True, null=True)
    name_fr = models.CharField(max_length=255, blank=True, null=True)
    name_it = models.CharField(max_length=255, blank=True, null=True)

    user_selectable = models.BooleanField(default=False)
    show_separate_filter = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0, db_index=True, editable=False)

    def __str__(self):
        return f"{self.key} {self.account}"


class BusinessCaseType(Timestampable, SortableMixin):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["account", "key"], name="unique_businesscase"
            ),
        ]
        ordering = ["order"]

    account = models.ForeignKey(Account, on_delete=CASCADE)
    key = models.CharField(max_length=255)
    name_de = models.CharField(max_length=255, blank=True, null=True)
    description_de = models.TextField(blank=True, null=True)
    name_en = models.CharField(max_length=255, blank=True, null=True)
    description_en = models.TextField(blank=True, null=True)
    name_fr = models.CharField(max_length=255, blank=True, null=True)
    description_fr = models.TextField(blank=True, null=True)
    name_it = models.CharField(max_length=255, blank=True, null=True)
    description_it = models.TextField(blank=True, null=True)

    order = models.PositiveIntegerField(default=0, editable=False)

    def translated_name(self, lang):
        language = lang.lower()
        if language not in ["name_de", "name_en", "name_fr", "name_it"]:
            raise ValueError(f"unsupported language {language}")
        return getattr(self, language) or f"{getattr(self, 'name_de')}"

    def translated_description(self, lang):
        language = lang.lower()
        if language not in [
            "description_de",
            "description_en",
            "description_fr",
            "description_it",
        ]:
            raise ValueError(f"unsupported language {language}")
        return getattr(self, language) or f"{getattr(self, 'description_de')}"

    def __str__(self):
        return f"{self.account}/{self.key}"


class Dossier(Timestampable):
    default_name = "Dossier vom"
    account = models.ForeignKey(Account, on_delete=CASCADE)
    owner = models.ForeignKey(get_user_model(), on_delete=CASCADE, default=1)

    dossierfile_set: models.QuerySet["DossierFile"]

    class DossierAccessMode(models.TextChoices):
        READ_WRITE = "read_write", "read_write"
        READ_ONLY = "read_only", "read_only"

    access_mode = models.CharField(
        max_length=20,
        choices=DossierAccessMode.choices,
        default=DossierAccessMode.READ_WRITE,
    )

    # Note: there are two work status fields, one on the semantid document and one on the dossier
    work_status = models.ForeignKey(Status, on_delete=PROTECT, blank=True, null=True)

    expiry_date = models.DateTimeField(default=get_default_dossiers_date, db_index=True)

    # TODO: remove this field, not needed anymore as this is calculated on request
    max_expiry_date = models.DateTimeField(default=get_max_dossiers_date)

    lang = models.CharField(
        max_length=2, choices=Languages.choices, default=Languages.GERMAN
    )

    bucket = models.CharField(max_length=255, default="dossier")
    name = models.CharField(max_length=255, default=default_name)
    note = models.TextField(null=True, blank=True)
    photos_sorting = models.BooleanField(default=False)

    businesscase_type = models.ForeignKey(
        BusinessCaseType, blank=True, null=True, on_delete=PROTECT
    )
    doccheck_case = models.OneToOneField(
        doccheck_models.Case, blank=True, null=True, on_delete=PROTECT
    )

    external_id = models.CharField(max_length=255, blank=True, null=True)

    # The objects manager hides all dossiers which are expired
    # I consider it to be an anti-pattern to have a default manager which hides objects
    # However it's extensively used in the system
    objects = DossierCustomQuerySet()
    # Use Dossier._base_manager.all() to get unfiltered queryset
    # Its possible to set multiple custom managers
    # https://docs.djangoproject.com/en/4.2/topics/db/managers/

    class Meta:
        unique_together = ["account", "external_id"]

    @property
    def _name(self):
        if self.name is self.default_name:
            return f"{self.default_name} {self.created_at}"
        return self.name

    def __str__(self):
        return self._name

    @staticmethod
    def get_by_uuid(uuid):
        """
        Dossier.objects.get(uuid=xxx) will only return non-deleted dossiers. Use this method to get
        deleted and non-deleted dossiers.
        @param uuid:
        @return:
        """
        return Dossier._base_manager.get(uuid=uuid)


# Todo: These should be unique together e.g.
# unique together (dossier, role, user)
# or unique together (dossier, role), so that only one user at a time can be assigned to a role
class UserInvolvement(Timestampable):
    dossier = models.ForeignKey(Dossier, on_delete=CASCADE)
    user = models.ForeignKey(DossierUser, on_delete=CASCADE)
    role = models.ForeignKey(DossierRole, on_delete=CASCADE)


class ExceptionDetails(models.Model):
    dossier = models.ForeignKey(Dossier, on_delete=CASCADE)

    class HypoDossierException(models.IntegerChoices):
        UNKNOWN_EXCEPTION = 1
        NOT_READABLE = 2
        PASSWORD_PROTECTED = 3
        UNSUPPORTED_FILETYPE = 4
        TOO_SMALL_FILE = 5
        XLSM_FILE_CANNOT_BE_CONVERTED = 6
        OCR_FILETYPE_PROCESSING = 7
        VIRUS_DETECTED = 8
        TOO_MANY_PAGES_IN_DOCUMENT = 9

        # This is an exception that we do not know about
        UNMAPPED_EXCEPTION = 999

    exception_type = models.IntegerField(
        choices=HypoDossierException.choices,
        default=HypoDossierException.UNKNOWN_EXCEPTION,
    )
    en = models.TextField()
    de = models.TextField()
    fr = models.TextField(blank=True, null=True, default=None)
    it = models.TextField(blank=True, null=True, default=None)
    # Do NOT, expose details to the frontend, it might contain sensitive information (identified
    # by security audit)
    details = models.TextField(blank=True, null=True)

    class Meta:
        abstract = True


minio_client = minio.Minio(
    settings.S3_ENDPOINT,
    settings.S3_ACCESS_KEY,
    settings.S3_SECRET_KEY,
    secure=settings.S3_SECURE,
    region=settings.S3_REGION,
)


def generate_path(instance, filename):
    assert instance.bucket
    return f"{instance.bucket}/{instance.dossier_id}/files/{instance.uuid}/{filename}"


class DossierFile(Timestampable):
    # Wrapper for a physical file in the S3 bucket
    dossier = models.ForeignKey(Dossier, on_delete=CASCADE)
    bucket = models.CharField(max_length=255, help_text="S3 bucket name")

    @property
    def _bucket(self):
        return self.bucket

    @property
    def _prefix(self):
        return f"{self.dossier_id}/files/{self.uuid}/"

    # @property
    # def permanent_url(self):
    #     return reverse('api:get-file',
    #                    kwargs={'dossier_uuid': str(self.dossier_id), 'dossier_file_uuid': str(self.uuid)})

    @property
    def name(self) -> str:
        """returns the filename of the dossier file (without bucket or path)"""
        return Path(self.data.name).name

    data = models.FileField(
        blank=True,
        null=True,
        storage=HyS3Storage(),
        upload_to=generate_path,
        max_length=1024,
    )

    @property
    def put_url(self):
        bucket, object_name = split_bucket_objectname(self.data.name)
        presigned_url = minio_client.presigned_put_object(bucket, object_name)
        if settings.S3_ENDPOINT_PROXY:
            presigned_url = presigned_url.replace(
                settings.S3_ENDPOINT, settings.S3_ENDPOINT_PROXY
            )
        return presigned_url

    def get_fast_url(self, response_headers=None):
        bucket, object_name = split_bucket_objectname(self.data.name)
        request_date = timezone.now()

        # if the url does not expire for an entire day, activate caching else deactivated browser caching
        if (settings.S3_URL_EXPIRATION_SECONDS) == 86400:
            request_date = request_date.replace(minute=0, second=0, microsecond=0)

        presigned_url = minio_client.presigned_get_object(
            bucket,
            object_name,
            response_headers=response_headers,
            request_date=request_date,
            expires=timedelta(seconds=settings.S3_URL_EXPIRATION_SECONDS),
        )

        if settings.S3_ENDPOINT_PROXY:
            presigned_url = presigned_url.replace(
                settings.S3_ENDPOINT, settings.S3_ENDPOINT_PROXY
            )
        return presigned_url

    @property
    def fast_url(self):
        return self.get_fast_url()

    @property
    def download_url(self):
        return self.get_fast_url(
            response_headers={"response-content-disposition": "attachment"}
        )

    def sha256sum(self):
        response = requests.get(self.fast_url)
        return hashlib.sha256(response.content).hexdigest()


class DossierExport(Timestampable):
    # Model to keep track of Dossier Export process via Dossier Zipper Worker.
    # When a request is dispatched to dossier zipper worker, a dossier export is created
    # Once the processing is completed, the Dossier Export .done field is set via a timetamp.
    # This way we can keep track of progress dossierfiles that are being processed by Dossier Zipper
    dossier = models.ForeignKey(Dossier, on_delete=CASCADE, related_name="exports")
    file = models.ForeignKey(DossierFile, on_delete=CASCADE, blank=True, null=True)
    done = models.DateTimeField(blank=True, null=True)


class DossierCopyStatus(Timestampable):
    # Table used to keep track of dossier copy process when it's run in the background
    # used by swissfex, hence we keep track of the external id
    source_dossier_uuid = models.UUIDField()
    target_external_id = models.CharField(max_length=255, blank=True, null=True)
    target_dossier = models.ForeignKey(
        Dossier, on_delete=CASCADE, null=True
    )  # we keep a nullable reference to the target dossier so that we can cleanup upon target dossier deletion
    account = models.ForeignKey(Account, on_delete=CASCADE)
    done = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"{self.target_external_id} {self.account.name} {self.done}"


class OriginalFile(Timestampable):
    """
    Represents an original file uploaded to a dossier, with support for file replacement.

    This model manages both the original uploaded file and an optional replacement file.
    The replacement feature allows fixing unrecoverable processing errors by:
    1. Keeping the original file reference intact (preserving file history)
    2. Adding a replacement file that will be used for processing instead
    3. Maintaining all relationships to extracted files, processed pages, etc.

    The replacement workflow typically involves:
    1. Download the original file that failed processing
    2. Fix it locally (e.g., converting problematic PDF pages to images)
    3. Upload the fixed version as a replacement
    4. Reprocess using the replacement while maintaining original file references
    """

    dossier = models.ForeignKey(
        Dossier, on_delete=CASCADE, related_name="original_files"
    )

    # The original uploaded file that will always be preserved for reference
    file = models.ForeignKey(DossierFile, on_delete=CASCADE)

    # Optional replacement file that can be used instead of the original for processing
    # When set, this file will be used for reprocessing while maintaining all references
    # to the original file (extracted_files, processed_pages, semantic_pages, etc.)
    file_replacement = models.ForeignKey(
        DossierFile,
        on_delete=CASCADE,
        null=True,
        blank=True,
        related_name="original_files_replacement",
        help_text="Replacement file to use for processing instead of the original. "
        "Allows fixing unrecoverable processing errors while maintaining original file references.",
    )

    status = models.CharField(
        max_length=10,
        choices=FileStatus.choices,
        default=FileStatus.PROCESSING,
    )

    # only set if status is error
    exception_en = models.TextField(null=True, blank=True)
    exception_de = models.TextField(null=True, blank=True)
    exception_fr = models.TextField(null=True, blank=True)
    exception_it = models.TextField(null=True, blank=True)
    # Do NOT, expose exception_details to the frontend, it might contain sensitive information (identified
    # by security audit)
    exception_details = models.TextField(null=True, blank=True)

    details = models.TextField(blank=True, null=True)

    # Optional field: If provided the external_semantic_document_id will be set on the semantic_document after
    # processing. Should only be used if it is certain that only a single external document is created
    # Needs to be re-applied if reprocessing happens.
    force_external_semantic_document_id = models.CharField(
        max_length=255, blank=True, null=True, default=None
    )

    class OriginalFileForceAccessMode(models.TextChoices):
        READ_WRITE = "read_write", "read_write"
        READ_ONLY = "read_only", "read_only"

    # Optional field: if provided the access_mode will be set on all created semantic documents after processing.
    force_access_mode = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        default=None,
        choices=OriginalFileForceAccessMode.choices,
    )

    force_semantic_document_custom_attribute = models.CharField(
        max_length=255, blank=True, null=True, default=None
    )

    # If each extracted file that is uploaded should be classified as a
    # specific document category the key of the respective document category
    # can be forced here. No splitting will be applied to the extracted file
    force_document_category_key = models.CharField(
        max_length=255, blank=True, null=True, default=None
    )

    # Optional title suffix to be used
    force_title_suffix = models.CharField(
        max_length=255, blank=True, null=True, default=None
    )

    force_semantic_document_splitting_style = models.CharField(
        max_length=20,
        choices=SemanticDocumentSplittingStyle.choices,
        default=SemanticDocumentSplittingStyle.DEFAULT,
    )

    source = models.CharField(
        max_length=10,
        choices=OriginalFileSource.choices,
        blank=True,
        null=True,
        default=None,
        help_text="The source of the original file, i.e. how it was uploaded.",
    )

    create_user = models.ForeignKey(
        get_user_model(),
        related_name="create_user",
        on_delete=DO_NOTHING,
        null=True,
        default=None,
        help_text="The user who created or altered the original file. "
        "Can be a user logged in via the DMF or an API user.",
    )

    @property
    def dossier_file_uuid(self):
        return self.file.uuid

    @property
    def download_url(self):
        """URL for downloading the file, uses replacement if available"""
        if self.file_replacement:
            return self.file_replacement.fast_url
        return self.file.fast_url

    @property
    def active_file(self):
        """Returns the active file (replacement if available, otherwise original)

        This property should be used when processing or accessing the file content
        to automatically handle replacement files when they exist.
        """
        return self.file_replacement or self.file

    @property
    def file_name(self):
        return self.file.name


class ExtractedFile(Timestampable):
    dossier = models.ForeignKey(Dossier, on_delete=CASCADE)
    original_file = models.ForeignKey(OriginalFile, on_delete=CASCADE)
    path_from_original = models.CharField(max_length=32000)
    status = models.CharField(
        max_length=10,
        choices=FileStatus.choices,
        default=FileStatus.PROCESSING,
    )
    file = models.ForeignKey(DossierFile, on_delete=CASCADE)

    @property
    def original_file_uuid(self):
        return self.original_file_id

    @property
    def dossier_file_uuid(self):
        return self.file_id


class DocumentCategory(Timestampable):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["name", "account"], name="dossier_category_unique_name_account"
            ),
        ]

    id = models.CharField(max_length=255)
    # name should actually be called key and is treated as such (e.g. unqiue constraint with account)
    # Swissfex and ZKB serializers convert the name to key
    name = models.CharField(max_length=255)
    account = models.ForeignKey(Account, on_delete=CASCADE)
    de = models.CharField(max_length=255, blank=True, null=True)
    en = models.CharField(max_length=255, blank=True, null=True)
    fr = models.CharField(max_length=255, blank=True, null=True)
    it = models.CharField(max_length=255, blank=True, null=True)
    id_external = models.CharField(max_length=255, blank=True, null=True)
    de_external = models.CharField(max_length=255, blank=True, null=True)
    en_external = models.CharField(max_length=255, blank=True, null=True)
    fr_external = models.CharField(max_length=255, blank=True, null=True)
    it_external = models.CharField(max_length=255, blank=True, null=True)
    additional_search_terms_de = models.CharField(max_length=255, blank=True, null=True)
    additional_search_terms_en = models.CharField(max_length=255, blank=True, null=True)
    additional_search_terms_fr = models.CharField(max_length=255, blank=True, null=True)
    additional_search_terms_it = models.CharField(max_length=255, blank=True, null=True)
    description_de = models.CharField(max_length=255, blank=True, null=True)
    description_en = models.CharField(max_length=255, blank=True, null=True)
    description_fr = models.CharField(max_length=255, blank=True, null=True)
    description_it = models.CharField(max_length=255, blank=True, null=True)

    exclude_for_recommendation = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.id} {self.name}"

    def translated(self, lang):
        language = lang.lower()
        if language not in DEFAULT_VALID_UI_LANGUAGES:
            raise ValueError(f"unsupported language {language}")
        return getattr(self, language) or f"[{getattr(self, 'de')}]"


def available_document_categories(account: Account, add_to_qs_doc_cat=None):
    criterion1 = Q(exclude_for_recommendation=False)

    if add_to_qs_doc_cat is not None:
        criterion2 = Q(name=add_to_qs_doc_cat.name)
        return DocumentCategory.objects.filter(
            criterion1 | criterion2, account=account
        ).order_by("id")

    return DocumentCategory.objects.filter(criterion1, account=account).order_by("id")


class PageCategory(Timestampable):
    id = models.CharField(max_length=255)
    name = models.CharField(max_length=255, unique=True)

    def __str__(self):
        return f"{self.id} {self.name}"


class FileException(Timestampable, ExceptionDetails):
    extracted_file = models.OneToOneField(ExtractedFile, on_delete=CASCADE)

    class TypeError(models.TextChoices):
        PROCESSED = "Processed", _("Processed")
        EXTRACTED = "Extracted", _("Extracted")

    type = models.CharField(max_length=16, choices=TypeError.choices)


class AccessDelegation(Timestampable):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["account", "delegator", "delegate"],
                name="unique_access_delegation",
            )
        ]

    account = models.ForeignKey(Account, on_delete=CASCADE)
    delegator = models.ForeignKey(
        get_user_model(), related_name="delegator_user", on_delete=CASCADE
    )
    delegate = models.ForeignKey(
        get_user_model(), related_name="delegate_user", on_delete=CASCADE
    )
    expire_time = models.DateTimeField(blank=True, null=True)


def post_delete_callback(instance, *args, **kwargs):
    minio_client.remove_object(
        instance.bucket, f"{instance._prefix}{instance.__dict__['data']}"
    )


post_delete.connect(post_delete_callback, sender=DossierFile)


def annotate_with_calculated_access_mode_dossier(dossier_query_set: QuerySet[Dossier]):
    # Just relying on the dossier - i.e. for processes that don't rely on a user
    # E.g. dossier export

    # Base query for all dossiers
    annotated_queryset = dossier_query_set.annotate(
        calculated_access_mode=Case(
            When(
                work_status__key__in=DOSSIER_READ_ONLY_WORK_STATUS,
                then=Value(Dossier.DossierAccessMode.READ_ONLY),
            ),
            default=F("access_mode"),
        )
    )

    return annotated_queryset


def annotate_with_calculated_access_mode(
    dossier_query_set: QuerySet[Dossier], user: User
):
    # Base query for all dossiers
    annotated_queryset = annotate_with_calculated_access_mode_dossier(dossier_query_set)

    # Subquery to get the most restrictive valid DossierAccessGrant for each Dossier
    valid_grant = (
        DossierAccessGrant.objects.filter(
            dossier=OuterRef("pk"),
            expires_at__gt=timezone.now(),
            # The has_access is handled by account_specific_access_checks
            # has_access=True,
            user=user,
        )
        .order_by("scope")
        .values("scope")[:1]
    )

    # Annotate with grant_scope and update calculated_access_mode
    annotated_queryset = annotated_queryset.annotate(
        grant_scope=Subquery(valid_grant)
    ).annotate(
        calculated_access_mode=Case(
            When(
                Q(calculated_access_mode=Dossier.DossierAccessMode.READ_ONLY)
                | Q(grant_scope=Scope.READ_ONLY),
                then=Value(Dossier.DossierAccessMode.READ_ONLY),
            ),
            default=F("access_mode"),
        )
    )

    return annotated_queryset


class RealestateProperty(Timestampable):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["dossier", "key"], name="unique_realestate_per_dossier"
            ),
        ]

    dossier = models.ForeignKey(Dossier, on_delete=CASCADE)
    key = models.CharField(max_length=255)
    title = models.CharField(max_length=255, blank=True, null=True)
    floor = models.IntegerField(blank=True, null=True)
    street = models.CharField(max_length=255, blank=True, null=True)
    street_nr = models.CharField(max_length=10, blank=True, null=True)
    zipcode = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        street = " ".join(filter(None, [self.street, self.street_nr]))
        address_parts = [street, self.zipcode, self.city]
        address = ", ".join(filter(None, address_parts))

        return f"{self.key} - {self.title or ''} | {address or ''}"


class AssignedRealestatePropertyOriginalFile(Timestampable):
    """Creates a relationship between an original file and a realestate property
    We manually create an intermediate table as we can store additional information
    by inheriting from Timestampable (i.e. updated_at, created_at)

    There are actually two AssignedRealestateProperty intermediate tables, this one and one
    for semantic documents. Hence, I've added the suffix OriginalFile to this one.

    We originally added a ManyToManyField to the OriginalFile model, and added a through i.e.

    realestate_property = models.ManyToManyField(
        "RealestateProperty", through="AssignedRealestatePropertyOriginalFile"
    )

    https://docs.djangoproject.com/en/4.2/topics/db/models/#extra-fields-on-many-to-many-relationships

    Which would have provided some syntactic sugar i.e. lookup via

    original_file.realestate_property.first()

    and creation via assignment

    original_file.realestate_property.add(real_estate_property, through_defaults={})

    However, we anticipate that we may in the future need to scale the system, and it would be easier to
    partition out the database, if we manually join via the intermediate table in the code.
    """

    originalfile = models.ForeignKey(OriginalFile, on_delete=CASCADE)
    realestate_property = models.ForeignKey(RealestateProperty, on_delete=CASCADE)


class ProcessedPageCount(Timestampable):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=[
                    "account",
                    "dossier_uuid",
                    "original_file_uuid",
                    "extracted_file_uuid",
                ],
                name="unique_processed_page_count",
            )
        ]

    account = models.ForeignKey(Account, on_delete=CASCADE)
    dossier_uuid = models.UUIDField()
    external_dossier_id = models.CharField(max_length=255, blank=True, null=True)
    original_file_uuid = models.UUIDField()
    extracted_file_uuid = models.UUIDField()
    extracted_file_path_from_original = models.CharField(max_length=32000)
    processed_pages = models.PositiveIntegerField()
    owner = models.CharField(max_length=150, blank=True, null=True)


class CopyDossierHistory(Timestampable):
    # Used to keep a record of when a new dossier was copied from an old one
    account = models.ForeignKey(Account, on_delete=CASCADE, null=True)
    source_dossier_uuid = models.UUIDField()
    source_dossier_external_id = models.CharField(max_length=255, blank=True, null=True)
    new_dossier_uuid = models.UUIDField()
    new_dossier_external_id = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(
        max_length=10,
        choices=FileStatus.choices,
        default=FileStatus.PROCESSING,
    )
    duration = models.DurationField(null=True)

    def __str__(self):
        return (
            f"{self.source_dossier_uuid} {self.source_dossier_external_id} {self.new_dossier_uuid} "
            f"{self.new_dossier_external_id} {self.status}"
        )


class Scope(models.TextChoices):
    READ_ONLY = "read_only", _("Read Only")
    READ_WRITE = "read_write", _("Read Write")


class DossierAccessGrant(Timestampable):

    user = models.ForeignKey(get_user_model(), on_delete=CASCADE)

    dossier = models.ForeignKey(Dossier, on_delete=CASCADE)

    expires_at = models.DateTimeField()

    # We want to cache both access granted and access denied
    has_access = models.BooleanField(default=False)

    scope = models.CharField(
        max_length=20, choices=Scope.choices, default=Scope.READ_WRITE.value
    )

    class Meta:
        # Composite index to speed up
        # DossierAccessGrant.objects.filter(user=user, dossier=dossier)

        constraints = [
            models.UniqueConstraint(
                fields=["user", "dossier"],
                name="unique_user_dossier_access_grant",
            ),
        ]

    def clean(self):
        super().clean()
        user_accounts = DossierUser.objects.filter(
            user=self.user, account=self.dossier.account
        ).first()
        if user_accounts is None:
            raise ValidationError(
                _("The dossier's account must match the user's account.")
            )

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
