import structlog
from typing import Optional
from uuid import UUID

import pika
from django.conf import settings
from pydantic import BaseModel

from core.publisher import publish
from dossier.helpers import add_processing_config_context
from dossier.models import OriginalFile
from dossier.processing_config import OriginalFileProcessingConfig

logger = structlog.get_logger()


class ProcessOriginalFileRequestV1(BaseModel):
    dossier_uuid: UUID
    account_name: str
    original_file_uuid: UUID
    filename: str
    file_url: str
    lang: str
    processing_config: Optional[OriginalFileProcessingConfig] = None


def process_original_file(
    original_file: OriginalFile,
    processing_config: Optional[OriginalFileProcessingConfig] = None,
    use_replacement: bool = True,
):
    """
    Process an original file, optionally using its replacement file if available.

    This function handles the processing of files in the dossier system, with support for file replacement.
    When a file has processing errors that cannot be fixed by reprocessing the same file, a replacement
    file can be uploaded and used instead while maintaining all original file references.

    Args:
        original_file (OriginalFile): The original file to process
        processing_config (Optional[OriginalFileProcessingConfig]): Configuration for processing
        use_replacement (bool): Whether to use the replacement file if available. Defaults to True.
                              If True and a replacement file exists, it will be used for processing
                              instead of the original file. If False or no replacement exists,
                              the original file will be used.

    The function:
    1. Sets up virus scanning based on account settings
    2. Determines which file to process (replacement or original)
    3. Creates and publishes a processing request to the async worker

    Note:
        Even when using a replacement file, the original file's references are maintained:
        - The original_file_uuid in the request remains the same
        - All extracted files, processed pages, etc. will link back to the original file
        - The file history and audit trail are preserved
    """
    enable_virus_scan = original_file.dossier.account.enable_virus_scan
    max_num_pages_allowed_input = (
        original_file.dossier.account.max_num_pages_allowed_input
    )
    if processing_config:
        processing_config.enable_virus_scan = enable_virus_scan
        processing_config.max_num_pages_allowed_input = max_num_pages_allowed_input
    else:
        processing_config = OriginalFileProcessingConfig(
            enable_virus_scan=enable_virus_scan,
            max_num_pages_allowed_input=max_num_pages_allowed_input,
        )

    logger.info(
        "send task ProcessOriginalFileRequestV1", processing_config=processing_config
    )

    if use_replacement and original_file.file_replacement:
        # Use the replacement file if available, download_url falls back to .fast_url
        file_url = original_file.download_url
        filename = original_file.file_replacement.name
    else:
        file_url = original_file.file.fast_url
        filename = original_file.file.name

    pc = add_processing_config_context(processing_config, original_file)
    request = ProcessOriginalFileRequestV1(
        dossier_uuid=original_file.dossier.uuid,
        account_name=original_file.dossier.account.name,
        original_file_uuid=original_file.uuid,
        filename=filename,
        file_url=file_url,
        lang=original_file.dossier.lang.lower(),
        processing_config=pc,
    )
    publish(
        request.model_dump_json().encode(),
        settings.ASYNC_ORIGINAL_FILE_PROCESSOR_WORKER_V1_ROUTING_KEY,
        properties=pika.BasicProperties(
            reply_to=settings.ASYNC_DOSSIER_EVENTS_WORKER_V1_QUEUE_NAME
        ),
    )
