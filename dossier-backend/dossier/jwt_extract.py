from dossier.models import Account
from projectconfig.roles import INTERNAL_ROLE, DOSSIER_MANAGER_ROL<PERSON>


def has_user_role(decoded_jwt: dict, role: str) -> bool:
    return role in decoded_jwt.get("user_roles", [])


def is_manager(decoded_jwt: dict) -> bool:
    return has_user_role(decoded_jwt, DOSSIER_MANAGER_ROLE)


def is_internal(decoded_jwt: dict) -> bool:
    return has_user_role(decoded_jwt, INTERNAL_ROLE)


def get_external_dossier_id(decoded_jwt: dict) -> str | None:
    return decoded_jwt.get("external_dossier_id")


def get_account_key(decoded_jwt: dict) -> str | None:
    return decoded_jwt.get("account_key")


def get_account(decoded_jwt: dict) -> Account | None:
    account_key = get_account_key(decoded_jwt)
    if account_key is None:
        return None
    return Account.objects.get(key=account_key)
