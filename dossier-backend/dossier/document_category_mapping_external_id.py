import csv
from collections import OrderedDict
from pathlib import Path

from django.db.models import QuerySet

from dossier.models import Document<PERSON><PERSON><PERSON><PERSON>


def read_csv_to_ordered_dict(file_path):
    """
    Reads a UTF-8 encoded CSV file and returns an OrderedDict with keys from the first column
    and values from the second column. The first line is assumed to be a header.

    Input format looks like this (left is HD document_category key, right is custom id of client):

    document_category_key,id_external
    RESIDENCE_PERMIT_B,231Bexternal
    RESIDENCE_PERMIT_C,231Cexternal
    RESIDENCE_PERMIT_G,231Gexternal
    PASSPORT_CH,PASSPORT

    :param file_path: Path to the input CSV file.
    :return: OrderedDict where keys are from the first column and values from the second.
    """
    ordered_dict = OrderedDict()

    # Open the CSV file with UTF-8 encoding
    with open(file_path, encoding="utf-8") as file:
        reader = csv.DictReader(file)

        # Iterate through the rows and populate the OrderedDict
        for row in reader:
            ordered_dict[row["document_category_key"]] = row["id_external"]

    return ordered_dict


def add_external_id_to_document_categories(
    account_key: str, document_categories_mapping_csv_path: Path
):
    """
    Loop over all document categories and override the field "id_external" based on the mapping
    in a static csv file. It is expected that all document categories of the catalog (standard and custom bcge
    categories must be present in the mapping file).
    @param document_categories_mapping_csv_path:
    @param account_key:
    @return:
    """
    mapping = read_csv_to_ordered_dict(document_categories_mapping_csv_path)

    updates = []
    not_found = []
    doc_cats: QuerySet[DocumentCategory] = DocumentCategory.objects.filter(
        account__key=account_key
    )

    for dc in doc_cats:
        if dc.name in mapping:
            dc.id_external = mapping[dc.name]
            dc.save()
            updates.append(dc.name)
        else:
            not_found.append(dc.name)
    success = len(not_found) == 0 and len(updates) > 0
    return success, updates, not_found
