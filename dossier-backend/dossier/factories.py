import random
from typing import Optional
from datetime import datetime
from enum import Enum
from itertools import cycle

import factory
from PIL import Image, ImageDraw, ImageFont
from django.contrib.auth import get_user_model
from django.core.files.base import ContentFile
from django.utils import timezone
from factory.django import DjangoModelFactory
from faker import Faker
from faker.providers import address, date_time, internet, lorem, person

from assets import ASSETS_PATH
from dossier.helpers_timezone import create_faker_past_datetime_with_timezone
from dossier.models import (
    DossierRole,
    DocumentCategory,
    DossierUser,
    Dossier,
    OriginalFile,
    DossierFile,
    FileStatus,
    Account,
    ExtractedFile,
    PageCategory,
    JWK,
)
from dossier.services import image_to_content_file
from processed_file.models import (
    ProcessedFile,
    ProcessedPage,
    PageObject,
    PageObjectTitle,
    PageObjectType,
)
from semantic_document.models import (
    SemanticDocument,
    SemanticPage,
    SemanticPagePageObject,
)
from semantic_document.services import map_confidence
import dossier.schemas as dossier_schemas
from dossier.services_external import create_or_update_dossier_from_external_id
from dossier.doc_cat_helpers import (
    load_document_categories_from_path,
)
from projectconfig.authentication import get_user_or_create

fake = Faker()


def set_new_account_defaults(account: Account) -> Account:
    """
        Set standard settings for new accounts that do not correspond to the conservative
        default settings of the account. Use this for every new account being implemented.

        Account is not saved, needs to be done by user of this method if needed.
    @param account: Account to be modified
    @return:
    """
    account.is_bekb = False
    account.photo_album_docx_template = ""
    account.enable_documents_delta_view = True
    account.enable_semantic_page_image_lazy_loading = True
    account.enable_rendering_plans_tab = True
    account.enable_dossier_assignment_to_someone_else = True
    account.enable_download_extraction_excel = False
    account.enable_rendering_structure_details_tab = True
    account.enable_rendering_structure_tab = False
    return account


def create_or_update_default_assignee_role(account: Account):
    DossierRole.objects.get_or_create(
        defaults=dict(
            name_de="Zuständiger",
            name_fr="Responsable",
            name_en="Assignee",
            name_it="Assegnatario",
        ),
        key="ASSIGNEE",
        user_selectable=True,
        show_separate_filter=True,
        account=account,
    )


class DefaultAccountFactoryFaker:
    def __init__(
        self,
        account: Optional[Account] = None,
        locale: Optional[str] = None,
    ):
        self.document_categories = None
        self.account_key = account.key
        self.account = account

        create_or_update_default_assignee_role(account)

        faker = Faker(locale=locale)
        faker.add_provider(person)
        faker.add_provider(address)
        faker.add_provider(internet)
        faker.add_provider(date_time)
        faker.add_provider(lorem)
        self.faker = faker

        # This user is also setup in keycloak
        username = f"{self.account_key}<EMAIL>"
        user = get_user_or_create(
            account=self.account,
            username=username,
            email=username,
            fname=f"JC {self.account_key}",
            lname="Denton",
        )

        user.is_staff = False
        user.save()

    def load_initial_document_categories(self):
        document_categories, _, _, _ = load_document_categories_from_path(self.account)

        # Only needed if we will have custom <client> categories in the future
        #     document_categories_json_path = (
        #         ASSETS_PATH / "document_category/<client>/DocumentCategory-2024-xx-xx.json"
        #     )
        #     document_categories_custom, _, _, _ = load_initial_document_categories(
        #         self.account, document_categories_json_path
        #     )
        #     document_categories += document_categories_custom
        #     document_categories.sort(key=lambda x: x.id)
        self.document_categories = document_categories
        return document_categories

    def create_dossier(
        self,
        current_user: DossierUser = None,
    ) -> Dossier:
        if current_user is None:
            current_user = get_user_or_create(
                account=self.account,
                username=f"{self.account_key}<EMAIL>",
                email=f"{self.account_key}<EMAIL>",
                fname="JC",
                lname="Denton",
            )

        dossier = create_or_update_dossier_from_external_id(
            self.account,
            external_dossier_id="test",
            dossier_name=dossier_schemas.DossierName(self.faker.sentence()),
            user=current_user,
            language=random.choice(list(dossier_schemas.Language)),
        )

        dossier.created_at = create_faker_past_datetime_with_timezone(self.faker)
        dossier.save()

        return dossier


class AccountFactory(DjangoModelFactory):
    class Meta:
        model = Account
        django_get_or_create = ("key",)

    key = "default"
    name = factory.SelfAttribute("key")
    default_bucket_name = "dms-default-bucket"


class JWKFactory(DjangoModelFactory):
    class Meta:
        model = JWK

    account = factory.SubFactory(AccountFactory)
    jwk = (
        {
            "use": "sig",
            "kty": "RSA",
            "kid": "yUczJ4mWNA15YZWGafeMoUbzc3HWPboOZm3qohGFVQg",
            "alg": "RS256",
            "n": "kCmvvqDTcjFI-Xphm_cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv-ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27-CC1p_lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2-VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj-XqcZXvKAo_v1U2A9c5CztadZSa7TL8TZa_TOByxbYCaElxWKs-TWVmnj-TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITw",
            "e": "AQAB",
        },
    )
    enabled = True


class DossierFactory(DjangoModelFactory):
    class Meta:
        model = Dossier

    uuid = factory.Faker("uuid4")
    account = factory.SubFactory(AccountFactory)
    name = factory.Faker("sentence")
    lang = "de"

    external_id = factory.LazyFunction(lambda: fake.bothify("ext-????-###-???"))
    work_status = None
    businesscase_type = None

    @factory.lazy_attribute
    def expiry_date(self):
        return timezone.make_aware(
            fake.future_datetime(end_date=datetime(2028, 1, 1, 1, 38, 2, 594731)),
            timezone.get_current_timezone(),
        )

    @factory.lazy_attribute
    def owner(self):
        dossier_user = DossierUserFactory(account=self.account)
        return dossier_user.user

    @factory.lazy_attribute
    def note(self):
        return fake.text() if fake.boolean(chance_of_getting_true=10) else None

    created_at = timezone.now()


class DossierFileFactory(DjangoModelFactory):
    class Meta:
        model = DossierFile

    dossier = factory.SubFactory(DossierFactory)
    data = factory.django.FileField(
        filename=fake.file_name(), data=fake.binary(length=10)
    )

    @factory.lazy_attribute
    def bucket(self):
        return self.dossier.account.default_bucket_name


class FakePageDossierFile(DossierFileFactory):
    class Params:
        page_text = "Fake Page Image"
        filename = "image.jpg"
        format = "JPEG"
        semantic_document_title = None
        additional_text = None
        color = None

    @factory.lazy_attribute
    def data(self):
        image = create_fake_page(
            self.page_text,
            semantic_document_title=self.semantic_document_title,
            additional_text=self.additional_text,
            color=self.color,
        )
        filename = self.filename
        return image_to_content_file(image, filename, format=self.format)


class FakeImageDossierFileFactory(DossierFileFactory):
    class Param:
        page_test = "Fake Page Image"
        filename = "image.jpg"
        semantic_document_title = None
        additional_text = None
        color = None

    @factory.lazy_attribute
    def data(self):
        image = create_fake_page(
            page_text=self.page_test,
            semantic_document_title=self.semantic_document_title,
            additional_text=self.additional_text,
            color=self.color,
        )
        filename = self.filename
        return image_to_content_file(image, filename)


class OriginalFileFactory(DjangoModelFactory):
    class Meta:
        model = OriginalFile

    dossier = factory.SubFactory(DossierFactory)

    @factory.lazy_attribute
    def file(self):
        return DossierFileFactory(dossier=self.dossier, data=self.content_file)

    class Params:
        content_file: ContentFile = None


class ExtractedFileFactory(DjangoModelFactory):
    class Meta:
        model = ExtractedFile

    dossier = factory.SubFactory(DossierFactory)
    original_file = factory.SubFactory(OriginalFileFactory)
    status = FileStatus.PROCESSING
    file = factory.SubFactory(DossierFileFactory)

    @factory.lazy_attribute
    def path_from_original(self):
        return self.original_file.file.data


class ProcessedFileFactory(DjangoModelFactory):
    class Meta:
        model = ProcessedFile

    dossier = factory.SubFactory(DossierFactory)
    extracted_file = factory.SubFactory(ExtractedFileFactory)


class PageCategoryFactory(DjangoModelFactory):
    class Meta:
        model = PageCategory
        django_get_or_create = ("id", "name")

    id = None
    name = None


class ProcessedPageFactory(DjangoModelFactory):
    class Meta:
        model = ProcessedPage

    class Params:
        page_number = 0
        semantic_document_title = None
        additional_text = None
        color = None

    dossier = factory.SubFactory(DossierFactory)

    @factory.lazy_attribute
    def processed_file(self):
        return ProcessedFileFactory(dossier=self.dossier)

    page_category = factory.SubFactory(
        PageCategoryFactory,
        id=1,
        name="GENERIC_PAGE",
    )

    number = factory.SelfAttribute("page_number")

    confidence_value = factory.Faker(
        "pyfloat", positive=True, min_value=0.7, max_value=1
    )

    @factory.lazy_attribute
    def document_category(self):
        return fake.random_element(
            DocumentCategory.objects.filter(account=self.dossier.account_id)
        )

    @factory.lazy_attribute
    def image(self):
        return FakePageDossierFile(
            dossier=self.dossier,
            page_text=f"Page Number: {self.number}",
            filename=f"image_{self.number}.jpg",
            format="JPEG",
            semantic_document_title=self.semantic_document_title,
            additional_text=self.additional_text,
            color=self.color,
        )

    @factory.lazy_attribute
    def searchable_pdf(self):
        return FakePageDossierFile(
            dossier=self.dossier,
            page_text=f"Page Number: {self.number}",
            filename=f"image_{self.number}.pdf",
            format="PDF",
        )

    @factory.lazy_attribute
    def searchable_txt(self):
        return DossierFileFactory(dossier=self.dossier)


class RandomUserFactory(DjangoModelFactory):
    class Meta:
        model = get_user_model()
        django_get_or_create = ("username",)

    username = factory.Faker("email")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")


class FicoRoleFactory(DjangoModelFactory):
    class Meta:
        model = DossierRole

    key = "FICO"
    name_de = "FICO"
    name_fr = "FICO"
    name_en = "FICO"
    name_it = "FICO"
    user_selectable = True
    show_separate_filter = True


class AssigneeRoleFactory(DjangoModelFactory):
    class Meta:
        model = DossierRole

    key = "ASSIGNEE"
    name_de = "Zuständiger"
    name_fr = "Responsable"
    name_en = "Assignee"
    name_it = "Assegnatario"
    user_selectable = True


class SemanticDocumentFactory(DjangoModelFactory):
    class Meta:
        model = SemanticDocument

    dossier = factory.SubFactory(DossierFactory)
    confidence_value = fake.random_int(min=80, max=100) / 100

    title_suffix = None

    @factory.lazy_attribute
    def confidence_level(self):
        return map_confidence(self.confidence_value)

    @factory.lazy_attribute
    def confidence_formatted(self):
        return f"{int(self.confidence_value * 100)}%"

    @factory.lazy_attribute
    def document_category(self):
        return random.choice(
            DocumentCategory.objects.filter(account=self.dossier.account).all(),
        )

    title_suffix = None


class SemanticPageFactory(DjangoModelFactory):
    class Meta:
        model = SemanticPage

    class Params:
        page_number = 0
        additional_text = None
        color = None

    dossier = factory.SubFactory(DossierFactory)

    semantic_document = factory.SubFactory(SemanticDocumentFactory)
    document_category = factory.SelfAttribute("semantic_document.document_category")

    @factory.lazy_attribute
    def processed_page(self):
        return ProcessedPageFactory(
            dossier=self.dossier,
            page_number=self.page_number,
            semantic_document_title=self.semantic_document.title,
            additional_text=self.additional_text,
            color=self.color,
        )

    number = factory.SelfAttribute("page_number")

    confidence_value = fake.random_int(min=80, max=100) / 100

    @factory.lazy_attribute
    def confidence_level(self):
        return map_confidence(self.confidence_value)

    @factory.lazy_attribute
    def confidence_formatted(self):
        return f"{int(self.confidence_value * 100)}%"

    @factory.lazy_attribute
    def lang(self):
        return self.semantic_document.dossier.lang

    @factory.lazy_attribute
    def page_category(self):
        return self.processed_page.page_category


class DossierUserFactory(DjangoModelFactory):
    class Meta:
        model = DossierUser
        django_get_or_create = ("user", "account")

    user = factory.SubFactory(RandomUserFactory)
    account = factory.SubFactory(AccountFactory)


class PageObjectTitleFactory(DjangoModelFactory):
    class Meta:
        model = PageObjectTitle

    key = factory.Sequence(lambda n: f"PageObjectTitle_{n}")


class PageObjectTypeFactory(DjangoModelFactory):
    class Meta:
        model = PageObjectType

    name = factory.Sequence(lambda n: f"PageObjectType_{n}")


class PageObjectFactory(DjangoModelFactory):
    class Meta:
        model = PageObject

    processed_page = factory.SubFactory(ProcessedPageFactory)
    confidence_value = fake.random_int(min=80, max=100) / 100
    confidence_formatted = f"{int(confidence_value * 100)}%"
    ref_height = 1000
    ref_width = 1000
    top = factory.Faker("random_int", min=0, max=500)
    left = factory.Faker("random_int", min=0, max=500)
    right = factory.Faker("random_int", min=500, max=1000)
    bottom = factory.Faker("random_int", min=500, max=1000)
    key = factory.SubFactory(PageObjectTitleFactory)
    type = factory.SubFactory(PageObjectTypeFactory)
    value = factory.Faker("word")


class SemanticPagePageObjectFactory(DjangoModelFactory):
    class Meta:
        model = SemanticPagePageObject

    page_object = factory.SubFactory(PageObjectFactory)
    semantic_page = factory.SubFactory(SemanticPageFactory)


def create_image(size, bgColor, message, font, fontColor):
    W, H = size
    image = Image.new("RGB", size, bgColor)
    draw = ImageDraw.Draw(image)
    _, _, w, h = draw.textbbox((0, 0), message, font=font)
    draw.text(((W - w) / 2, (H - h) / 2), message, font=font, fill=fontColor)
    return image


def draw_text_to_image(image, position, message, font, fontColor):
    x, y = position
    draw = ImageDraw.Draw(image)
    draw.text((x, y), message, font=font, fill=fontColor)
    return image


grey_colors = [
    "#fafafa",
    "#f5f5f5",
    "#eeeeee",
    "#e0e0e0",
    "#bdbdbd",
    "#9e9e9e",
]
grey_color_cycler = cycle(grey_colors)


class LightBackgroundColors(Enum):
    LIGHT_LAVENDER = "#E6E6FA"
    MINT_CREAM = "#F5FFFA"
    HONEYDEW = "#F0FFF0"
    AZURE = "#F0FFFF"
    ALICE_BLUE = "#F0F8FF"
    SEASHELL = "#FFF5EE"
    LEMON_CHIFFON = "#FFFACD"
    BEIGE = "#F5F5DC"


light_color_cycle = cycle([color.value for color in LightBackgroundColors])


class MediumStrengthColors(Enum):
    TEAL = "#008080"
    # CORAL = "#FF7F50"
    OLIVE = "#808000"
    SLATE_BLUE = "#6A5ACD"
    GOLDENROD = "#DAA520"
    CADET_BLUE = "#5F9EA0"
    ORCHID = "#DA70D6"
    TOMATO = "#FF6347"


medium_color_cycle = cycle([color.value for color in MediumStrengthColors])


class StrongBackgroundColors(Enum):
    VIVID_BLUE = "#0000FF"
    ELECTRIC_GREEN = "#00FF00"
    DEEP_PURPLE = "#800080"
    VIVID_ORANGE = "#FFA500"
    HOT_PINK = "#FF69B4"
    NEON_YELLOW = "#FFFF33"
    TURQUOISE = "#40E0D0"


strong_color_cycle = cycle([color.value for color in StrongBackgroundColors])


def create_fake_page(
    page_text: str,
    semantic_document_title=None,
    additional_text: str = None,
    color: str = None,
):
    if color is None:
        color = next(grey_color_cycler)
    myFont = ImageFont.truetype(ASSETS_PATH / "Roboto-Regular.ttf", 16)
    myImage = create_image((210, 297), color, page_text, myFont, "black")
    if semantic_document_title:
        draw_text_to_image(
            myImage,
            (10, 50),
            semantic_document_title,
            ImageFont.truetype(ASSETS_PATH / "Roboto-Regular.ttf", 12),
            "black",
        )
    if additional_text:
        draw_text_to_image(
            myImage,
            (50, 250),
            additional_text,
            ImageFont.truetype(ASSETS_PATH / "Roboto-Regular.ttf", 25),
            "red",
        )
    return myImage
