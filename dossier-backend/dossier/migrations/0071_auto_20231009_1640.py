# Generated by Django 3.2.22 on 2023-10-09 14:40

import django.contrib.postgres.fields
from django.db import migrations, models
import dossier.models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0070_account_enable_rendering_plans_tab'),
    ]

    operations = [
        migrations.AlterField(
            model_name='account',
            name='photo_album_docx_template',
            field=models.CharField(blank=True, default='photo-album-docx-template-default-v01.docx', help_text="If empty, no Word-Template based photo album download is available. Else set name of deployed template. Default is 'photo-album-docx-template-hbl-v01.docx'.", max_length=255),
        ),
        migrations.AlterField(
            model_name='account',
            name='valid_dossier_languages',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.TextField(help_text="Comma separated list of languages that are available to name the documents in the dossier. If only one language is given, the field is invisible. Default: 'De,En,Fr,It'", max_length=255), default=dossier.models.get_valid_dossier_languages_default, size=None),
        ),
        migrations.AlterField(
            model_name='account',
            name='valid_ui_languages',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.TextField(help_text="Comma separated list of user interface languages for the application. If only one language is given, the language selection is invisible and instructions, video are only shown for this language (else all languages). Default: 'de,en,fr,it'", max_length=255), default=dossier.models.get_valid_ui_languages_default, size=None),
        ),
    ]
