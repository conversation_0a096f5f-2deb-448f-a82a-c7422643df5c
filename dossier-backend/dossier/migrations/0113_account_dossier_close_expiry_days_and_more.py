# Generated by Django 4.2.16 on 2024-11-22 15:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0112_account_enable_semantic_document_export_unknown_documents"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="dossier_close_expiry_days",
            field=models.FloatField(
                default=7.0,
                help_text="Expiry after dossier close in days. E.g. 1.5 means 36 hours into the future.",
                verbose_name="Dossier Expiry after 'dossier close' event in days (float)",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="dossier_close_strategy",
            field=models.CharField(
                choices=[
                    ("DEFAULT", "DEFAULT: Basic strategy, no requirements"),
                    (
                        "REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE",
                        "REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE",
                    ),
                    (
                        "EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE",
                        "EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE",
                    ),
                ],
                default="DEFAULT",
                help_text="Defines which requirements must be met before a dossier can be closed.",
                max_length=50,
                verbose_name="Semantic Document Export Strategy",
            ),
        ),
    ]
