# Generated by Django 4.2.20 on 2025-03-20 10:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0117_alter_dossieraccessgrant_scope"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="enable_semantic_document_annotations",
            field=models.BooleanField(
                default=True,
                help_text="Whether the user has the option to add highlights and comments to the semantic documents in the detail view, and whether these annotations get exported with the document.",
            ),
        ),
    ]
