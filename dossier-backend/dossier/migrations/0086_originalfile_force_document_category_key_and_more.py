# Generated by Django 4.2.9 on 2024-01-29 17:07

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("dossier", "0085_alter_account_enable_button_download"),
    ]

    operations = [
        migrations.AddField(
            model_name="originalfile",
            name="force_document_category_key",
            field=models.CharField(blank=True, default=None, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="originalfile",
            name="force_semantic_document_splitting_style",
            field=models.Char<PERSON>ield(
                choices=[("Default", "Default"), ("NoSplitting", "No Splitting")],
                default="Default",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="originalfile",
            name="force_title_suffix",
            field=models.CharField(blank=True, default=None, max_length=255, null=True),
        ),
    ]
