# Generated by Django 4.2.16 on 2024-11-18 13:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0109_account_enable_pdfa_conversion_for_export"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="enable_custom_semantic_document_date",
            field=models.BooleanField(
                default=False,
                help_text="If True the user can set the date of a semantic document (e.g in the document rename modal). If it hasn't been set, a dynamically computed value is used instead.",
            ),
        ),
    ]
