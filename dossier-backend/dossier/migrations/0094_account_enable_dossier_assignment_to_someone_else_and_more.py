# Generated by Django 4.2.11 on 2024-04-10 06:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0093_account_enable_doccheck_in_statemgmt"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="enable_dossier_assignment_to_someone_else",
            field=models.BooleanField(
                default=False,
                help_text="If True show the user can reassign the 'ASSIGNEE' in the dossier properties dialog - even to another person. Else the 'assign' dropdown is not shown.",
            ),
        ),
        migrations.AlterField(
            model_name="account",
            name="enable_dossier_assignment",
            field=models.BooleanField(
                default=True,
                help_text="If True show the user can reassign the 'ASSIGNEE' in the dossier properties dialog. Else the 'assign to myself' option is not shown.",
            ),
        ),
    ]
