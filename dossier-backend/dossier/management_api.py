import json
from http import HTTPStatus
from typing import List
from typing import Optional
from uuid import UUID

import jwt
import structlog
from django.conf import settings
from django.db import IntegrityError, transaction
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.utils import timezone
from jwt import PyJWK
from ninja import Router
from ninja.errors import HttpError
from ninja.security import HttpBearer

from core.schema import Error, Message
from doccheck.models import DocCheck
from dossier.models import (
    Account,
    DossierAccessCheckProvider,
    JWK,
    Dossier,
)
from dossier.schemas import (
    AccountResponseSchema,
    AccountChangeSchema,
    AccountCreateSchema,
    JWKSchema,
    JWKSetSchema,
)
from dossier.services import (
    dossier_hard_delete,
    create_new_account,
)
from e2e.account_types import account_types
from projectconfig.authentication import has_role
from projectconfig.jwk import extract_public_jwk
from statemgmt.models import StateMachine

logger = structlog.get_logger()


class ManagementAuth(HttpBearer):
    # Simplified Auth that only checks the JWT is valid against settings.MANAGEMENT_PUBLIC_KEY
    # and that token contains "mgmt-api" role

    def authenticate(self, request, token, *args, **kw) -> Optional[dict]:
        try:
            jwk_dict = json.loads(settings.INSTANCE_SIGNING_KEY)
            public_key = PyJWK(extract_public_jwk(jwk_dict))
            decoded_payload = jwt.decode(
                jwt=token,
                key=public_key,
                audience="account",
                algorithms=["RS256"],
                options={"verify_signature": True, "exp": True},
            )

            if decoded_payload and has_role(
                role="mgmt-api", decoded_payload=decoded_payload
            ):
                return decoded_payload
            else:
                logger.exception("Token does not have role mgmt-api")
                return None

        except Exception:
            logger.exception("Could not authenticate user", exc_info=True)
            return None


management_router = Router(auth=ManagementAuth())


@management_router.get(
    "/accounts", response=List[AccountResponseSchema], url_name="accounts"
)
def list_accounts(request):
    qs = Account.objects.all()
    return qs


@management_router.get(
    "/accounts/{account_uuid}", response=AccountResponseSchema, url_name="accounts"
)
def retrieve_account(request, account_uuid: str):
    # Get account via UUID or KEY
    account = Account.objects.filter(Q(key=account_uuid) | Q(uuid=account_uuid)).first()
    if not account:
        return HttpError(status_code=404, message="Account not found")
    return account


@management_router.post(
    "/accounts", response={201: AccountResponseSchema, 409: Error}, url_name="accounts"
)
def create_account(request, payload: AccountCreateSchema):
    account = Account.objects.filter(key=payload.key).first()
    if account:
        return 409, {
            "code": 409,
            "message": f"Account with key {account.key} already exists",
        }

    account = create_new_account(payload)
    account.save()

    return 201, account


@management_router.put(
    "/account/setup/{key}/{type}",
    response={201: AccountResponseSchema, 401: Message},
    url_name="accounts",
)
def setup_account(request, key: str, type: str):
    start = timezone.now()
    try:
        account = Account.objects.filter(key=key).first()
        if account:
            logger.info("Account with key already exists, deleting it", key=key)
            account.delete()

        account_type_creator = account_types.get(type)
        if account_type_creator is None:
            return 401, f"Account type {type} not found"
        account = account_type_creator(key)
        return 201, account
    finally:
        logger.info("Setup Account duration", duration=timezone.now() - start)


@management_router.put(
    "/accounts/{account_uuid}",
    response={200: AccountResponseSchema, 409: Error},
    url_name="accounts",
)
def update_account(request, account_uuid: UUID, payload: AccountChangeSchema):
    account = get_object_or_404(Account, uuid=account_uuid)
    data = payload.model_dump(exclude_unset=True, exclude_none=True)
    active_work_status_state_machine = data.pop(
        "active_work_status_state_machine", None
    )
    active_doc_check = data.pop("active_doc_check", None)
    dossier_access_check_provider = data.pop("dossier_access_check_provider", None)

    if active_work_status_state_machine:
        account.active_work_status_state_machine = get_object_or_404(
            StateMachine, uuid=active_work_status_state_machine
        )

    if active_doc_check:
        account.active_doc_check = get_object_or_404(DocCheck, uuid=active_doc_check)

    if dossier_access_check_provider:
        account.dossier_access_check_provider = get_object_or_404(
            DossierAccessCheckProvider, uuid=dossier_access_check_provider
        )

    for attr, value in data.items():
        setattr(account, attr, value)

    try:
        account.save()

    except IntegrityError as e:
        raise HttpError(
            HTTPStatus.CONFLICT,
            f"Conflict {e.args}",
        )

    return account


@management_router.delete("/accounts/{account_uuid}", url_name="accounts")
@transaction.atomic
def delete_account(request, account_uuid: UUID):
    account = get_object_or_404(Account, uuid=account_uuid)
    dossiers = Dossier.objects.filter(account=account)
    for dossier in dossiers:
        # we delete the dossiers using the hard delete service function so that it also cleans up/deletes
        # associated DossierFiles in the miniobucket
        dossier_hard_delete(dossier_uuid=dossier.uuid)
    account.delete()
    return {"success": True}


@management_router.get(
    "/accounts/{account_uuid}/jwk/",
    response={200: List[JWKSchema], 409: Error},
    url_name="accounts-jwk",
)
def get_jwks_from_account(request, account_uuid: UUID):
    return JWK.objects.filter(account__uuid=account_uuid).all()


@management_router.get(
    "/jwk/", response={200: List[JWKSchema], 409: Error}, url_name="jwks"
)
def get_jwks(request):
    return JWK.objects.all()


@management_router.get(
    "/jwk/{jwk_uuid}", response={200: JWKSchema, 409: Error}, url_name="jwk"
)
def get_jwk(request, jwk_uuid: UUID):
    return get_object_or_404(JWK, uuid=jwk_uuid)


@management_router.post("/jwk/", response={201: JWKSchema, 409: Error}, url_name="jwk")
def add_jwk(request, payload: JWKSetSchema):
    account = get_object_or_404(Account, uuid=payload.account.uuid)
    if not account:
        return HttpError(status_code=404, message="Account not found")

    existing_jwk = JWK.objects.filter(
        jwk__n=payload.jwk.n, account__uuid=payload.account.uuid
    ).first()
    if existing_jwk:
        return 409, {
            "code": 409,
            "message": f"JWK with public key already exists with UUID {existing_jwk}",
        }
    return 201, JWK.objects.create(**{**payload.model_dump(), "account": account})


@management_router.delete("/jwk/{jwk_uuid}", url_name="jwk")
def delete_jwk(request, jwk_uuid: UUID):
    jwk = get_object_or_404(JWK, uuid=jwk_uuid)
    jwk.delete()
    return {"success": True}
