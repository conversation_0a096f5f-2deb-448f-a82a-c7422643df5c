from pathlib import Path

import djclick as click

from dossier.services import (
    create_businesscase_type_export,
    create_businesscase_type_import,
)


@click.group()
def cli():
    pass


@cli.command()
@click.argument("businesscase_type_json_path")
@click.argument("account_key")
def export_businesscase_type(businesscase_type_json_path: str, account_key: str):
    """
    Example:
    python manage.py export_import_businesscase_type export-businesscase-type bekb/data/businesscase_types/mortgage/bekb/bekb_businesscase_types_NEW.json bekbe
    @param businesscase_type_json_path:
    @param account_key:
    @return:
    """
    create_businesscase_type_export(account_key, Path(businesscase_type_json_path))


@cli.command()
@click.argument("businesscase_type_json_path")
@click.argument("account_key")
def import_businesscase_type(businesscase_type_json_path: str, account_key: str = None):
    """
    Example:

    python manage.py export_import_businesscase_type import-businesscase-type bekb/data/businesscase_types/mortgage/bekb_businesscase_types_20240326.json bekbe
    @param businesscase_type_json_path:
    @param account_key:
    @return:
    """
    p = Path(businesscase_type_json_path)
    if not p.exists():
        raise Exception(f"path does not exist: {p}. Current path: {Path().absolute()}")
    create_businesscase_type_import(p, account_key)
