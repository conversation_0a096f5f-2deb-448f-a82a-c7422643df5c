import os

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db import IntegrityError


class Command(BaseCommand):
    help = "Create mockup user"

    def handle(self, *args, **kwargs):
        UserModel = get_user_model()
        str(os.environ.get("DJANGO_TEST_USERNAME", default="admin"))
        str(os.environ.get("DJANGO_TEST_USERNAME", default="123"))
        try:
            user = UserModel.objects.create_user("admin", password="123")
        except IntegrityError:
            print("User already exist")
            return
        user.is_superuser = False
        user.is_staff = False
        user.save()
        print("User created")
