import pytest
from django.utils import timezone

from dossier.conftest import dossier_file_mock
from dossier.management.commands.dossier_event_consumer_v2 import (
    FileExtractedV1,
    add_extracted_file,
)

from datetime import datetime, timedelta
from pathlib import Path
from uuid import uuid4


from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.files.base import ContentFile
from faker import Faker

from dossier.models import (
    Account,
    DocumentCategory,
    Dossier,
    DossierFile,
    OriginalFile,
    FileStatus,
    ExtractedFile,
)

from django.conf import settings


pytestmark = pytest.mark.django_db(transaction=True)


def gen_processed_files(
    service_account: Account, expires: datetime, status: FileStatus
):
    faker = Faker(locale="de_CH")

    User: AbstractUser = get_user_model()

    user, _ = User.objects.get_or_create(email=faker.email())

    dossier_uuid = uuid4()
    dossier = Dossier.objects.create(
        uuid=dossier_uuid, name="Testdossier", account=service_account, owner=user
    )

    data_directory = Path(__file__).parent.parent.parent.parent / "data"
    pdf_file = data_directory / "lawssimplicity.pdf"
    data_directory / "lawsimplicity.txt"
    data_directory / "lawssimplicity.jpg"
    # generic_page_cat = PageCategory.objects.get(id=1)
    (
        DocumentCategory.objects.filter(account=dossier.account)
        .exclude(exclude_for_recommendation=True)
        .all()
    )

    data = pdf_file.read_bytes()

    searchable_pdf_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=data, name="lawsimplicity.pdf"),
        bucket=dossier.bucket,
        created_at=expires,
        updated_at=expires,
    )
    # Need for forcibly override the created_at and updated_at
    searchable_pdf_file.created_at = expires
    searchable_pdf_file.updated_at = expires
    searchable_pdf_file.save()

    original_file = OriginalFile.objects.create(
        dossier=dossier,
        file=searchable_pdf_file,
        status=status,
        created_at=expires,
        updated_at=expires,
    )

    original_file.created_at = expires
    original_file.updated_at = expires
    original_file.save()

    return dossier, original_file


def test_file_extracted_event_processing(
    monkeypatch, service_account, load_document_categories
):
    # Setup test data
    (
        dossier,
        original_file,
    ) = gen_processed_files(
        service_account,
        timezone.now()
        - timedelta(minutes=settings.FILE_STATUS_MAX_PROCESSING_TIME + 1),
        FileStatus.PROCESSING,
    )

    # Create a FileExtractedV1 event
    extracted_file_uuid = uuid4()
    file_extracted_event = FileExtractedV1(
        dossier_uuid=dossier.uuid,
        extracted_file_uuid=extracted_file_uuid,
        original_file_uuid=original_file.uuid,
        path_from_original="lawssimplicity.pdf",
        file_url="http://test-file-url.com",
    )

    monkeypatch.setattr(
        "dossier.management.commands.dossier_event_consumer_v2.create_dossier_file_from_url",
        dossier_file_mock,
    )

    add_extracted_file(file_extracted_event.model_dump_json())

    # Verify that the ExtractedFile was created
    extracted_file = ExtractedFile.objects.filter(uuid=extracted_file_uuid)
    extracted_file = extracted_file.first()
    assert extracted_file is not None
    assert extracted_file.dossier == dossier
    assert extracted_file.original_file == original_file
    assert extracted_file.path_from_original == "lawssimplicity.pdf"
    assert extracted_file.status == "Processing"

    assert extracted_file.file is not None
