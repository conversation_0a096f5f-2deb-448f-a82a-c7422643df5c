import copy
import datetime
import os
import unicodedata as ud
import urllib.parse
from datetime import timedelta
from io import Bytes<PERSON>
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import List, Dict, Tuple, Union
from uuid import UUID
from zipfile import ZipFile

from PyPDF2 import PdfWriter
import requests
from django.contrib.auth.models import User
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import Q
from django.utils import timezone as django_timezone

from bekb.models import CollateralAssignment
from core.helpers import open_and_validate_and_upload_data_to_minio
from dossier import schemas
from dossier.exceptions import HttpError
from dossier.models import (
    FileException,
    FileStatus,
    DossierFile,
    Dossier,
    OriginalFile,
    Account,
    DossierUser,
    DossierRole,
    UserInvolvement,
    generate_path,
    DocumentCategory,
    ExceptionDetails,
    minio_client,
)
from dossier.processing_config import OriginalFileProcessingConfig
from dossier.schemas import BCT_NOT_INITIALIZED, FileExceptionType
from processed_file.models import (
    PageObject,
    ProcessedFile,
    ConfidenceLevel,
)
from projectconfig.settings import ENABLE_ERROR_DETAIL_IN_BACKEND, format_datetime
from semantic_document.models import (
    SemanticDocument,
    SemanticPage,
    SemanticPagePageObject,
)


def add_processing_config_context(
    pc: OriginalFileProcessingConfig, original_file: OriginalFile
):
    if pc:
        pc2 = copy.deepcopy(pc)
    else:
        pc2 = OriginalFileProcessingConfig()

    account: Account = original_file.dossier.account
    pc2.context = f"account={account.key}, bucket={account.default_bucket_name}, dossier={original_file.dossier.uuid}, original_file={original_file.uuid}"
    return pc2


def get_hypodossier_exception_from_string(
    exception_name: str,
) -> Union[ExceptionDetails.HypoDossierException, None]:
    if exception_name:
        for val, key in ExceptionDetails.HypoDossierException.choices:
            unformatted_key = key.replace(" ", "_").upper()
            if exception_name == unformatted_key:
                return val
        return ExceptionDetails.HypoDossierException.UNMAPPED_EXCEPTION.value
    return ExceptionDetails.HypoDossierException.UNKNOWN_EXCEPTION.value


def get_confidence_summary(data_dict):
    """
    the following fields should be mandatory for the dict:
    confidence_value, confidence_formatted, confidence_level
    """
    return {
        "value": data_dict["confidence_value"],
        "value_formatted": data_dict["confidence_formatted"],
        "level": data_dict["confidence_level"],
    }


def calculate_max_dossier_expiry_date(dossier: Dossier) -> datetime:
    offset = dossier.account.max_dossier_expiry_duration_days
    creation = dossier.created_at

    max_expiry_date = (creation + timedelta(days=offset)).replace(microsecond=0)

    return max_expiry_date


def prepare_for_api_dossier_data(dossier, show_soft_deleted=False) -> dict:
    is_processing = dossier.original_files.filter(status=FileStatus.PROCESSING).exists()

    data = {
        "uuid": str(dossier.uuid),
        "name": dossier._name,
        "lang": dossier.lang,
        "creation": dossier.created_at,
        "updated_at": dossier.updated_at,
        "note": dossier.note,
        "count_documents": dossier.original_files.count(),
        "status": FileStatus.PROCESSING if is_processing else FileStatus.PROCESSED,
        "businesscase_type_id": dossier.businesscase_type_id,
        "access_mode": dossier.calculated_access_mode,
        "work_status_id": dossier.work_status_id,
        "doccheck_case_id": dossier.doccheck_case_id,
    }

    if show_soft_deleted:
        count_deleted_semantic_documents = SemanticDocument.objects.filter(
            dossier=dossier, deleted_at__isnull=False
        ).count()

        count_deleted_semantic_pages = SemanticPage.objects.filter(
            dossier=dossier, deleted_at__isnull=False
        ).count()

        data.update(
            {
                "count_deleted_semantic_documents": count_deleted_semantic_documents,
                "count_deleted_semantic_pages": count_deleted_semantic_pages,
                "count_original_files": OriginalFile.objects.filter(
                    dossier=dossier
                ).count(),  # dossier.count_original_files,
                "expiry_date": dossier.expiry_date,
                "max_expiry_date": calculate_max_dossier_expiry_date(dossier),
            }
        )

    return data


def prepare_exception_data_for_api(exceptions: List[Tuple[str, FileException]]):
    exceptions_data = {}
    for file_path, exception in exceptions:
        exceptions_data[file_path] = {
            "uuid": exception.uuid,
            "file_uuid": exception.extracted_file_id,
            "dossier_file_uuid": exception.extracted_file.file.uuid,
            "original_file_uuid": exception.extracted_file.original_file.uuid,
            "type": exception.type,
            "file_status": exception.extracted_file.status,
            "id": exception.exception_type,
            "path_from_original": exception.extracted_file.original_file.file.name,
            "last_update": exception.updated_at,
            "created_at": exception.created_at,
            "en": exception.en,
            "de": exception.de,
            "fr": exception.fr,
            "it": exception.it,
            "details": exception.details if ENABLE_ERROR_DETAIL_IN_BACKEND else None,
        }

    return exceptions_data


def prepare_extracted_files_data(dossier):
    original_files = (
        OriginalFile.objects.filter(dossier=dossier)
        .prefetch_related(
            "extractedfile_set",
            "extractedfile_set__file",
            "extractedfile_set__fileexception",
        )
        .select_related("file")
    )

    data = {}
    for original_file in original_files:
        exceptions = []
        for extracted_file in original_file.extractedfile_set.all():
            try:
                if (
                    extracted_file.fileexception.type
                    == FileException.TypeError.EXTRACTED
                ):
                    exceptions.append(
                        (
                            extracted_file.path_from_original,
                            extracted_file.fileexception,
                        )
                    )
            except:
                pass

        exceptions = prepare_exception_data_for_api(exceptions)

        try:
            if original_file.exception_de:
                exceptions[str(original_file.file.name)] = {
                    "uuid": original_file.uuid,
                    "file_uuid": original_file.uuid,
                    "dossier_file_uuid": original_file.file.uuid,
                    "original_file_uuid": original_file.uuid,
                    # This is the type of the exception, should be of type FileExceptionType
                    # so either "Processed" or "Extracted".
                    # Caution: the original_file.status can be Processing / Processed / Error, this is something different
                    "type": FileExceptionType.PROCESSED.value,
                    "file_status": original_file.status,
                    "id": ExceptionDetails.HypoDossierException.UNMAPPED_EXCEPTION.value,
                    "path_from_original": original_file.file.name,
                    "last_update": original_file.updated_at,
                    "created_at": original_file.created_at,
                    "en": original_file.exception_en,
                    "de": original_file.exception_de,
                    "fr": original_file.exception_fr,
                    "it": original_file.exception_it,
                    "details": (
                        original_file.details
                        if ENABLE_ERROR_DETAIL_IN_BACKEND
                        else None
                    ),
                }
        except:
            pass

        data[str(original_file.uuid)] = {
            "original_file": original_file.file.name,
            "extracted_files": [
                extracted_file.path_from_original
                for extracted_file in original_file.extractedfile_set.all()
            ],
            "exceptions": exceptions,
            "status": original_file.status,
            "last_update": original_file.updated_at,
            "created_at": original_file.created_at,
            "original_file_uuid": original_file.uuid,
            "dossier_file_uuid": original_file.file.uuid,
            "original_file_name": original_file.file.name,
        }

    return data


def prepare_processing_exceptions(dossier: Dossier):
    exceptions = [
        (exception.extracted_file.path_from_original, exception)
        for exception in FileException.objects.filter(
            type="Processed", dossier=dossier
        ).prefetch_related("extracted_file")
    ]

    return prepare_exception_data_for_api(exceptions)


def prepare_page_object(page_object: PageObject):
    # Returns JSON format compatible with PageObjectFullApiData
    return {
        "uuid": str(page_object.uuid),
        "key": page_object.key.key,
        "visible": page_object.visible,
        "title": page_object.key.de,
        "titles": {
            "en": page_object.key.en,
            "de": page_object.key.de,
            "fr": page_object.key.fr,
            "it": page_object.key.it,
        },
        "value": page_object.value,
        "type": page_object.type.name,
        "bbox": {
            "ref_width": page_object.ref_width,
            "ref_height": page_object.ref_height,
            "top": page_object.top,
            "left": page_object.left,
            "right": page_object.right,
            "bottom": page_object.bottom,
        },
        # We should be using the page number of the semantic page, rather than the processed page, as the
        # semantic page can be re-ordered via the webui and so the page number can change
        "page_number": page_object.processed_page.number,
        "confidence": page_object.confidence_value,
        "confidence_summary": {
            "value": page_object.confidence_value,
            "value_formatted": page_object.confidence_formatted,
            "level": page_object.confidence_level,
        },
    }


def prepare_page_objects(page_objects: List[PageObject]):
    return [prepare_page_object(page_object) for page_object in page_objects]


def prepare_page_objects_for_api_with_page_objects(page_objects: List[PageObject]):
    return prepare_page_objects(page_objects)


def prepare_page_objects_for_api_new_version(list_uuid):
    page_objects = PageObject.objects.filter(uuid__in=list_uuid).select_related(
        "key", "type", "processed_page"
    )
    return prepare_page_objects(page_objects)


def prepare_processed_files_for_api(dossier: Dossier):
    processed_files = ProcessedFile.objects.filter(dossier=dossier).prefetch_related(
        "extracted_file__original_file__file",
        "processed_pages",
        "processed_pages__page_objects__key",
        "processed_pages__page_objects__type",
        "processed_pages__image",
        "processed_pages__searchable_pdf",
        "processed_pages__searchable_txt",
        "processed_pages__document_category",
        "processed_pages__page_category",
    )

    return {
        str(processed_file.uuid): {
            "name": processed_file.extracted_file.file.name,
            "original_file_path": processed_file.extracted_file.original_file.file.name,
            "file_path": processed_file.extracted_file.path_from_original,
            "file_path_url_encoded": urllib.parse.quote(
                processed_file.extracted_file.path_from_original
            ),
            "filename": processed_file.extracted_file.file.name,
            "pages": {
                str(processed_page.number): {
                    "number": processed_page.number,
                    "lang": processed_page.lang,
                    "document_category": {
                        "id": processed_page.document_category.id,
                        "name": processed_page.document_category.name,
                    },
                    "page_category": {
                        "id": processed_page.page_category.id,
                        "name": processed_page.page_category.name,
                    },
                    "confidence": processed_page.confidence_value,
                    "page_objects": prepare_page_objects_for_api_with_page_objects(
                        processed_page.page_objects.all()
                    ),
                    "image": processed_page.image.fast_url,
                    "image_dossier_file_uuid": processed_page.image_id,
                    "searchable_pdf": processed_page.searchable_pdf.fast_url,
                    "searchable_pdf_dossier_file_uuid": processed_page.searchable_pdf_id,
                    "searchable_txt": processed_page.searchable_txt.fast_url,
                    "searchable_txt_dossier_file_uuid": processed_page.searchable_txt_id,
                }
                for processed_page in processed_file.processed_pages.all()
            },
        }
        for processed_file in processed_files
    }


def prepare_page_object_v2(
    semantic_page_object: SemanticPagePageObject, use_semantic_page_no: bool = False
) -> dict:
    # Returns JSON format compatible with PageObjectFullApiData
    return {
        "uuid": str(semantic_page_object.page_object.uuid),
        "key": semantic_page_object.page_object.key.key,
        "visible": semantic_page_object.page_object.visible,
        "title": semantic_page_object.page_object.key.de,
        "titles": {
            "en": semantic_page_object.page_object.key.en,
            "de": semantic_page_object.page_object.key.de,
            "fr": semantic_page_object.page_object.key.fr,
            "it": semantic_page_object.page_object.key.it,
        },
        "value": semantic_page_object.page_object.value,
        "type": semantic_page_object.page_object.type.name,
        "bbox": {
            "ref_width": semantic_page_object.page_object.ref_width,
            "ref_height": semantic_page_object.page_object.ref_height,
            "top": semantic_page_object.page_object.top,
            "left": semantic_page_object.page_object.left,
            "right": semantic_page_object.page_object.right,
            "bottom": semantic_page_object.page_object.bottom,
        },
        # We theoretically should be using the page number of the semantic page, rather than the processed page, as the
        # semantic page can be re-ordered via the webui and so the page number can change
        # keep it as it is here, as the code for compy semantic document and copy semantic pages uses
        # page_object.processed_page.number for matching
        # Also there maybe logic/dependencies on frontend for using processed_page number vs semantic_page number
        "page_number": (
            semantic_page_object.semantic_page.number
            if use_semantic_page_no
            else semantic_page_object.page_object.processed_page.number
        ),
        "confidence": semantic_page_object.page_object.confidence_value,
        "confidence_summary": {
            "value": semantic_page_object.page_object.confidence_value,
            "value_formatted": semantic_page_object.page_object.confidence_formatted,
            "level": semantic_page_object.page_object.confidence_level,
        },
        "semantic_page_uuid": str(semantic_page_object.semantic_page.uuid),
    }


def prepare_original_files(dossier: Dossier) -> Dict[str, OriginalFile]:
    return {
        str(original_file.uuid): schemas.OriginalFile.model_validate(original_file)
        for original_file in dossier.original_files.all()
    }


def prepare_dossier_files(dossier: Dossier) -> Dict[str, DossierFile]:
    return {
        str(dossier_file.uuid): schemas.DossierFile(
            uuid=dossier_file.uuid, url=dossier_file.fast_url
        )
        for dossier_file in dossier.dossierfile_set.all()
    }


def prepare_extracted_files_v2(dossier: Dossier) -> Dict[str, Dict[str, str]]:
    return {
        str(extracted_file.uuid): {
            **schemas.ExtractedFileV2.model_validate(extracted_file).model_dump(),
            "dossier_file_uuid": extracted_file.file.uuid,
        }
        for extracted_file in dossier.extractedfile_set.all()
    }


def update_dossier_expired_date_by_account(dossier: Dossier, save=True):
    if dossier.account:
        dossier.expiry_date = dossier.created_at + timedelta(
            days=dossier.account.default_dossier_expiry_duration_days
        )
        if save:
            dossier.save()


def datetime_string_to_datetime(datetime_str: str):
    return datetime.datetime.strptime(datetime_str, format_datetime)


def compare_data_from_request_with_db(list_from_request, qs):
    status_update = False

    list_uuid_from_request = map(lambda item: item["uuid"], list_from_request)
    original_files_qs = list(qs.all().values("uuid", "updated_at"))

    for uuid in list_uuid_from_request:
        item_from_db = list(
            filter(lambda x: x["uuid"] == UUID(uuid), original_files_qs)
        )
        item_from_request = list(
            filter(lambda x: x["uuid"] == uuid, list_from_request)
        )[0]
        if len(item_from_db) == 1:
            item_from_db = item_from_db[0]

            date_from_request = datetime_string_to_datetime(
                item_from_request["last_update"]
            )
            date_from_db = datetime_string_to_datetime(
                item_from_db["updated_at"].isoformat(timespec="milliseconds")
            )

            if date_from_db != date_from_request:
                status_update = True
                break
            else:
                original_files_qs.remove(item_from_db)

    if len(original_files_qs) > 0:
        status_update = True

    return status_update


def get_ordering_value_to_dossier_list(
    ordering_value: str = "created_at", type_ordering: str = "desc"
):
    """

    @param ordering_value: name of field by which should be ordered
    @param type_ordering: "desc" or "asc"
    @return: String that can be set as a order_by param in Django so sort the list of dossiers
    """
    new_ordering_value = "" if type_ordering.lower() == "asc" else "-"
    if ordering_value is None:
        new_ordering_value = "-expiry_date"
    elif ordering_value in [
        "name",
        "count_documents",
        "created_at",
        "expiry_date",
        "status",
    ]:
        new_ordering_value = new_ordering_value + ordering_value
    else:
        raise HttpError(400, "Unknown field")

    return new_ordering_value


def get_manager_filter(
    is_manager: bool, user: User, account: Account, filter_kwargs: dict or None = None
):
    if filter_kwargs is None:
        filter_kwargs = {}

    if is_manager:
        filter_kwargs.update({"account": account})
    elif user:
        filter_kwargs.update({"owner": user})

    return filter_kwargs


def delete_document_collateral_assigment(dossier):
    docs = SemanticDocument.objects.filter(dossier_id=dossier)
    for doc in docs:
        collateral = CollateralAssignment.objects.filter(
            semantic_document_id=doc.uuid
        ).first()
        if collateral:
            collateral.delete()


def compare_dossier_manager_data(dossier_dict, dossier_from_request) -> bool:
    date_from_db = datetime_string_to_datetime(
        dossier_dict["updated_at"].isoformat(timespec="milliseconds")
    )

    if date_from_db != datetime_string_to_datetime(dossier_from_request.updated_at):
        return True

    if dossier_dict["status"] != dossier_from_request.status:
        return True

    return False


def find_uuid_in_dossier_list(dossier_data, uuid) -> Dict or None:
    try:
        return next(filter(lambda x: x["uuid"] == UUID(uuid), dossier_data))
    except (StopIteration, ValueError, AttributeError):
        return None


def get_dossier_queryset():
    base_dossier_qs = Dossier.objects

    # base_dossier_qs = base_dossier_qs.annotate(
    #     status=Case(
    #         When(status=0, then=Value(FileStatus.PROCESSED)),
    #         When(status__gt=0, then=Value(FileStatus.PROCESSING)),
    #     )
    # )

    return base_dossier_qs


def save_pdf_file_to_dossier_file(
    writer: PdfWriter,
    dossier: Dossier,
    title: str,
    content_type="application/pdf",
) -> DossierFile:
    buffer = BytesIO()
    writer.write(buffer)
    content_file = ContentFile(buffer.getvalue())

    return DossierFile.objects.create(
        dossier=dossier,
        bucket=dossier.bucket,
        data=InMemoryUploadedFile(
            content_file, None, title, content_type, content_file.tell, None
        ),
    )


def validate_expiry_date(date: datetime):
    if django_timezone.now() > date:
        raise HttpError(status=400, detail="invalid date")

    return True


def update_dossier_properties(
    dossier: Dossier,
    user: User,
    dossier_schema: schemas.SemanticDossierName,
    request_user,
):
    # Depreciated, use change_dossier_properties_v2 instead
    # this is due to update_dossier_properties handling assign user logic in a weird way
    dossier.name = dossier_schema.name
    assignee_role = DossierRole.objects.filter(
        account=dossier.account, key="ASSIGNEE"
    ).first()
    dossier_request_user = DossierUser.objects.get(
        account=dossier.account, user=request_user
    )

    if dossier_schema.dossier_role:
        role_list = dossier_schema.dossier_role.split(",")
        for uuid in role_list:
            user_inv = UserInvolvement.objects.filter(dossier=dossier, role=uuid)
            if user_inv:
                if user_inv[0].role == assignee_role:
                    dossier.owner = request_user
                user_inv[0].user = dossier_request_user
                user_inv[0].save()
            else:
                role = DossierRole.objects.get(uuid=uuid)
                UserInvolvement.objects.create(
                    dossier=dossier, role=role, user=dossier_request_user
                )

    if dossier_schema.expiry_date:
        if validate_expiry_date(dossier_schema.expiry_date):
            dossier.expiry_date = dossier_schema.expiry_date

    if dossier_schema.businesscase_type_id is not None:
        dossier.businesscase_type_id = dossier_schema.businesscase_type_id

    if dossier_schema.work_status_id is not None:
        dossier.work_status_id = dossier_schema.work_status_id

    dossier.save()


def create_dossier_file_without_saving(dossier: Dossier, filename: str):
    """Note: This does NOT save the dossier file
    I'm guessing this is by design, so that the save happens in an async function
    """
    dossier_file = DossierFile()
    dossier_file.dossier = dossier
    dossier_file.bucket = dossier.bucket
    dossier_file.data = generate_path(dossier_file, filename)
    return dossier_file


def get_dossier_original_files(dossier):
    return OriginalFile.objects.filter(dossier=dossier)


def create_zip_archive(dossier):
    sanitized_dossier_name = create_export_filename(dossier.name)
    name_of_zip = sanitized_dossier_name + "_Original.zip"

    dossier_file = create_dossier_file_without_saving(dossier, name_of_zip)
    dossier_file.save()

    original_files_qs = get_dossier_original_files(dossier).select_related("file")

    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        filepath = os.path.join(temp_path, name_of_zip)

        with ZipFile(filepath, "w") as zip_file:
            for original_file in original_files_qs:
                content = BytesIO(requests.get(original_file.file.fast_url).content)

                zip_file.writestr(
                    original_file.file.name,
                    content.getvalue(),
                )

        open_and_validate_and_upload_data_to_minio(
            filepath, dossier_file.put_url, text_error="ORIGINAL_FILES_NOT_FOUND"
        )

    return dossier_file


def check_update_by_count_files(dossier: Dossier, request_data) -> bool:
    # Compares the number of Original and Extracted files and FileExceptions received
    # from the front end with the ones that are in the database
    original_files_count = dossier.original_files.count()
    extracted_files_count = dossier.extractedfile_set.count()
    exceptions_count = (
        dossier.fileexception_set.values("extracted_file__path_from_original")
        .distinct()
        .count()
    )

    original_files_with_exception_de_count = OriginalFile.objects.filter(
        ~Q(exception_de=""), exception_de__isnull=False, dossier=dossier
    ).count()

    if (
        original_files_count != len(request_data["original_files"])
        or extracted_files_count != len(request_data["extracted_files"])
        or exceptions_count + original_files_with_exception_de_count
        != len(request_data["exceptions"])
    ):
        status_update = True
    else:
        # Compares the last updating time for records in the OriginalFile, ExtractedFile, FileException
        # tables with the ones that are received from the front end
        status_update = compare_data_from_request_with_db(
            request_data["original_files"], dossier.original_files
        )
        if not status_update:
            status_update = compare_data_from_request_with_db(
                request_data["extracted_files"], dossier.extractedfile_set
            )

        # Note useful as we have duplicate entries in the FileException table
        # Re-enable if we clean this up
        # if not status_update:
        #     status_update = compare_data_from_request_with_db(
        #         request_data["exceptions"],
        #         dossier.fileexception_set.values(
        #             "extracted_file__path_from_original"
        #         ).distinct(),
        #     )

        # if not status_update:
        #     status_update = len(extracted_data["exceptions"]) != len(
        #         request_data["exceptions"]
        #     )

    return status_update


def check_update_by_dossier_properties(dossier: Dossier, request_data) -> bool:
    status_update = False

    mapper_dossier_key = "dossier_key"
    mapper_request_key = "dossier_key"
    mapper_keys = [
        {mapper_dossier_key: "name", mapper_request_key: "name"},
        {mapper_dossier_key: "note", mapper_request_key: "note"},
    ]

    for object_with_keys in mapper_keys:
        value_from_request = request_data.get(object_with_keys[mapper_request_key])

        if value_from_request is not None:
            dossier_value = dossier.__getattribute__(
                object_with_keys[mapper_dossier_key]
            )

            if dossier_value != value_from_request:
                status_update = True

    # 240411 If businesscase_type_id is provided by frontend, then compare it. Else skip it
    # BCT_NOT_INITIALIZED is necessary because the frontend might not support this feature and we need to distinguish
    # between "not delivered by frontend" and "frontend delivers None"
    bct_request = request_data.get("businesscase_type_id")

    # bct_request = "d2d4b4b4-304e-4267-8176-bac25b37b235"
    if bct_request != BCT_NOT_INITIALIZED:
        bct_dossier = (
            str(dossier.businesscase_type.uuid) if dossier.businesscase_type else None
        )
        one_is_none = bct_request is None and bct_dossier is not None
        both_different = bct_request != bct_dossier
        if one_is_none or both_different:
            status_update = True

    return status_update


def get_confidence_level(confidence_value: int):
    mapper_confidence = {
        95: ConfidenceLevel.HIGH,
        90: ConfidenceLevel.MEDIUM,
        80: ConfidenceLevel.LOW,
    }

    list_keys = list(mapper_confidence.keys())
    list_keys.sort(reverse=True)

    try:
        confidence_key = next(filter(lambda x: x < confidence_value, list_keys))
    except StopIteration:
        confidence_key = min(
            list_keys, key=lambda input_list: abs(input_list - confidence_value)
        )

    return mapper_confidence[confidence_key]


def update_page_object_confidence(
    page_object, data: schemas.UpdatePageObjectConfidenceRequest
):
    confidence_value = int(data.confidence_value * 100)

    if confidence_value > 100:
        raise HttpError(
            status=400, detail=f"invalid confidence value: {confidence_value}"
        )

    if data.confidence_value == 0:
        page_object.visible = False

    page_object.confidence_value = data.confidence_value
    page_object.confidence_formatted = f"{confidence_value}%"
    page_object.confidence_level = get_confidence_level(confidence_value)
    page_object.save()

    return page_object


def is_document_topic_property(document_category: DocumentCategory):
    """
    is_document_topic_property() cannot be cleanly implemented as we do not yet have a concept of DocumentTopic
    (where each DocumentCategory would be in a DocumentTopic).
    This catches all 6xx numbers if the naming scheme of document categories is not customized
    """
    return document_category.id.startswith("6")


def remove_diacritics(char):
    """
    Removes any diacritics like accents or curls and strokes from a character.

    Args:
        char (str): The character from which to remove diacritics.

    Returns:
        str: The base character without diacritics.
    """
    desc = ud.name(char)
    cutoff = desc.find(" WITH ")
    if cutoff != -1:
        desc = desc[:cutoff]
        try:
            char = ud.lookup(desc)
        except KeyError:
            pass  # removing "WITH ..." produced an invalid name
    return char


def create_export_filename(old_filename):
    filename = old_filename
    filename = filename.replace(" / ", "_").replace("/", "_")
    filename = "".join([remove_diacritics(c) for c in filename])
    filename = filename.replace(" ", "_")
    filename = filename.replace("__", "_")
    filename = filename.encode("ASCII", errors="replace")
    filename = filename.decode()
    # max length of string is 255
    if len(filename) > 250:
        filename_last = filename[-30:]
        filename = f"{filename[0:220]}...{filename_last}"
    return filename


def get_all_s3_objects_in_bucket(dossier_bucket: str) -> List[str]:
    # Get all objects in an s3 bucket - used for testing
    associated_objects = []

    for obj in minio_client.list_objects(bucket_name=dossier_bucket, recursive=True):
        associated_objects.append(obj.object_name)

    return associated_objects
