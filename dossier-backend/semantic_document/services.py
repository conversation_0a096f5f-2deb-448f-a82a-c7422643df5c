from pathlib import Path
from typing import List, Optional, Union, Set, Tuple
from uuid import UUID, uuid4

from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Count, Q, OuterRef, Subquery, QuerySet, Prefetch
from django.db.models.functions import TruncSecond
from django.shortcuts import get_object_or_404
from django.utils import timezone

from clientis.services import create_clientis_export_package
from core.helpers import remove_invalid_chars
from core.publisher import publish as rabbit_mq_publish
from doccheck.models import Case, CompletenessRule
from dossier import models as dossier_models
from dossier.doc_cat_helpers import UNKNOWN_DOCUMENT_CATEGORY_KEYS
from dossier.exceptions import HttpError
from dossier.helpers import (
    create_dossier_file_without_saving,
    prepare_page_object_v2,
    is_document_topic_property,
)
from dossier.helpers_access_check import get_dossier_with_access_check
from dossier.helpers_v2 import prepare_semantic_dossier
from dossier.models import (
    Dossier,
    DocumentCategory,
    Account,
    ConfidenceLevel,
    RealestateProperty,
    SemanticDocumentExportStrategy,
)
from dossier.schemas import SemanticDossier, PageObjectFullApiData
from events.services import publish as event_publish
from projectconfig.settings import (
    ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
)
from semantic_document import helpers as semantic_document_helpers, models, schemas
from semantic_document.events import create_semantic_pages_moved_event
from semantic_document.helpers import (
    create_semantic_page_moved_detail,
    fix_document_page_numbering,
)
from semantic_document.models import (
    SemanticDocument,
    DocCheckAssignment,
    AssignedRealEstatePropertySemanticDocument,
    SemanticPage,
    SemanticPagePageObject,
    SemanticDocumentPageObject,
)
from semantic_document.schemas_pss import PageStreamForSplit
from semantic_document.services_doccheck import get_doccheck_document_assignments
from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
    create_context_for_semantic_document_state_transition,
)
from statemgmt.models import Status, StateTransition, StateMachine
from statemgmt.services import validate_state_transition
from workers.models import SemanticDocumentExport
from workers.schemas import SemanticDocumentPDFRequestV1
from semantic_document.services_move_pages import (
    gather_move_pages_diagnostic_info,
    validate_source_pages_belong_to_same_dossier,
    validate_page_count_after_movecopy_pages_operation,
)


def assign_semantic_documents_to_doccheck_rule(
    case: Case,
    context_model_uuid: UUID,
    completeness_rule: CompletenessRule,
    fulfillment_type: models.FulfillmentType,
    semantic_documents: Optional[List[SemanticDocument]] = None,
    comment: Optional[str] = None,
) -> schemas.CreatedDocCheckAssignment:
    """

    @param case:
    @param context_model_uuid:
    @param completeness_rule:
    @param fulfillment_type:
    @param semantic_documents:
    @param comment: If this is None, do not change the comment. If it is an empty string, delete the comment. Else
    update the comment.
    @return:
    """

    if case.doc_check != completeness_rule.doc_check:
        raise LookupError("case and completeness_rule don't have the same doc_check")

    if context_model_uuid not in [p.uuid for p in case.person_set.all()] + [
        p.uuid for p in case.realestateproperty_set.all()
    ]:
        raise LookupError("context_model_uuid is not part of case")

    if semantic_documents:
        for doc in semantic_documents:
            if doc.dossier.doccheck_case != case:
                raise LookupError(
                    f"case associated with semantic document uuid={doc.uuid} does not match case given"
                )

    # drop this check for now as we want the frontend dialogue to be able to reset the assignments to no doc and no comment
    # if not semantic_documents and not comment:
    #     raise LookupError("semantic_documents and comment cannot both be None")

    assignment, _ = DocCheckAssignment.objects.get_or_create(
        case=case,
        context_model_uuid=context_model_uuid,
        completeness_rule=completeness_rule,
        # fulfillment must not be in here as it is not part of the uniqueness constraint
    )
    assignment.fulfillment_type = fulfillment_type
    assignment.assigned_documents.clear()
    if semantic_documents:
        assignment.assigned_documents.add(*semantic_documents)

    if comment is None:
        # Do not change the comment
        pass
    elif len(comment.strip()) == 0:
        assignment.comment = None
    else:
        assignment.comment = comment

    assignment.save()

    return schemas.CreatedDocCheckAssignment(uuid=assignment.uuid)


# def calc_doccheck_assignment_out_id(case_uuid: UUID, context_model_id: UUID, rule_key: str, hash: bool = True) -> str:
#     assignment_id = f"case_uuid={case_uuid} | cmodel_uuid={context_model_id} | rule_key={rule_key}"
#
#     if hash:
#         assignment_id = hashlib.md5(assignment_id.encode('utf-8')).hexdigest()
#
#     return assignment_id


def assign_semantic_documents_to_doccheck_rules_automatically(
    case: Case, make_assignment=False
) -> List[schemas.DocCheckAssignmentOut]:
    options_for_automatic_assignment_overall = []
    semantic_documents = case.dossier.semantic_documents.all()
    assignments = get_doccheck_document_assignments(case)

    # Loop over all assignments that are not yet fulfilled
    for assignment in [
        assignment
        for assignment in assignments
        if assignment.requirement_status == schemas.RequirementStatus.NOT_FULFILLED
    ]:
        options_for_automatic_assignment_per_requirement = []

        # Loop over all options of document_categories that are valid for this assignment
        for document_option in assignment.document_options:
            document_option_category_name = document_option.document_category

            # Find all semantic documents that match the document_option
            matching_semantic_documents = [
                semantic_document
                for semantic_document in semantic_documents
                if semantic_document.document_category.name
                == document_option_category_name
            ]
            if len(matching_semantic_documents) == 1:
                options_for_automatic_assignment_per_requirement.append(
                    matching_semantic_documents[0]
                )

        if len(options_for_automatic_assignment_per_requirement) > 0:
            options_for_automatic_assignment_per_requirement.sort(key=lambda x: x.title)
            options_for_automatic_assignment_overall.append(
                schemas.DocCheckAssignmentOut(
                    case_uuid=case.uuid,
                    context_model_uuid=assignment.context_model_uuid,
                    rule_key=assignment.rule_key,
                    rule_title_de=assignment.rule_title_de,
                    rule_title_en=assignment.rule_title_en,
                    rule_title_fr=assignment.rule_title_fr,
                    rule_title_it=assignment.rule_title_it,
                    rule_desc_de=assignment.rule_desc_de,
                    rule_desc_en=assignment.rule_desc_en,
                    rule_desc_fr=assignment.rule_desc_fr,
                    rule_desc_it=assignment.rule_desc_it,
                    rule_strictness=assignment.rule_strictness,
                    rule_order=assignment.rule_order,
                    document_options=assignment.document_options,
                    assigned_document_uuids=[
                        option.uuid
                        for option in options_for_automatic_assignment_per_requirement
                    ],
                    assigned_document_titles=[
                        option.title
                        for option in options_for_automatic_assignment_per_requirement
                    ],
                    fulfillment_type=schemas.FulfillmentType.DOC_ASSIGNMENT,
                    requirement_status=schemas.RequirementStatus.NOT_FULFILLED,
                )
            )

    if make_assignment:
        for option in options_for_automatic_assignment_overall:
            completeness_rule = CompletenessRule.objects.get(key=option.rule_key)
            semantic_documents = list(
                SemanticDocument.objects.filter(uuid__in=option.assigned_document_uuids)
            )
            _ = assign_semantic_documents_to_doccheck_rule(
                case=case,
                context_model_uuid=option.context_model_uuid,
                completeness_rule=completeness_rule,
                fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
                semantic_documents=semantic_documents,
            )

    return options_for_automatic_assignment_overall


def create_move_semantic_page_schema_from_semantic_pages_queryset(
    semantic_pages: Union[List[SemanticPage], QuerySet],
) -> List[schemas.MoveSemanticPageSchema]:
    return [
        schemas.MoveSemanticPageSchema(
            uuid=str(semantic_page.uuid),
            source_file_uuid=str(semantic_page.processed_page.processed_file.uuid),
            number=semantic_page.processed_page.number,
            page_objects=[
                prepare_page_object_v2(page_obj)
                for page_obj in semantic_page.semantic_page_page_objects.all()
            ],
        )
        for semantic_page in semantic_pages
    ]


def calculate_combine_pages_real_estate_properties(
    dossier: Dossier,
    category: DocumentCategory,
) -> schemas.PermittedCombinePagesCombinations:
    qs = AssignedRealEstatePropertySemanticDocument.objects.filter(
        semantic_document__dossier=dossier,
        semantic_document__document_category=category,
    )

    real_estate_enabled = dossier.account.enable_real_estate_properties

    permitted_combinations = schemas.PermittedCombinePagesCombinations(combinations={})

    if real_estate_enabled:
        realestate_property_list = list(
            qs.values_list("realestate_property", flat=True)
            .annotate(count=Count("realestate_property"))
            .filter(count__gt=1)
        )

        # Subquery that counts non-deleted pages of each semantic document
        non_deleted_pages_count_subquery = (
            (
                (
                    SemanticPage.objects.filter(
                        semantic_document=OuterRef(
                            "semantic_document"
                        ),  # OuterRef refers to semantic_document in the main query
                        deleted_at__isnull=True,  # only count pages that are not deleted
                    ).values("semantic_document")
                ).annotate(  # group by semantic_document
                    count=Count("uuid")
                )
            )
            # count pages
            .values("count")
        )  # return count only

        # Intermediate table mapping semantic documents and real estate properties
        assigned_realestateproperty_semantic_documents = (
            qs.order_by("realestate_property")
            .prefetch_related(
                "semantic_document__semantic_pages", "realestate_property"
            )
            .filter(realestate_property__uuid__in=realestate_property_list)
            # Important - exclude soft deleted semantic documents and documents with no pages (including soft deleted ones)
            .exclude(semantic_document__deleted_at__isnull=False)
            .annotate(
                non_deleted_pages_count=Subquery(non_deleted_pages_count_subquery)
            )
            .exclude(non_deleted_pages_count=0)
        )

        for assignment in assigned_realestateproperty_semantic_documents:
            combine_pages = schemas.RealEstatePropertyAssignmentCombinePages(
                real_estate_property_key=assignment.realestate_property.key,
                semantic_document_uuid=assignment.semantic_document.uuid,
                semantic_pages_uuid=list(
                    assignment.semantic_document.semantic_pages.values_list(
                        "uuid", flat=True
                    )
                ),
            )

            if (
                combine_pages.real_estate_property_key
                not in permitted_combinations.combinations
            ):
                permitted_combinations.combinations[
                    combine_pages.real_estate_property_key
                ] = []

            permitted_combinations.combinations[
                combine_pages.real_estate_property_key
            ].append(combine_pages)

        # Finally handle the case where we have semantic documents with no real estate property assigned
        semantic_docs_in_mapping_table = qs.values_list(
            "semantic_document__uuid", flat=True
        )

        semantic_documents_no_real_estate = SemanticDocument.objects.filter(
            dossier=dossier, document_category=category
        ).exclude(uuid__in=semantic_docs_in_mapping_table)

    else:
        # This is to handle the case where real estate properties are not enabled
        # but we have assigned real estate properties in the mapping table
        semantic_documents_no_real_estate = SemanticDocument.objects.filter(
            dossier=dossier, document_category=category
        )

    semantic_documents_no_real_estate = semantic_documents_no_real_estate.annotate(
        page_count=Count(
            "semantic_pages", filter=Q(semantic_pages__deleted_at__isnull=True)
        )
    ).order_by(
        "uuid"
    )  # Add explicit ordering by UUID for consistency

    if category.name in [
        "TAX_DECLARATION",
        "TAX_CALCULATION",
        "TAX_LIST_FINANCIAL_ASSETS",
        "TAX_DEBT_INVENTORY",
        "TAX_BUDGET",
    ]:
        # Only Combine tax docs with single pages
        semantic_documents_no_real_estate = semantic_documents_no_real_estate.filter(
            page_count=1
        )
    else:
        # For all other categories take all documents with at least 1 non-deleted page
        semantic_documents_no_real_estate = semantic_documents_no_real_estate.filter(
            page_count__gt=0
        )

    if (
        semantic_documents_no_real_estate.exists()
        and semantic_documents_no_real_estate.count() > 1
    ):
        combine_pages = []
        for sem_doc in semantic_documents_no_real_estate:
            combine_pages.append(
                schemas.RealEstatePropertyAssignmentCombinePages(
                    real_estate_property_key=schemas.COMBINE_PAGES_UNASSIGNED,
                    semantic_document_uuid=sem_doc.uuid,
                    semantic_pages_uuid=list(
                        sem_doc.semantic_pages.values_list("uuid", flat=True)
                    ),
                )
            )

        permitted_combinations.combinations[schemas.COMBINE_PAGES_UNASSIGNED] = (
            combine_pages
        )

    # Finally loop over all combinations and ensure that if there is a combination with only one semantic document
    # we remove that combination from the list

    for key in list(permitted_combinations.combinations.keys()):
        if len(permitted_combinations.combinations[key]) < 2:
            del permitted_combinations.combinations[key]

    return permitted_combinations


def copy_move_semantic_pages(
    *,  # Force the use of named params, otherwise I could see this going horribly wrong
    semantic_document: SemanticDocument,
    semantic_pages_schema_list: List[schemas.MoveSemanticPageSchema],
    source_semantic_pages: List[schemas.SemanticPageMovedDetail],
    list_of_semantic_page_uuid: List[str],
    user: AbstractUser,
    move: bool,
) -> List[SemanticPage]:
    """
    Core logic for copying or moving semantic pages to a specific semantic document
    @param semantic_document: target semantic document
    @param semantic_pages_schema_list:
    @param source_semantic_pages:
    @param list_of_semantic_page_uuid: uuids of semantic pages to be copied/moved
    @param user:
    @param move: If true move pages else copy them
    @return:


    Note: This does NOT adjust the page number of the copied/moved pages. This is done in the calling function
    """
    # Gather diagnostic information at the start
    source_pages = SemanticPage.all_objects.filter(uuid__in=list_of_semantic_page_uuid)
    diagnostic_info = gather_move_pages_diagnostic_info(
        semantic_document=semantic_document,
        source_pages=source_pages,
        list_of_semantic_page_uuid=list_of_semantic_page_uuid,
        move=move,
    )

    # Verify all semantic pages belong to the same dossier
    validate_source_pages_belong_to_same_dossier(
        source_pages=source_pages,
        target_semantic_document=semantic_document,
    )

    # Count the number of semantic pages in the dossier before any modifications, including soft-deleted ones
    initial_page_count = diagnostic_info["page_counts"]["initial"]

    if move:
        deleted = semantic_document_helpers.delete_semantic_pages(
            list_of_semantic_page_uuid, True
        )
        created_semantic_pages = semantic_document_helpers.save_deleted_semantic_page(
            deleted, semantic_document
        )

    else:
        list_semantic_pages = SemanticPage.objects.select_related(
            "dossier",
            "page_category",
            "semantic_document",
            "document_category",
            "processed_page",
        ).filter(uuid__in=list_of_semantic_page_uuid)
        created_semantic_pages = (
            semantic_document_helpers.create_duplicates_semantic_page(
                semantic_pages_schema_list, list_semantic_pages, semantic_document
            )
        )

    created_page_objects = (
        semantic_document_helpers.create_page_object_to_moved_semantic_page(
            semantic_pages_schema_list, created_semantic_pages
        )
    )

    semantic_document_helpers.create_aggregated_page_objects(
        semantic_document, created_page_objects
    )

    event_publish(
        create_semantic_pages_moved_event(
            user, source_semantic_pages, created_semantic_pages
        )
    )

    # Verify the final page count matches our expectations
    validate_page_count_after_movecopy_pages_operation(
        semantic_document=semantic_document,
        initial_page_count=initial_page_count,
        list_of_semantic_page_uuid=list_of_semantic_page_uuid,
        move=move,
        diagnostic_info=diagnostic_info,
    )

    return created_semantic_pages


def copy_move_semantic_pages_from_queryset(
    *,
    semantic_document: SemanticDocument,
    user: AbstractUser,
    move: bool,
    semantic_pages: Union[List[SemanticPage], QuerySet] = models.SemanticPage,
) -> List[SemanticPage]:
    """
    Move or copy a list of semantic pages into another semantic document

    Wrapper around copy_move_semantic_pages that uses django query sets as params
    @param semantic_document: Target doc for moved/copies pages
    @param user:
    @param move: If true, pages are moved else copied
    @param semantic_pages: List of the semantic pages that will be moved/copied
    @return:
    """

    # This will be the first index for the next page to be appended
    initial_sem_page_number = semantic_document.semantic_pages.count()

    semantic_pages_schema_list: List[schemas.MoveSemanticPageSchema] = (
        create_move_semantic_page_schema_from_semantic_pages_queryset(semantic_pages)
    )

    source_semantic_pages: List[schemas.SemanticPageMovedDetail] = []
    for source_semantic_page in semantic_pages:
        source_semantic_pages.append(
            create_semantic_page_moved_detail(source_semantic_page)
        )

    created_semantic_pages = copy_move_semantic_pages(
        semantic_document=semantic_document,
        semantic_pages_schema_list=semantic_pages_schema_list,
        source_semantic_pages=source_semantic_pages,
        list_of_semantic_page_uuid=[str(x.uuid) for x in semantic_pages],
        user=user,
        move=move,
    )

    # copy_move_semantic_pages copies the pages but does not adjust the page number correctly
    # So we need to adjust the semantic_page.number so the counting starts after the
    # page numbers of semantic document we are adding to.
    for idx, sem_page in enumerate(created_semantic_pages):
        sem_page.number = idx + initial_sem_page_number
        sem_page.save()

    return created_semantic_pages


def copy_or_move_semantic_pages_to_new_document(
    *,
    dossier: Dossier,
    user: AbstractUser,
    body: schemas.MoveSemanticPagesToNewDocumentSchema,
    move: bool,
):
    """Copy or move semantics pages to a new document
    service class specialized for API usage, as it expects a pydantic schema
    with params

    Not a fan of writing code this way - much prefer to pass around django querysets,
    however don't want to refactor or change this too much as it's already used in production
    and works

    @param dossier: Dossier in which the pages should be copied/moved
    @param user: User that initiates the copy/move. Not necessarily the owner
    @param body: Information which pages should be copied/moved
    @param move: If true pages are moved. Else pages are copied
    @return:
    """
    realestate_property = None
    if body.real_estate_property_key:
        realestate_property = get_object_or_404(
            dossier_models.RealestateProperty,
            dossier=dossier,
            key=body.real_estate_property_key,
        )

    # List of uuids of semantic pages to be copied/moved
    list_of_semantic_page_uuid = [
        semantic_page.uuid for semantic_page in body.semantic_pages
    ]

    with transaction.atomic():
        # Create new target document
        semantic_document = semantic_document_helpers.create_unnamed_semantic_document(
            dossier, body.model_dump()
        )

        # List of pages to be copied/moved
        source_semantic_pages_qs = SemanticPage.objects.filter(
            uuid__in=[UUID(uuid_str) for uuid_str in list_of_semantic_page_uuid]
        )

        # List of source documents for the copy/move
        source_semantic_documents = list(
            SemanticDocument.objects.filter(
                semantic_pages__in=source_semantic_pages_qs
            ).all()
        )

        # Create the list of semantic pages with their details that should be moved/copied
        source_semantic_pages: List[schemas.SemanticPageMovedDetail] = []
        for source_semantic_page in source_semantic_pages_qs:
            source_semantic_pages.append(
                create_semantic_page_moved_detail(source_semantic_page)
            )

        created_semantic_pages = copy_move_semantic_pages(
            semantic_document=semantic_document,
            semantic_pages_schema_list=body.semantic_pages,
            source_semantic_pages=source_semantic_pages,
            list_of_semantic_page_uuid=list_of_semantic_page_uuid,
            user=user,
            move=move,
        )

        # copy_move_semantic_pages copies the pages but does not adjust the page number correctly
        # So we need to adjust the semantic_page.number so the counting starts after the
        # Fix page numbers in target document
        fix_document_page_numbering(
            semantic_document,
            fix_non_sequential_pages=True,
            include_soft_deleted_pages=True,
        )

        # Fix page numbers in all the the source sem_docs
        for sem_doc in source_semantic_documents:
            fix_document_page_numbering(
                sem_doc, fix_non_sequential_pages=True, include_soft_deleted_pages=True
            )

        if realestate_property:
            # There should only be one AssignedRealEstatePropertySemanticDocument per semantic document
            # As we are creating a new semantic document, this should never delete anything
            AssignedRealEstatePropertySemanticDocument.objects.filter(
                semantic_document=semantic_document
            ).all().delete()

            AssignedRealEstatePropertySemanticDocument.objects.create(
                semantic_document=semantic_document,
                realestate_property=realestate_property,
            )

        return schemas.MoveSemanticPagesToNewDocumentResponseSchema(
            semantic_document_uuid=str(semantic_document.uuid),
            semantic_pages_number=[
                page.processed_page.number for page in created_semantic_pages
            ],
            real_estate_property_key=(
                realestate_property.key if realestate_property else None
            ),
        )


def export_semantic_document_wrapper(
    dossier: Dossier,
    semantic_document: SemanticDocument,
    semantic_dossier: SemanticDossier,
) -> SemanticDocumentPDFRequestV1:

    # The title of the semantic document looks like it might add a suffix or not
    # so we have to check
    add_uuid_suffix = dossier.account.document_download_ui_add_uuid_suffix
    filename = create_semantic_document_pdf_filename(
        semantic_document.title, semantic_document.uuid, add_uuid_suffix
    )

    if (
        dossier.account.semantic_document_export_strategy
        == SemanticDocumentExportStrategy.SWISSCOM_EDOSSIER_XML_ZIP
    ):
        # For Swisscom eDossier we replace the export PDF by a zip file which
        # contains the PDF + a metadata XML
        filename_pdf = filename
        metadata = create_clientis_export_package(
            semantic_document=semantic_document,
            dokumentenname=filename_pdf,
        )

        # As we are bundling the xml with the pdf, we need to change the extension
        # Stem of zip and pdf must be identical
        filename_zip = str(Path(filename_pdf).with_suffix(".zip"))
        filename = filename_zip
        add_uuid_suffix = False
    else:
        # Disable only for external API only
        add_uuid_suffix = dossier.account.document_download_ui_add_uuid_suffix
        metadata = None

    dossier_file: dossier_models.DossierFile = create_dossier_file_without_saving(
        dossier, filename
    )
    # IMPORTANT: save dossier_file as the create function does not save it to the database
    dossier_file.save()

    export_request_uuid = uuid4()

    semantic_document_export = SemanticDocumentExport()
    semantic_document_export.uuid = export_request_uuid
    # We need to keep an FK to the dossier as the generate path for minio uses the dossier_id in the path
    # If we separate out DossierFile, into DossierFile and SemanticDocumentFile, then we can remove this FK
    semantic_document_export.dossier = dossier
    semantic_document_export.semantic_document = semantic_document
    # If we separate out DossierFile, into DossierFile and SemanticDocumentFile, then
    # dossier_file should be renamed to semantic_document_file
    semantic_document_export.file = dossier_file

    semantic_document_export.save()

    data = semantic_dossier.model_dump()
    data["uuid"] = str(data["uuid"])
    semantic_dossier = SemanticDossier(**data)

    request = SemanticDocumentPDFRequestV1(
        semantic_document_pdf_request_uuid=semantic_document_export.uuid,
        semantic_dossier=semantic_dossier,
        semantic_document_uuid=semantic_document.uuid,
        put_upload_url=dossier_file.put_url,
        add_metadata=dossier.account.enable_download_metadata_json,
        enable_pdfa_conversion_for_export=dossier.account.enable_pdfa_conversion_for_export,
        semantic_document_export_strategy=dossier.account.semantic_document_export_strategy,
        metadata=metadata,
        add_uuid_suffix=add_uuid_suffix,
    )

    return request


def export_semantic_document_wrapper_with_access_check(
    dossier_user, external_dossier_id, semantic_document_uuid
) -> SemanticDocumentPDFRequestV1:
    """
    LEGACY Wrapper to export a single semantic document from a dossier

    Currently, assumes a synchronous process, will be replaced by async process
    generate_semantic_document_export
    """
    dossier: dossier_models.Dossier = get_dossier_with_access_check(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    if dossier_models.OriginalFile.objects.filter(dossier=dossier).first() is None:
        raise HttpError(
            400,
            f"Dossier {dossier.uuid} with external id {dossier.external_id} does not have any files",
        )

    semantic_document = get_object_or_404(
        SemanticDocument, uuid=semantic_document_uuid, dossier=dossier
    )

    # Computationally expensive, which is why we moved it out
    semantic_dossier: SemanticDossier = prepare_semantic_dossier(
        dossier=dossier,
        hide_empty_semantic_documents=True,
        # I think disabling page objects might breaks the export - check this
        enable_page_objects=False,
        include_annotations=True,
    )

    return export_semantic_document_wrapper(
        dossier, semantic_document, semantic_dossier
    )


# def generate_semantic_document_export(
#     request, external_dossier_id, semantic_document_uuid
# ) -> tuple[SemanticDocumentPDFRequestV2, SemanticDocument]:
#     """Create pydantic object with information to generate an export of a single semantic document from a dossier
#     It performs the following steps:
#
#     1. User Access Check
#     2. Prepares a semantic dossier (prepare_semantic_dossier)
#
#     We don't create a SemanticDocumentExport object, as this is done after a publish command is dispatched to workers
#
#     Designed to be used with an async process, Publish to RabbitMQ and not wait for processing to finish
#
#     Replaces export_semantic_document_wrapper.
#
#     # NOTE: The worker that accepts SemanticDocumentPDFRequestV2 has not currently been finished
#
#     """
#     dossier_user = request.auth.get_user_or_create()
#
#     dossier: dossier_models.Dossier = get_dossier_with_access_check(
#         dossier_user=dossier_user,
#         is_manager=True,  # Fix this to do proper ownership
#         external_dossier_id=external_dossier_id,
#     )
#
#     account: dossier_models.Account = dossier_user.account
#
#     if dossier_models.OriginalFile.objects.filter(dossier=dossier).first() is None:
#         raise HttpError(
#             400,
#             f"Dossier {dossier.uuid} with external id {dossier.external_id} does not have any files",
#         )
#
#     # Check that the semantic document exists
#     semantic_document = get_object_or_404(
#         SemanticDocument, uuid=semantic_document_uuid, dossier=dossier
#     )
#
#     semantic_dossier: SemanticDossier = prepare_semantic_dossier(
#         dossier, hide_empty_semantic_documents=True
#     )
#
#     export_request_uuid = uuid4()
#
#     return (
#         SemanticDocumentPDFRequestV2(
#             semantic_document_pdf_request_uuid=export_request_uuid,
#             semantic_dossier=semantic_dossier,
#             semantic_document_uuid=semantic_document_uuid,
#             add_metadata=account.enable_download_metadata_json,
#         ),
#         semantic_document,
#     )


def get_ordered_semantic_page_objects(
    semantic_document: SemanticDocument,
    additional_select_related: Optional[List[str]] = None,
) -> QuerySet:
    base_relations = [
        "page_object",
        "semantic_page",
        "page_object__key",
        "page_object__type",
        "page_object__processed_page",
    ]

    if additional_select_related:
        # Add additional select related to the base_relations e.g.
        # "semantic_page__semantic_document__document_category"
        base_relations += additional_select_related

    return (
        SemanticPagePageObject.objects.filter(
            semantic_page__semantic_document=semantic_document
        )
        .select_related(*base_relations)
        .order_by(
            "semantic_page__semantic_document",
            "semantic_page__number",
            "page_object__top",
            "page_object__right",
        )
    )


def filter_unique_page_objects(
    sp_page_objects: QuerySet,
) -> List[SemanticPagePageObject]:
    # Filter so that page_object.key is unique per semantic document

    unique_keys: Set[str] = set()
    unique_objects = []

    for spo in sp_page_objects:
        key = spo.page_object.key.key
        if key not in unique_keys:
            unique_keys.add(key)
            unique_objects.append(spo)

    return unique_objects


def export_aggregate_page_objects_from_semantic_document(
    semantic_document: SemanticDocument,
) -> List[PageObjectFullApiData]:
    # Return page objects for a specific semantic document - for a semantic page, if multiple page objects of the same
    # type exist (same category key), only return the first one.
    # This way the page objects are unique per semantic page
    sp_page_objects: SemanticPagePageObject.objects = get_ordered_semantic_page_objects(
        semantic_document
    )

    # Filter so that page_object.key is unique per semantic document
    unique_page_objects: List[SemanticPagePageObject] = filter_unique_page_objects(
        sp_page_objects
    )
    return [
        PageObjectFullApiData.model_validate(
            prepare_page_object_v2(spo, use_semantic_page_no=True)
        )
        for spo in unique_page_objects
    ]


def map_confidence(confidence_value):
    if confidence_value > 0.95:
        return schemas.Confidence.HIGH
    if confidence_value > 0.8:
        return schemas.Confidence.MEDIUM
    return schemas.Confidence.LOW


def check_transition_semantic_document_to_ready_for_export(
    account: Account, semantic_document_work_status: Status
) -> Tuple[Status, Status]:

    # Check that account has state machine enabled for semantic document
    if account.active_semantic_document_work_status_state_machine is None:
        raise HttpError(
            400,
            f"Account {account} does not have semantic document export state machine set",
        )

    # Initial state of the state machine, usually 'In front office'
    front_office_state = (
        account.active_semantic_document_work_status_state_machine.start_status
    )

    if front_office_state is None:
        raise HttpError(
            400,
            f"No start status set for semantic document state machine in account '{account.key}'",
        )

    # This is also checked by the finite state machine, but we want to give a more specific error message
    if semantic_document_work_status != front_office_state:
        raise HttpError(
            400,
            f"Semantic document is not in the correct state to be exported {semantic_document_work_status}",
        )

    ready_for_export_state = Status.objects.get(
        key=SemanticDocumentState.READY_FOR_EXPORT.value,
        state_machine=account.active_semantic_document_work_status_state_machine,
    )
    transition = StateTransition.objects.filter(
        from_state=front_office_state,
        to_state=ready_for_export_state,
        state_machine=ready_for_export_state.state_machine,
    ).first()

    if not transition:
        # Should never happen
        raise HttpError(400, "Invalid work status transition")

    context = create_context_for_semantic_document_state_transition()

    validate_state_transition(
        context, semantic_document_work_status, ready_for_export_state
    )

    return front_office_state, ready_for_export_state


def update_semantic_document_and_trigger_export(
    dossier: Dossier,
    semantic_document: SemanticDocument,
    front_office_state: Status,
    ready_for_export_state: Status,
    semantic_dossier: SemanticDossier,
) -> Optional[SemanticDocumentPDFRequestV1]:
    """

    @param dossier:
    @param semantic_document:
    @param front_office_state:
    @param ready_for_export_state:
    @param semantic_dossier:
    @return:  SemanticDocumentPDFRequestV1 that got published to perform the export.
    This contains the semantic_document_pdf_request_uuid
    """

    request: Optional[SemanticDocumentPDFRequestV1] = None

    num_pages = semantic_document.semantic_pages.count()
    if num_pages == 0:
        # Empty document is deleted without warning and ignored
        semantic_document.delete()
    elif semantic_document.work_status == front_office_state:

        with transaction.atomic():
            semantic_document.work_status = ready_for_export_state
            semantic_document.access_mode = "read_only"
            semantic_document.save()

            request: SemanticDocumentPDFRequestV1 = export_semantic_document_wrapper(
                dossier,
                semantic_document,
                semantic_dossier=semantic_dossier,
            )

        # Keep the publish out of the transaction so the transaction finishes quickly
        rabbit_mq_publish(
            message=request.model_dump_json().encode(),
            routing_key=ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
        )

    return request


def set_semantic_document_ready_for_export(
    semantic_document: SemanticDocument,
) -> Optional[UUID]:
    """
    This could have been included in either semantic_document app or statemgmt app.
    The reason for including it here is that "semantic documents know about their state machine".
    The state machine knows nothing about other apps (as they should be reused between dossiers and state machines).
    So this is in the right place here.

    @param semantic_document:
    @return:
    """
    dossier: Dossier = semantic_document.dossier
    account: Account = dossier.account

    front_office_state, ready_for_export_state = (
        check_transition_semantic_document_to_ready_for_export(
            account=account, semantic_document_work_status=semantic_document.work_status
        )
    )

    # Computationally expensive, which is why we moved it out
    semantic_dossier: SemanticDossier = prepare_semantic_dossier(
        dossier=dossier,
        include_annotations=True,
        hide_empty_semantic_documents=True,
        enable_page_objects=False,
    )

    # This returns None if empty doc was deleted
    request = update_semantic_document_and_trigger_export(
        dossier=dossier,
        semantic_document=semantic_document,
        front_office_state=front_office_state,
        ready_for_export_state=ready_for_export_state,
        semantic_dossier=semantic_dossier,
    )

    if request:
        return request.semantic_document_pdf_request_uuid

    return None


def api_set_semantic_document_ready_for_export(
    dossier: Dossier, semantic_document_uuid: UUID
) -> Optional[UUID]:
    """

    @param dossier:
    @param semantic_document_uuid:
    @return: UUID of created semantic_document_export or None if empty doc was deleted
    """

    queryset = SemanticDocument.objects.annotate(
        page_count=Count("semantic_pages")
    ).filter(
        uuid=semantic_document_uuid,
        work_status__key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        work_status__state_machine=dossier.account.active_semantic_document_work_status_state_machine,
        dossier=dossier,
        page_count__gt=0,
    )

    if not dossier.account.enable_semantic_document_export_unknown_documents:
        queryset = queryset.exclude(
            document_category__name__in=UNKNOWN_DOCUMENT_CATEGORY_KEYS
        )

    semantic_document = get_object_or_404(queryset)

    semdoc_export_uuid = set_semantic_document_ready_for_export(semantic_document)

    return semdoc_export_uuid


def set_semantic_documents_ready_for_export(
    semantic_documents: QuerySet[SemanticDocument],
) -> List[UUID]:
    """
    @param semantic_documents:
    @return:
    """

    # Delete any semantic_documents that contain 0 pages at the beginning, as worker can't handle empty documents
    # It also makes not sense to send empty documents
    semantic_documents.filter(semantic_pages__isnull=True).distinct().delete()

    semdoc_uuids = (
        semantic_documents.filter(semantic_pages__isnull=False)
        .values_list("uuid", flat=True)
        .distinct()
    )

    # Prefetch related semantic_documents for each dossier
    dossier_uuids = (
        semantic_documents.filter(semantic_pages__isnull=False)
        .values_list("dossier", flat=True)
        .distinct()
    )
    unique_dossiers = Dossier.objects.filter(uuid__in=dossier_uuids).prefetch_related(
        Prefetch("semantic_documents", queryset=semantic_documents)
    )

    # If there are only empty documents nothing gets exported and the list will be empty
    request_uuids = []

    # We need to iterate over dossiers and their associated semantic_documents, as prepare_semantic_dossier
    # depends on the dossier
    for dossier in unique_dossiers:
        semdocs = dossier.semantic_documents

        if semdocs.count() == 0:
            # Pass if dossier has no semantic documents as this will break worker
            continue

        work_status = semdocs.first().work_status

        account: Account = dossier.account

        front_office_state, ready_for_export_state = (
            check_transition_semantic_document_to_ready_for_export(
                account=account, semantic_document_work_status=work_status
            )
        )

        # Computationally expensive, which is why we moved it out
        semantic_dossier: SemanticDossier = prepare_semantic_dossier(
            dossier=dossier,
            include_annotations=True,
            hide_empty_semantic_documents=True,
            enable_page_objects=False,
        )

        # PG: if we want to be more efficient, we could update semantic documents in bulk
        #     semantic_documents.bulk_update(
        #         work_status=ready_for_export_state, access_mode="read_only"
        #     )
        # We could also bulk save created DossierFile
        # Although that would mean that we risk having lots of semantic documents in a wrong state

        for semantic_document in semdocs.all():

            if semantic_document.uuid in semdoc_uuids:
                request = update_semantic_document_and_trigger_export(
                    dossier=dossier,
                    semantic_document=semantic_document,
                    front_office_state=front_office_state,
                    ready_for_export_state=ready_for_export_state,
                    semantic_dossier=semantic_dossier,
                )

                request_uuids.append(request.semantic_document_pdf_request_uuid)

    return request_uuids


def set_dossier_semantic_documents_state_ready_for_export(
    dossier: Dossier,
) -> List[UUID]:
    """
    Trigger export for all not-yet-exported semantic documents.

    This deletes empty non-deleted documents without warning as it does not make sense to export them and we want
    a "fully exported" dossier after this call.

    @param dossier:
    @return: List of UUIDs of the semantic document export requests
    """

    semantic_documents = SemanticDocument.objects.filter(
        work_status__key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        work_status__state_machine=dossier.account.active_semantic_document_work_status_state_machine,
        dossier=dossier,
    )

    # No exception in case a dossier is empty
    # if semantic_documents.count() == 0:
    #     raise Http404("No semantic documents in IN_FRONT_OFFICE state")

    return set_semantic_documents_ready_for_export(
        semantic_documents=semantic_documents
    )


def save_page_streams_to_new_semantic_documents(
    semantic_document: SemanticDocument,
    dossier: Dossier,
    page_streams: List[PageStreamForSplit],
):
    """
    Splits a SemanticDocument into multiple new SemanticDocuments:
        - new SemanticDocuments inherit field values from original doc
        - old SemanticPage entities point to new SemanticDocument entities
        - old SemanticDocumentPageObjects point to new SemanticDocuments based on the
        SemanticPages the PageObjects share with the new SemanticDocument
        - old DocCheckAssignments are deleted
        - new SemanticDocuments get an entry in AssignedRealEstatePropertySemanticDocument for each entry in
        AssignedRealestatePropertyOriginalFile belonging to the OriginalFile of their first SemanticPage, and assuming
        their DocumentCategory id is 6XX_
    """
    with transaction.atomic():
        # Begin checks
        # Check that all provided document categories exist for this account
        document_categories_names = {
            page_stream.document_category for page_stream in page_streams
        }
        count = DocumentCategory.objects.filter(
            name__in=document_categories_names, account=dossier.account
        ).count()
        if count != len(document_categories_names):
            raise ValidationError(
                "The provided document categories do not match those available for this account."
            )

        all_pages = [page for page_stream in page_streams for page in page_stream.pages]

        # check that all pages actually belong to the dossier and original semantic document
        pages_uuids = [page.uuid for page in all_pages]
        qs_semantic_pages = SemanticPage.objects.filter(
            uuid__in=pages_uuids, dossier=dossier, semantic_document=semantic_document
        )
        if qs_semantic_pages.count() != len(pages_uuids):
            raise ValidationError(
                "One or more pages do not belong to the dossier or semantic document."
            )

        # Check that no page has been updated in the meantime
        sematic_pages_with_annotation = qs_semantic_pages.annotate(
            truncated_updated_at=TruncSecond("updated_at")
        )
        for page in all_pages:
            # Django stores microseconds in db, Ninja (specifically the JSONRenderer which uses json.dumps) only serializes up to milliseconds, so the value coming back from the frontend does not match what is in the db
            if sematic_pages_with_annotation.get(
                uuid=page.uuid
            ).truncated_updated_at > page.updated_at.replace(microsecond=0):
                raise ValidationError("A page has been updated in the meantime")
        # End checks

        # Create new semantic document for each page stream
        for page_stream in page_streams:
            new_semantic_doc = SemanticDocument.objects.create(
                dossier=dossier,
                document_category=DocumentCategory.objects.get(
                    name=page_stream.document_category, account=dossier.account
                ),
                confidence_level=ConfidenceLevel.HIGH,
                confidence_formatted="100%",
                confidence_value=100,
                work_status=semantic_document.work_status,
                last_page_change_date=timezone.now(),
                last_entity_change_date=timezone.now(),
            )

            if page_stream.deleted:
                new_semantic_doc.delete()

            for i, page in enumerate(page_stream.pages):
                # Re-assign semantic pages in page stream to new semantic doc and update relations
                semantic_page = SemanticPage.objects.get(uuid=page.uuid)
                semantic_page.semantic_document = new_semantic_doc
                semantic_page.number = i
                semantic_page.save()

                # Re-assign semantic document page objects to new semantic docs based on the pages they contain
                # In principle this can miss page objects since the sdpos are de-duplicated so only one page of the document containing a given po
                # will have an entry in SemanticDocumentPageObject. We decided to leave it like this for now as the aggregation will probably be changed to always
                # happen on the fly in the future.
                SemanticDocumentPageObject.objects.filter(
                    semantic_document=semantic_document,
                    page_object__in=[
                        sppo.page_object
                        for sppo in semantic_page.semantic_page_page_objects.all()
                    ],
                ).update(semantic_document=new_semantic_doc)

            first_semantic_page = new_semantic_doc.semantic_pages.first()
            processed_page = (
                first_semantic_page.processed_page if first_semantic_page else None
            )
            if (
                is_document_topic_property(new_semantic_doc.document_category)
                and processed_page
            ):
                original_file = (
                    processed_page.processed_file.extracted_file.original_file
                )
                realestate_property = RealestateProperty.objects.filter(
                    assignedrealestatepropertyoriginalfile__originalfile=original_file
                ).first()
                if realestate_property:
                    AssignedRealEstatePropertySemanticDocument.objects.create(
                        semantic_document=new_semantic_doc,
                        realestate_property=realestate_property,
                    )

    assigned_real_estate_properties = (
        AssignedRealEstatePropertySemanticDocument.objects.filter(
            semantic_document=semantic_document
        ).all()
    )
    # needed since we soft-delete semantic_document and there is no cascade deletion
    assigned_real_estate_properties.delete()
    semantic_document.doccheckassignment_set.clear()

    semantic_document.delete()


def reset_semantic_document_work_status(semantic_document: SemanticDocument) -> bool:
    """
    Reset the work status of a semantic document to the start status of the state machine.
    Also deletes stale SemanticDocumentExports and  sets semdoc to READ_WRITE.
    @param semantic_document: Document for which a) to delete all exports and b) reset work state to inital state
    and c) change access mode back to read_write
    @return: True if work status of semantic document was changed else False
    """

    active_semantic_document_work_status_state_machine = (
        semantic_document.dossier.account.active_semantic_document_work_status_state_machine
    )

    ret = False
    if active_semantic_document_work_status_state_machine:
        with transaction.atomic():
            SemanticDocumentExport.objects.filter(
                semantic_document=semantic_document
            ).delete()

            state_change_needed = (
                semantic_document.work_status
                != active_semantic_document_work_status_state_machine.start_status
            )
            rw_change_needed = (
                semantic_document.access_mode
                != SemanticDocument.SemanticDocumentAccessMode.READ_WRITE
            )
            if state_change_needed or rw_change_needed:
                semantic_document.work_status = (
                    active_semantic_document_work_status_state_machine.start_status
                )
                semantic_document.access_mode = (
                    SemanticDocument.SemanticDocumentAccessMode.READ_WRITE
                )

                semantic_document.save()
                ret = True

    return ret


def reset_semantic_document_export(semdocs: QuerySet[SemanticDocument]):
    """
    Set status of semantic document back to initial status
    """
    semdocs_changed = []
    semdocs_unchanged = []
    initial_work_status = None
    for semdoc in semdocs:
        reset = reset_semantic_document_work_status(semdoc)

        if reset:
            semdocs_changed.append(semdoc)
        else:
            semdocs_unchanged.append(semdoc)
        if not initial_work_status:
            state_machine = (
                semdoc.dossier.account.active_semantic_document_work_status_state_machine
            )
            if state_machine:
                if state_machine.start_status:
                    initial_work_status = state_machine.start_status.key

    return semdocs_changed, semdocs_unchanged, initial_work_status


@transaction.atomic
def force_semantic_document_done(semantic_documents_qs: QuerySet[SemanticDocument]):
    """
    For semdocs with an existing semdoc export:
    - update done timestamp to now
    For semdocs without an existing export:
    - create new semdoc export and set done timestamp to now
    For all semdocs:
    - update semdoc work status
    - update semdoc read-only

    @param semantic_documents_qs:
    @return:
    """
    num_semdoc_exports_set_done = SemanticDocumentExport.objects.filter(
        semantic_document__in=semantic_documents_qs,
        done__isnull=True,
    ).update(done=timezone.now())

    semdoc_state_machine: StateMachine = (
        semantic_documents_qs.first().dossier.account.active_semantic_document_work_status_state_machine
    )

    num_semdocs_work_state_changed = 0

    if semdoc_state_machine:

        # export_available_state = Status.objects.get(
        #     key=SemanticDocumentState.EXPORT_AVAILABLE.value,
        #     state_machine=semdoc_state_machine,
        # )

        export_done_state = Status.objects.get(
            key=SemanticDocumentState.EXPORT_DONE.value,
            state_machine=semdoc_state_machine,
        )
        read_only = SemanticDocument.SemanticDocumentAccessMode.READ_ONLY
        for semdoc in semantic_documents_qs:
            if (
                semdoc.work_status != export_done_state
                or semdoc.access_mode != read_only
            ):
                semdoc.work_status = export_done_state
                semdoc.access_mode = read_only
                semdoc.save()
                num_semdocs_work_state_changed += 1

    documents_without_exports = semantic_documents_qs.filter(exports__isnull=True)

    num_new_semdoc_exports_created = 0
    for doc_without_exports in documents_without_exports:
        SemanticDocumentExport.objects.create(
            semantic_document=doc_without_exports, done=timezone.now()
        )
        num_new_semdoc_exports_created += 1

    return (
        num_semdoc_exports_set_done,
        num_new_semdoc_exports_created,
        num_semdocs_work_state_changed,
    )


def create_semantic_document_pdf_filename(
    semantic_document_title: str,
    semantic_document_uuid: UUID = None,
    add_uuid_suffix: bool = False,
) -> str:
    """
    ### TODO CONSOLIDATE_FILENAME -
    # This is the function that should be used everywhere to generate filenames
    # from semantic documents


    Create a sanitized PDF filename from the semantic document name and optionally a document UUID.
    Use this function also in the worker to create the filename
    Special characters of French and German like äöü èê must survive

    Args:
        semantic_document_title: Name of the semantic document without extension
        semantic_document_uuid: Optional UUID to append to filename

    Returns:
        Sanitized filename string with .pdf extension
        :param semantic_document_uuid:
        :param semantic_document_title:
        :param add_uuid_suffix:
    """
    sanitized_name = remove_invalid_chars(semantic_document_title)

    if semantic_document_uuid and add_uuid_suffix:
        return f"{sanitized_name}_{semantic_document_uuid}.pdf"
    return f"{sanitized_name}.pdf"


def assert_semantic_document_is_writable(semantic_document: SemanticDocument) -> None:
    """
    Checks if a semantic document is writable (not read-only). Raises HttpError if the document is read-only.

    Args:
        semantic_document: The semantic document to check

    Raises:
        HttpError: If the semantic document is read-only
    """
    if (
        semantic_document.access_mode
        == SemanticDocument.SemanticDocumentAccessMode.READ_ONLY
    ):
        raise HttpError(
            403, "This semantic document is read-only and cannot be modified"
        )


def assert_semantic_documents_are_writable(
    semantic_documents: Union[List[SemanticDocument], QuerySet[SemanticDocument]],
) -> None:
    """
    Checks if all semantic documents are writable (not read-only). Raises HttpError if any document is read-only.

    Args:
        semantic_documents: List or QuerySet of semantic documents to check

    Raises:
        HttpError: If any semantic document is read-only
    """
    for semantic_document in semantic_documents:
        assert_semantic_document_is_writable(semantic_document)
