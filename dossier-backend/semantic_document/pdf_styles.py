"""
PDF styles for use with ReportLab in the semantic document module.

This module provides reusable ParagraphStyle configurations for consistent
styling across PDF generation in the application.
"""

from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet


def apply_hypodossier_style_defaults(style_params):
    """
    Apply HypoDossier default style parameters to a dictionary of style parameters.

    Args:
        style_params: Dictionary of style parameters

    Returns:
        Dictionary with default values applied
    """
    # Make a copy to avoid modifying the original
    params = style_params.copy()

    # Set default values if not provided
    if "wordWrap" not in params:
        params["wordWrap"] = "CJK"  # Forces wrap on any character if needed
    if "allowOrphans" not in params:
        params["allowOrphans"] = 0
    if "allowWidows" not in params:
        params["allowWidows"] = 0
    if "spaceAfter" not in params:
        params["spaceAfter"] = 0
    if "spaceBefore" not in params:
        params["spaceBefore"] = 0

    # Set leading based on fontSize if not provided
    if "leading" not in params and "fontSize" in params:
        params["leading"] = params["fontSize"] * 1.2

    return params


def get_base_style():
    """
    Get the base style for HypoDossier documents.

    Returns:
        ParagraphStyle: Base style derived from ReportLab's Normal style
    """
    styles = getSampleStyleSheet()
    return styles["Normal"]


def create_test_style(base_style=None, font_size=None, **kwargs):
    """
    Create a test style with the given parameters.

    Args:
        base_style: Base style to inherit from (default: Normal style)
        font_size: Font size in points (default: base_style's font size)
        **kwargs: Additional style attributes

    Returns:
        ParagraphStyle: A configured paragraph style
    """
    if base_style is None:
        base_style = get_base_style()

    if font_size is None:
        font_size = base_style.fontSize

    # Start with base parameters
    style_params = {"fontSize": font_size, **kwargs}

    # Apply HypoDossier defaults
    style_params = apply_hypodossier_style_defaults(style_params)

    return ParagraphStyle("test_style", parent=base_style, **style_params)


def create_optimal_style(base_style=None, font_size=None, **kwargs):
    """
    Create an optimal style for text that needs to fit in a specific area.

    Args:
        base_style: Base style to inherit from (default: Normal style)
        font_size: Font size in points (default: base_style's font size)
        **kwargs: Additional style attributes

    Returns:
        ParagraphStyle: A configured paragraph style
    """
    if base_style is None:
        base_style = get_base_style()

    if font_size is None:
        font_size = base_style.fontSize

    # Start with base parameters
    style_params = {"fontSize": font_size, **kwargs}

    # Apply HypoDossier defaults
    style_params = apply_hypodossier_style_defaults(style_params)

    return ParagraphStyle("optimal_style", parent=base_style, **style_params)


def create_comment_style(base_style=None, font_size=10, **kwargs):
    """
    Create a style specifically for comment annotations.

    Args:
        base_style: Base style to inherit from (default: Normal style)
        font_size: Font size in points (default: 10)
        **kwargs: Additional style attributes

    Returns:
        ParagraphStyle: A configured paragraph style for comments
    """
    if base_style is None:
        base_style = get_base_style()

    # Start with base parameters
    style_params = {
        "fontSize": font_size,
        "textColor": (0, 0, 0),  # Black text
        "fontName": "Helvetica",  # Use Helvetica as it's widely supported in PDFs
        **kwargs,
    }

    # Apply HypoDossier defaults
    style_params = apply_hypodossier_style_defaults(style_params)

    return ParagraphStyle("comment_style", parent=base_style, **style_params)
