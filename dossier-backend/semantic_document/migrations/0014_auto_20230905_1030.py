# Generated by Django 3.2.20 on 2023-09-05 08:30

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0059_auto_20230905_1030'),
        ('semantic_document', '0013_doccheckassignment'),
    ]

    operations = [
        migrations.CreateModel(
            name='AssignedRealEstatePropertySemanticDocument',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('realestate_property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.realestateproperty')),
                ('semantic_document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='semantic_document.semanticdocument')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='semanticdocument',
            name='realestate_property',
            field=models.ManyToManyField(through='semantic_document.AssignedRealEstatePropertySemanticDocument', to='dossier.RealestateProperty'),
        ),
    ]
