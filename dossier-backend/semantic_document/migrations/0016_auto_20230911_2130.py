# Generated by Django 3.2.21 on 2023-09-11 19:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0059_auto_20230905_1030'),
        ('semantic_document', '0015_doccheckassignment_fulfillment_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='semanticdocument',
            name='access_mode',
            field=models.CharField(choices=[('read_write', 'read_write'), ('read_only', 'read_only')], default='read_write', max_length=20),
        ),
        migrations.AddField(
            model_name='semanticdocument',
            name='external_semantic_document_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='semanticdocument',
            name='last_entity_change_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='semanticdocument',
            name='last_page_change_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterUniqueTogether(
            name='semanticdocument',
            unique_together={('dossier', 'external_semantic_document_id')},
        ),
    ]
