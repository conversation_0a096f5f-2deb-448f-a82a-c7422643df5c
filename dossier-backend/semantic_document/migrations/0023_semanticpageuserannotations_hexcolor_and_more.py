# Generated by Django 4.2.20 on 2025-04-15 22:21

import colorfield.fields
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("semantic_document", "0022_semanticpageuserannotations"),
    ]

    operations = [
        migrations.AddField(
            model_name="semanticpageuserannotations",
            name="hexcolor",
            field=colorfield.fields.ColorField(
                default="#FFFF00", image_field=None, max_length=25, samples=None
            ),
        ),
        migrations.AlterField(
            model_name="semanticpageuserannotations",
            name="bbox_height",
            field=models.FloatField(
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(1.0),
                ]
            ),
        ),
        migrations.AlterField(
            model_name="semanticpageuserannotations",
            name="bbox_left",
            field=models.FloatField(
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(1.0),
                ]
            ),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name="semanticpageuserannotations",
            name="bbox_top",
            field=models.FloatField(
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(1.0),
                ]
            ),
        ),
        migrations.AlterField(
            model_name="semanticpageuserannotations",
            name="bbox_width",
            field=models.FloatField(
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(1.0),
                ]
            ),
        ),
    ]
