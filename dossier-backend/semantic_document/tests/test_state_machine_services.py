import pytest
from django.db.models import QuerySet
from pytest_mock import Mo<PERSON><PERSON>ix<PERSON>

from dossier.exceptions import HttpError
from dossier.fakes import (
    add_some_fake_semantic_documents,
    load_initial_document_categories,
)
from dossier.management.commands.dossier_event_consumer_v2 import (
    set_semantic_document_export_done,
)
from dossier.models import Account, Dossier
from events.models import Event
from semantic_document.models import SemanticDocument
from semantic_document.services import (
    set_semantic_document_ready_for_export,
    force_semantic_document_done,
    set_semantic_documents_ready_for_export,
    reset_semantic_document_work_status,
)
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
    SemanticDocumentState,
    STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
)
from statemgmt.models import StateMachine, Status
from workers import schemas as worker_schemas
from workers.models import SemanticDocumentExport
from workers.workers import process_semantic_dossier_pdf_request

pytestmark = pytest.mark.django_db


@pytest.fixture
def prepare_data():
    account = Account.objects.create(key="test", name="Test")
    dossier = Dossier.objects.create(name="TestDossier", account=account)

    load_initial_document_categories(account=dossier.account)

    # Ensure that a state machine is created
    created_objects = create_semantic_document_state_machine()

    state_machine = created_objects["machine"]

    # Associate state machine with account
    account.active_semantic_document_work_status_state_machine = state_machine
    account.save()
    account.refresh_from_db()

    # Check that we have a state machine loaded
    assert StateMachine.objects.get(name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS)

    # Create a semantic document
    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier,
        num_docs=1,
        min_num_pages=1,
        max_pages=1,
        allow_empty_docs=False,
    )

    # Pick a first one
    semantic_document: SemanticDocument = semantic_documents[0]

    return dossier, semantic_document, account


def test_set_semantic_document_ready_for_export_success(
    prepare_data,
    mocker: MockerFixture,
):
    dossier, semantic_document, account = prepare_data

    # Check that we have a state machine enabled
    assert (
        account.active_semantic_document_work_status_state_machine
        == StateMachine.objects.get(name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS)
    )

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document: SemanticDocument = semantic_documents[0]

    ready_for_export_state = Status.objects.get(
        key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        state_machine__name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    )

    assert semantic_document.work_status == ready_for_export_state

    def mock_publish_side_effect(*args, **kwargs) -> str:
        request = worker_schemas.SemanticDocumentPDFRequestV1.model_validate_json(
            kwargs["message"]
        )

        # Returns json dump in format SemanticDocumentPDFResponseV1
        return process_semantic_dossier_pdf_request(
            semantic_document_pdf_request=request
        )

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect,
    )

    # Request to generate an export

    response_uuid = set_semantic_document_ready_for_export(semantic_document)

    assert response_uuid
    mock_dispatch_publish_request.assert_called_once()

    semantic_document_export = SemanticDocumentExport.objects.get()

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    # We need to unset this, otherwise teardown fails for unrelated dossier.work_status
    semantic_document.work_status = None
    semantic_document.save()


def test_set_semantic_document_ready_for_export_empty_doc(
    prepare_data,
):
    dossier, semantic_document, account = prepare_data

    Status.objects.get(
        key=SemanticDocumentState.READY_FOR_EXPORT.value,
        state_machine__name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    )

    for sempage in semantic_document.semantic_pages.all():
        sempage.delete()

    state_machine = dossier.account.active_semantic_document_work_status_state_machine
    assert semantic_document.work_status == state_machine.start_status
    assert semantic_document.semantic_pages.count() == 0

    assert set_semantic_document_ready_for_export(semantic_document) is None


def test_set_semantic_document_ready_for_export_failure(
    prepare_data,
):
    dossier, semantic_document, account = prepare_data

    ready_for_export_state = Status.objects.get(
        key=SemanticDocumentState.READY_FOR_EXPORT.value,
        state_machine__name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    )

    semantic_document.work_status = ready_for_export_state

    semantic_document.save()

    with pytest.raises(HttpError, match="is not in the correct state to be exported"):
        set_semantic_document_ready_for_export(semantic_document)

    account = semantic_document.dossier.account
    account.active_semantic_document_work_status_state_machine = None
    account.save()

    with pytest.raises(
        HttpError, match="does not have semantic document export state machine set"
    ):
        set_semantic_document_ready_for_export(semantic_document)

    # We need to unset this, otherwise teardown fails for unrelated dossier.work_status
    semantic_document.work_status = None
    semantic_document.save()


def test_semantic_document_work_status_change_event(prepare_data):

    dossier, semantic_document, account = prepare_data

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier, num_docs=1)

    # Pick a first one
    semantic_document: SemanticDocument = semantic_documents[0]

    assert semantic_document.work_status == Status.objects.get(
        key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        state_machine__name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    )

    Event.objects.all().delete()

    semantic_document.work_status = Status.objects.get(
        key=SemanticDocumentState.READY_FOR_EXPORT.value,
        state_machine__name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    )
    semantic_document.save()

    list(Event.objects.all())

    assert (
        Event.objects.filter(
            type="semantic_document.schemas.SemanticDocumentWorkStatusChangedEvent"
        ).count()
        == 1
    )


def test_force_semantic_document_done(prepare_data, mocker: MockerFixture):
    # This creates a dossier with already a single semantic doc inside
    dossier, semantic_document, account = prepare_data

    # Make sure we can export unknown documents because some
    account.enable_semantic_document_export_unknown_documents = True
    account.save()

    # Add some more semdocs and asset they all have the inital work status
    num_semdocs_added = 4
    semdocs_created = add_some_fake_semantic_documents(
        dossier=dossier, num_docs=num_semdocs_added, allow_empty_docs=False
    )
    assert len(semdocs_created) == num_semdocs_added
    semantic_documents: QuerySet[SemanticDocument] = SemanticDocument.objects.filter(
        dossier=dossier
    ).all()
    num_semdocs = 1 + num_semdocs_added
    assert semantic_documents.count() == num_semdocs
    for semantic_document in semantic_documents:
        # Work status should have already been set by create semantic document function
        assert semantic_document.work_status == Status.objects.get(
            key=SemanticDocumentState.IN_FRONT_OFFICE.value,
            state_machine=dossier.account.active_semantic_document_work_status_state_machine,
        )

    def mock_publish_side_effect(*args, **kwargs):
        request = worker_schemas.SemanticDocumentPDFRequestV1.model_validate_json(
            kwargs["message"]
        )

        # Process the pdf generation
        # Returns json dump in format SemanticDocumentPDFResponseV1
        process_semantic_document_response = process_semantic_dossier_pdf_request(
            semantic_document_pdf_request=request
        )

        # which is then collected by dossier events consumer
        # and sets event as done
        set_semantic_document_export_done(process_semantic_document_response)

    mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect,
    )

    # Step 1: set all semdocs to ready for export, due to mocking this sets all exports directly to 'done'
    # then force done on exports
    export_request_uuids = set_semantic_documents_ready_for_export(
        semantic_documents=semantic_documents.all()
    )
    assert semantic_documents.count() == num_semdocs
    assert len(export_request_uuids) == num_semdocs

    semdoc_exports_created = list(
        SemanticDocumentExport.objects.filter(semantic_document__in=semantic_documents)
    )
    assert len(semdoc_exports_created) == num_semdocs

    (
        num_semdoc_exports_set_done,
        num_new_semdoc_exports_created,
        num_semdocs_work_state_changed,
    ) = force_semantic_document_done(semantic_documents_qs=semantic_documents.all())

    # No changes should have been made - force does nothing on the export
    # because mocking has already set all exports to 'done'
    assert num_semdoc_exports_set_done == 0
    assert num_new_semdoc_exports_created == 0
    assert (
        num_semdocs_work_state_changed == 5
    )  # State of semdoc is changed for all documents

    # Step 2: Remove one semantic export
    semantic_documents.first().exports.first().delete()
    (
        num_semdoc_exports_set_done,
        num_new_semdoc_exports_created,
        num_semdocs_work_state_changed,
    ) = force_semantic_document_done(semantic_documents_qs=semantic_documents.all())

    # One export should have been created
    # No work status is updated because all of them already set to EXPORT_DONE
    assert num_semdoc_exports_set_done == 0
    assert num_semdocs_work_state_changed == 0
    assert num_new_semdoc_exports_created == 1

    # Step 3: Reset the workstate of a semantic document and delete its export, then force export again
    semdoc_has_changed = reset_semantic_document_work_status(semantic_documents.first())
    assert semdoc_has_changed

    (
        num_semdoc_exports_set_done,
        num_new_semdoc_exports_created,
        num_semdocs_work_state_changed,
    ) = force_semantic_document_done(semantic_documents_qs=semantic_documents.all())
    assert num_semdoc_exports_set_done == 0
    assert num_new_semdoc_exports_created == 1
    assert num_semdocs_work_state_changed == 1  # set to EXPORT_DONE again

    # Step 4: Remove done from a single export, but state of all semdocs is still EXPORT_DONE so nothing to do there
    export = semantic_documents.first().exports.first()
    export.done = None
    export.save()

    (
        num_semdoc_exports_set_done,
        num_new_semdoc_exports_created,
        num_semdocs_work_state_changed,
    ) = force_semantic_document_done(semantic_documents_qs=semantic_documents.all())
    assert num_new_semdoc_exports_created == 0
    assert num_semdocs_work_state_changed == 0
    assert num_semdoc_exports_set_done == 1
