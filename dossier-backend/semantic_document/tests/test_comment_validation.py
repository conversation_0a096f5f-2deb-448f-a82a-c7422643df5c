import pytest
import uuid
from django.urls import reverse
from pydantic import ValidationError

from dossier.models import <PERSON><PERSON><PERSON>
from semantic_document.models import SemanticPageUserAnnotations
from semantic_document.schemas_page_annotations import (
    UserAnnotationsSchema,
    AnnotationType,
    UpdateUserAnnotationSchema,
)


def test_create_empty_comment_validation():
    """Test that creating a comment with empty text raises a validation error"""
    # Try to create a comment annotation with empty text
    with pytest.raises(ValidationError) as exc_info:
        UserAnnotationsSchema(
            annotation_type=AnnotationType.COMMENT,
            text=None,  # Empty text should fail validation
            bbox_left=0.1,
            bbox_top=0.2,
            bbox_width=0.3,
            bbox_height=0.15,
        )

    # Check that the error message is correct
    assert "Comment text cannot be empty" in str(exc_info.value)

    # Try with empty string
    with pytest.raises(ValidationError) as exc_info:
        UserAnnotationsSchema(
            annotation_type=AnnotationType.COMMENT,
            text="",  # Empty string should fail validation
            bbox_left=0.1,
            bbox_top=0.2,
            bbox_width=0.3,
            bbox_height=0.15,
        )

    assert "Comment text cannot be empty" in str(exc_info.value)

    # Try with whitespace-only string
    with pytest.raises(ValidationError) as exc_info:
        UserAnnotationsSchema(
            annotation_type=AnnotationType.COMMENT,
            text="   ",  # Whitespace-only string should fail validation
            bbox_left=0.1,
            bbox_top=0.2,
            bbox_width=0.3,
            bbox_height=0.15,
        )

    assert "Comment text cannot be empty" in str(exc_info.value)


def test_highlight_empty_text_allowed():
    """Test that highlights can have empty text"""
    # Highlights should allow empty text
    annotation = UserAnnotationsSchema(
        annotation_type=AnnotationType.HIGHLIGHT,
        text=None,  # Empty text should be allowed for highlights
        bbox_left=0.1,
        bbox_top=0.2,
        bbox_width=0.3,
        bbox_height=0.15,
    )

    assert annotation.text is None

    # Empty string should also be allowed for highlights
    annotation = UserAnnotationsSchema(
        annotation_type=AnnotationType.HIGHLIGHT,
        text="",
        bbox_left=0.1,
        bbox_top=0.2,
        bbox_width=0.3,
        bbox_height=0.15,
    )

    assert annotation.text == ""


def test_update_empty_comment_validation():
    """Test that updating a comment with empty text raises a validation error"""
    # Try to update a comment with empty text
    with pytest.raises(ValidationError) as exc_info:
        UpdateUserAnnotationSchema(text="")

    assert "Comment text cannot be empty" in str(exc_info.value)

    # Try with whitespace-only string
    with pytest.raises(ValidationError) as exc_info:
        UpdateUserAnnotationSchema(text="   ")

    assert "Comment text cannot be empty" in str(exc_info.value)

    # None should be allowed (means no update to text)
    update_data = UpdateUserAnnotationSchema(text=None)
    assert update_data.text is None


@pytest.mark.django_db
def test_create_empty_comment_api(testuser1_client):
    """Test that the API rejects creating a comment with empty text"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Try to create a comment with empty text
    annotation_data = [
        {
            "annotation_type": "comment",
            "text": "",  # Empty text should be rejected
            "bbox_left": 0.1,
            "bbox_top": 0.2,
            "bbox_width": 0.3,
            "bbox_height": 0.15,
        }
    ]

    response = testuser1_client.post(
        reverse(
            "api:semantic-page-user-annotations-create",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        ),
        data=annotation_data,
        content_type="application/json",
    )

    # Should return a validation error
    assert response.status_code == 422
    assert "Comment text cannot be empty" in response.content.decode()


@pytest.mark.django_db
def test_update_empty_comment_api(testuser1_client):
    """Test that the API rejects updating a comment with empty text"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create a valid comment first
    annotation_group_uuid = uuid.uuid4()
    SemanticPageUserAnnotations.objects.create(
        semantic_page=semantic_page,
        annotation_type="comment",
        annotation_group_uuid=annotation_group_uuid,
        text="Initial comment text",
        bbox_left=0.1,
        bbox_top=0.2,
        bbox_width=0.3,
        bbox_height=0.15,
    )

    # Try to update with empty text
    update_data = {"text": ""}

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    # Should return a validation error
    assert response.status_code == 422
    assert "Comment text cannot be empty" in response.content.decode()

    # Try with whitespace-only string
    update_data = {"text": "   "}

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    # Should return a validation error
    assert response.status_code == 422
    assert "Comment text cannot be empty" in response.content.decode()
