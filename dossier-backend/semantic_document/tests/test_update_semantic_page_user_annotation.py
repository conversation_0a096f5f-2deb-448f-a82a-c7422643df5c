import uuid
import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.urls import reverse
from django.test.client import Client

from dossier.models import Do<PERSON><PERSON>, Account, DocumentCategory
from dossier.fakes import add_some_fake_semantic_documents
from semantic_document.models import SemanticPageUserAnnotations

pytestmark = pytest.mark.django_db

User: AbstractUser = get_user_model()


def create_test_annotation(semantic_page, annotation_type="comment"):
    """Helper function to create a test annotation for a semantic page"""
    annotation_group_uuid = uuid.uuid4()

    annotation = SemanticPageUserAnnotations.objects.create(
        semantic_page=semantic_page,
        annotation_type=annotation_type,
        annotation_group_uuid=annotation_group_uuid,
        text="Test comment",
        bbox_left=10.0,
        bbox_top=20.0,
        bbox_width=100.0,
        bbox_height=50.0,
    )

    return annotation_group_uuid, annotation


def test_update_user_annotation_text_only(testuser1_client):
    """Test updating only the text of a comment annotation"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create test annotation
    annotation_group_uuid, _ = create_test_annotation(semantic_page)

    # Update only the text
    update_data = {"text": "Updated comment text"}

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    assert response.status_code == 200
    data = response.json()
    assert data["annotation_group_uuid"] == str(annotation_group_uuid)

    # Verify annotation was updated
    updated_annotation = SemanticPageUserAnnotations.objects.get(
        semantic_page=semantic_page, annotation_group_uuid=annotation_group_uuid
    )
    assert updated_annotation.text == "Updated comment text"
    # Verify bbox fields were not changed
    assert updated_annotation.bbox_left == 10.0
    assert updated_annotation.bbox_top == 20.0
    assert updated_annotation.bbox_width == 100.0
    assert updated_annotation.bbox_height == 50.0


def test_update_user_annotation_bbox_only(testuser1_client):
    """Test updating only the bbox fields of a comment annotation"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create test annotation
    annotation_group_uuid, _ = create_test_annotation(semantic_page)

    # Update only the bbox fields
    update_data = {
        "bbox_left": 15.0,
        "bbox_top": 25.0,
        "bbox_width": 90.0,
        "bbox_height": 45.0,
    }

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    assert response.status_code == 200
    data = response.json()
    assert data["annotation_group_uuid"] == str(annotation_group_uuid)

    # Verify annotation was updated
    updated_annotation = SemanticPageUserAnnotations.objects.get(
        semantic_page=semantic_page, annotation_group_uuid=annotation_group_uuid
    )
    assert updated_annotation.text == "Test comment"  # Text should not change
    # Verify bbox fields were changed
    assert updated_annotation.bbox_left == 15.0
    assert updated_annotation.bbox_top == 25.0
    assert updated_annotation.bbox_width == 90.0
    assert updated_annotation.bbox_height == 45.0


def test_update_user_annotation_text_and_bbox(testuser1_client):
    """Test updating both text and bbox fields of a comment annotation"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create test annotation
    annotation_group_uuid, _ = create_test_annotation(semantic_page)

    # Update both text and bbox fields
    update_data = {
        "text": "Updated text and position",
        "bbox_left": 15.0,
        "bbox_top": 25.0,
        "bbox_width": 90.0,
        "bbox_height": 45.0,
    }

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    assert response.status_code == 200
    data = response.json()
    assert data["annotation_group_uuid"] == str(annotation_group_uuid)

    # Verify annotation was updated
    updated_annotation = SemanticPageUserAnnotations.objects.get(
        semantic_page=semantic_page, annotation_group_uuid=annotation_group_uuid
    )
    assert updated_annotation.text == "Updated text and position"
    assert updated_annotation.bbox_left == 15.0
    assert updated_annotation.bbox_top == 25.0
    assert updated_annotation.bbox_width == 90.0
    assert updated_annotation.bbox_height == 45.0


def test_update_user_annotation_invalid_bbox(testuser1_client):
    """Test validation error when providing incomplete bbox fields"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create test annotation
    annotation_group_uuid, original_annotation = create_test_annotation(semantic_page)

    original_values = {
        "text": original_annotation.text,
        "bbox_left": original_annotation.bbox_left,
        "bbox_top": original_annotation.bbox_top,
        "bbox_width": original_annotation.bbox_width,
        "bbox_height": original_annotation.bbox_height,
    }

    # Try to update with incomplete bbox fields
    update_data = {
        "text": "This should not be updated",
        "bbox_left": 15.0,  # Only providing one bbox field should fail
    }

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    # Should return 400 Bad Request
    assert response.status_code == 400
    assert "All bbox fields must be provided together" in response.content.decode()

    # Verify annotation was not updated
    unchanged_annotation = SemanticPageUserAnnotations.objects.get(
        semantic_page=semantic_page, annotation_group_uuid=annotation_group_uuid
    )
    assert unchanged_annotation.text == original_values["text"]
    assert unchanged_annotation.bbox_left == original_values["bbox_left"]
    assert unchanged_annotation.bbox_top == original_values["bbox_top"]
    assert unchanged_annotation.bbox_width == original_values["bbox_width"]
    assert unchanged_annotation.bbox_height == original_values["bbox_height"]


def test_update_user_annotation_highlight_type(testuser1_client):
    """Test that only comments can be updated, not highlights"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create test highlight annotation (not comment)
    annotation_group_uuid, original_annotation = create_test_annotation(
        semantic_page, annotation_type="highlight"
    )

    original_text = original_annotation.text

    # Try to update a highlight annotation
    update_data = {"text": "This should not update a highlight"}

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    assert response.status_code == 404  # Not found expected for non-comment annotations

    # Verify annotation was not updated
    unchanged_annotation = SemanticPageUserAnnotations.objects.get(
        semantic_page=semantic_page, annotation_group_uuid=annotation_group_uuid
    )
    assert unchanged_annotation.text == original_text


def test_update_user_annotation_no_fields(testuser1_client):
    """Test that an error is returned when no fields are provided for update"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create test annotation
    annotation_group_uuid, _ = create_test_annotation(semantic_page)

    # Try to update with empty data
    update_data = {}

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    # Should return 400 Bad Request
    assert response.status_code == 400
    assert "No fields to update" in response.content.decode()


def test_update_user_annotation_none_fields(testuser1_client):
    """Test that an error is returned when fields are explicitly set to None"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create test annotation
    annotation_group_uuid, _ = create_test_annotation(semantic_page)

    # Try to update with all fields set to None
    update_data = {
        "text": None,
        "bbox_left": None,
        "bbox_top": None,
        "bbox_width": None,
        "bbox_height": None,
    }

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    # Should return 400 Bad Request
    assert response.status_code == 400
    assert "No fields to update" in response.content.decode()


def test_update_user_annotation_access_control(testuser1_client, testuser2_client):
    """Test access control for the update annotation endpoint"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create an annotation
    annotation_group_uuid, _ = create_test_annotation(semantic_page)

    # Test unauthorized access
    client = Client()
    update_data = {"text": "This should not work"}

    response = client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    assert response.status_code in [401, 404]

    # Test with a different user who doesn't have access
    response = testuser2_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    assert response.status_code == 404  # Should be not found for users without access


def test_update_annotation_synthetic_dossier(testuser1_client):
    """Test updating a comment annotation in a dynamically created dossier"""
    user = User.objects.get(username="<EMAIL>")

    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier for annotations",
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    sem_docs = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=1,
        min_num_pages=1,
    )

    semantic_page = sem_docs[0].semantic_pages.first()

    # Create test annotation
    annotation_group_uuid, _ = create_test_annotation(semantic_page)

    # Update the annotation
    update_data = {"text": "Updated in synthetic dossier"}

    response = testuser1_client.patch(
        reverse(
            "api:semantic-page-user-annotation-update",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}",
        data=update_data,
        content_type="application/json",
    )

    assert response.status_code == 200
    data = response.json()
    assert data["annotation_group_uuid"] == str(annotation_group_uuid)

    # Verify annotation was updated
    updated_annotation = SemanticPageUserAnnotations.objects.get(
        semantic_page=semantic_page, annotation_group_uuid=annotation_group_uuid
    )
    assert updated_annotation.text == "Updated in synthetic dossier"
