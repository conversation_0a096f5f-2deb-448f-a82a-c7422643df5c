from datetime import timed<PERSON><PERSON>
from pathlib import Path
from uuid import uuid4

import pytest
from django.core.exceptions import ValidationError
from django.urls import reverse

from django.test.client import Client

from conftest import synthetic_dossier
from dossier.fakes import (
    add_some_fake_semantic_documents,
)
from dossier.models import (
    DocumentCategory,
    RealestateProperty,
    AssignedRealestatePropertyOriginalFile,
)

from semantic_document.models import (
    SemanticPage,
    SemanticDocument,
    SemanticDocumentPageObject,
)
from semantic_document.schemas_pss import PageStreamForSplit
from semantic_document.services import save_page_streams_to_new_semantic_documents

pytestmark = pytest.mark.django_db


def test_get_page_streams_categories_suggestions(testuser1_client, testuser2_client):
    url = reverse("api:get-page-streams-categories-suggestions")
    request_json = (
        Path(__file__).parent / "data/page_stream_categories_suggestions.json"
    )
    # Unauthenticated user does not have permission
    unauthenticated_client = Client()
    response = unauthenticated_client.post(
        url, data=request_json.read_text(), content_type="application/json"
    )
    assert response.status_code == 401

    # Authenticated user without access to this dossier
    response = testuser2_client.post(
        url, data=request_json.read_text(), content_type="application/json"
    )
    assert response.status_code == 404

    # Authenticated user with access to this dossier
    response = testuser1_client.post(
        url, data=request_json.read_text(), content_type="application/json"
    )
    assert response.status_code == 200
    response = response.json()
    assert response[0]["suggested_document_categories"] == ["TAX_DECLARATION"]
    assert response[1]["suggested_document_categories"] == ["TAX_LIST_FINANCIAL_ASSETS"]


def test_save_page_streams_to_new_semantic_documents():
    def get_request_body(
        document_category_1,
        document_category_2,
        pages_stream_1,
        pages_stream_2,
        invalid_uuids=False,
        invalid_document_categories=False,
        updated_pages=False,
    ):
        request_body = [
            PageStreamForSplit(
                **{
                    "index": 0,
                    "deleted": False,
                    "document_category": (
                        document_category_1.name
                        if not invalid_document_categories
                        else "INVALID_TEST_DOC_CAT"
                    ),
                    "pages": [
                        {"uuid": page.uuid, "number": i, "updated_at": page.updated_at}
                        for i, page in enumerate(pages_stream_1)
                    ],
                }
            ),
            PageStreamForSplit(
                **{
                    "index": 1,
                    "deleted": False,
                    "document_category": document_category_2.name,
                    "pages": [
                        {
                            "uuid": page.uuid if not invalid_uuids else uuid4(),
                            "number": i,
                            "updated_at": (
                                page.updated_at
                                if not updated_pages
                                else page.updated_at - timedelta(minutes=1)
                            ),
                        }
                        for i, page in enumerate(pages_stream_2)
                    ],
                }
            ),
        ]
        return request_body

    NUM_PAGES = 9
    NUM_PAGE_OBJECTS_PER_PAGE = 20

    # Begin generating synthetic data
    dossier = synthetic_dossier()
    dossier.bucket = "dms-default-bucket"
    dossier.save()

    test_document_categories = [
        DocumentCategory(
            name=f"TEST_DOC_CAT_{i}", id=f"6XX_DOC_CAT_{i}", account=dossier.account
        )
        for i in range(3)
    ]
    document_category_0, document_category_1, document_category_2 = (
        DocumentCategory.objects.bulk_create(test_document_categories)
    )

    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier,
        num_docs=1,
        allow_empty_docs=False,
        min_num_pages=NUM_PAGES,
        max_pages=NUM_PAGES,
        no_page_objects_per_page=NUM_PAGE_OBJECTS_PER_PAGE,
    )
    semantic_document = semantic_documents[0]
    semantic_document.document_category = document_category_0
    semantic_document.save()

    sdpos = [
        SemanticDocumentPageObject(
            semantic_document=semantic_document, page_object=sppo.page_object
        )
        for semantic_page in semantic_document.semantic_pages.all()
        for sppo in semantic_page.semantic_page_page_objects.all()
    ]
    SemanticDocumentPageObject.objects.bulk_create(sdpos)
    assert (
        semantic_document.aggregated_page_objects.count()
        == NUM_PAGES * NUM_PAGE_OBJECTS_PER_PAGE
    )

    real_estate_property = RealestateProperty.objects.create(
        dossier=dossier, key="lorem"
    )
    first_page: SemanticPage = semantic_document.semantic_pages.first()
    original_file = (
        first_page.processed_page.processed_file.extracted_file.original_file
    )
    AssignedRealestatePropertyOriginalFile.objects.create(
        originalfile=original_file, realestate_property=real_estate_property
    )

    pages_stream_1 = SemanticPage.objects.filter(
        number__lte=4, semantic_document=semantic_document
    )
    pages_stream_1.update(document_category=document_category_1)

    pages_stream_2 = SemanticPage.objects.filter(
        number__gt=4, number__lt=9, semantic_document=semantic_document
    )
    pages_stream_2.update(document_category=document_category_2)
    # End generate synthetic data

    # Prepare data for request with unsuccessful split because the provided document categories are wrong
    request_body = get_request_body(
        document_category_1,
        document_category_2,
        pages_stream_1,
        pages_stream_2,
        invalid_document_categories=True,
    )

    with pytest.raises(ValidationError):
        save_page_streams_to_new_semantic_documents(
            semantic_document, dossier, request_body
        )

    # Prepare data for request with unsuccessful split because pages have been updated in the meantime
    request_body = get_request_body(
        document_category_1,
        document_category_2,
        pages_stream_1,
        pages_stream_2,
        updated_pages=True,
    )

    with pytest.raises(ValidationError):
        save_page_streams_to_new_semantic_documents(
            semantic_document, dossier, request_body
        )

    # Prepare data for request with unsuccessful split because of bad page uuid
    request_body = get_request_body(
        document_category_1,
        document_category_2,
        pages_stream_1,
        pages_stream_2,
        invalid_uuids=True,
    )

    with pytest.raises(ValidationError):
        save_page_streams_to_new_semantic_documents(
            semantic_document, dossier, request_body
        )

    # Prepare data for request with successful split
    request_body = get_request_body(
        document_category_1, document_category_2, pages_stream_1, pages_stream_2
    )

    save_page_streams_to_new_semantic_documents(
        semantic_document, dossier, request_body
    )

    # dossier should now have 2 non-deleted semantic documents
    assert SemanticDocument.objects.filter(dossier=dossier).count() == 2
    assert SemanticDocument.all_objects.filter(dossier=dossier).count() == 3
    # new semantic documents should have the expected number of pages
    semantic_document_1 = SemanticPage.objects.get(
        uuid=pages_stream_1[0].uuid
    ).semantic_document
    semantic_document_2 = SemanticPage.objects.get(
        uuid=pages_stream_2[0].uuid
    ).semantic_document
    assert semantic_document_1.semantic_pages.count() == 5
    assert semantic_document_2.semantic_pages.count() == 4
    assert semantic_document_1.document_category.name == "TEST_DOC_CAT_1"
    assert semantic_document_2.document_category.name == "TEST_DOC_CAT_2"

    assert (
        semantic_document_1.aggregated_page_objects.count()
        + semantic_document_2.aggregated_page_objects.count()
        == NUM_PAGES * NUM_PAGE_OBJECTS_PER_PAGE
    )

    # original file was the same for all pages so we expect both new semantic docs to have assigned realestate properties
    assert (
        semantic_document_1.assignedrealestatepropertysemanticdocument_set.count() == 1
    )
    assert (
        semantic_document_2.assignedrealestatepropertysemanticdocument_set.count() == 1
    )
