from django.db.models.signals import pre_save
from django.dispatch import receiver
from events.services import publish

from semantic_document.models import SemanticDocument
from semantic_document.schemas import SemanticDocumentWorkStatusChangedEvent


def try_to_get_username():
    try:
        import inspect

        for fr in inspect.stack():
            if "request" in fr[0].f_locals:
                request = fr[0].f_locals["request"]
                if hasattr(request, "auth"):
                    print(request.auth)
                    return request.auth.user.username

    except:
        pass
    return "system"


@receiver(pre_save, sender=SemanticDocument)
def semantic_document_status_changed_handler(
    sender, instance: SemanticDocument, **kwargs
):
    old_semantic_document = SemanticDocument.objects.filter(uuid=instance.uuid).first()

    from_state_key = None
    if (
        old_semantic_document is not None
        and old_semantic_document.work_status is not None
    ):
        from_state_key = old_semantic_document.work_status.key

    to_state_key = None
    if instance.work_status is not None:
        to_state_key = instance.work_status.key

    if from_state_key != to_state_key:
        username = try_to_get_username()
        publish(
            SemanticDocumentWorkStatusChangedEvent(
                semantic_document_uuid=instance.uuid,
                username=username,
                from_state_key=from_state_key,
                to_state_key=to_state_key,
            )
        )
