import structlog
import djclick as click

from dossier.models import Account
from semantic_document.helpers import (
    get_semantic_documents_with_incorrect_page_numbering,
)

logger = structlog.get_logger()


@click.command()
@click.argument("account_key")
def check_incorrect_page_numbering(account_key: str):
    """
    python manage.py check_incorrect_page_numbering account_name
    Lists all semantic documents with incorrect page numbering for the given account.

    python manage.py check_incorrect_page_numbering default

    @param account_key: The account key to check documents for
    @return:
    """
    logger.info(f"Checking incorrect page numbering for account '{account_key}'...")

    account = Account.objects.get(key=account_key)
    documents = get_semantic_documents_with_incorrect_page_numbering(account)

    start_at_one = documents["start_at_one"]
    start_at_two_or_more = documents["start_at_two_or_more"]
    non_consecutive_docs = documents["non_consecutive"]

    if (
        not start_at_one.exists()
        and not start_at_two_or_more.exists()
        and not non_consecutive_docs.exists()
    ):
        logger.info("No documents with incorrect page numbering found.")
        return

    total_count = (
        start_at_one.count()
        + start_at_two_or_more.count()
        + non_consecutive_docs.count()
    )
    logger.info(f"Found {total_count} documents with incorrect page numbering:")

    if start_at_one.exists():
        logger.info(f"Documents starting at page index 1 ({start_at_one.count()}):")
        for idx, doc in enumerate(start_at_one):
            logger.info(
                f"{idx + 1} Document starts at wrong page number 1",
                document_id=doc.uuid,
                document_title=doc.title,
                dossier_name=doc.dossier.name,
                min_page=doc.min_page_number,
                max_page=doc.max_page_number,
                page_count=doc.page_count,
                created_at=doc.created_at,
            )
            if idx >= 49:
                break

    if start_at_two_or_more.exists():
        logger.info(
            f"Documents starting at page index 2 or more ({start_at_two_or_more.count()}):"
        )
        for idx, doc in enumerate(start_at_two_or_more):
            logger.info(
                f"{idx + 1} Document starts at wrong page number 2 or more",
                document_id=doc.uuid,
                document_title=doc.title,
                dossier_name=doc.dossier.name,
                min_page=doc.min_page_number,
                max_page=doc.max_page_number,
                page_count=doc.page_count,
                created_at=doc.created_at,
            )
            if idx >= 49:
                break

    if non_consecutive_docs.exists():
        logger.info(
            f"Documents with non-consecutive pages ({non_consecutive_docs.count()}):"
        )
        for idx, doc in enumerate(non_consecutive_docs):
            logger.info(
                f"{idx + 1} Document has gaps in page numbering",
                document_id=doc.uuid,
                document_title=doc.title,
                dossier_name=doc.dossier.name,
                min_page=doc.min_page_number,
                max_page=doc.max_page_number,
                page_count=doc.page_count,
                created_at=doc.created_at,
            )
            if idx >= 49:
                break
