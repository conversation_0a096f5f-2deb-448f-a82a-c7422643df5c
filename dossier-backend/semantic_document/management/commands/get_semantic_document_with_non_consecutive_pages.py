import djclick as click
import logging

from dossier.models import Account
from semantic_document.helpers import (
    get_semantic_documents_with_incorrect_page_numbering,
)

logger = logging.getLogger(__name__)


@click.command()
@click.argument("account_key")
def get_semantic_document_with_non_consecutive_pages(account_key: str):
    """
    Get a single semantic document with non-consecutive page ordering from the specified account.

    Example usage:
    python manage.py get_semantic_document_with_non_consecutive_pages default
    """
    try:
        account = Account.objects.get(key=account_key)
    except Account.DoesNotExist:
        logger.error(f"Account '{account_key}' not found")
        return

    # Get documents with incorrect page numbering
    documents = get_semantic_documents_with_incorrect_page_numbering(account)
    non_consecutive_docs = documents["non_consecutive"]

    if not non_consecutive_docs.exists():
        logger.info("No documents found with non-consecutive pages")
        return

    # Get the first document
    doc = non_consecutive_docs.first()
    pages = doc.semantic_pages.order_by("number")
    page_numbers = list(pages.values_list("number", flat=True))

    logger.info(
        f"Found document with UUID: {doc.uuid}\n"
        f"Current page numbers: {page_numbers}\n"
        f"Document created at: {doc.created_at}"
    )
