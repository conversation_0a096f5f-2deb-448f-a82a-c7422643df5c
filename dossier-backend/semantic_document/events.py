from typing import List

from django.contrib.auth.models import AbstractUser

from semantic_document import schemas
from semantic_document.models import SemanticPage, SemanticDocument


def create_semantic_pages_moved_event(
    user: AbstractUser,
    source_semantic_pages: List[schemas.SemanticPageMovedDetail],
    dest_semantic_pages: List[SemanticPage],
):
    moves = []
    for created_page in dest_semantic_pages:
        source_semantic_page = next(
            (
                source_semantic_page
                for source_semantic_page in source_semantic_pages
                if source_semantic_page.processed_page_uuid
                == created_page.processed_page_id
            ),
            None,
        )
        destination_semantic_document = created_page.semantic_document
        moves.append(
            create_semantic_page_moved(
                created_page, destination_semantic_document, source_semantic_page
            )
        )
    event = schemas.SemanticPagesMovedEvent(username=user.username, moves=moves)
    return event


def create_semantic_page_moved(
    created_page: SemanticPage,
    destination_semantic_document: SemanticDocument,
    source_semantic_page: schemas.SemanticPageMovedDetail,
):
    return schemas.SemanticPageMoved(
        source=schemas.SemanticPageMovedDetail(**source_semantic_page.model_dump()),
        destination=schemas.SemanticPageMovedDetail(
            processed_page_uuid=created_page.processed_page.uuid,
            semantic_page_uuid=created_page.uuid,
            semantic_document=schemas.SemanticDocumentMoved(
                uuid=destination_semantic_document.uuid,
                document_category_name=destination_semantic_document.document_category.name,
                title=destination_semantic_document.title,
            ),
            page_number=created_page.number,
        ),
    )
