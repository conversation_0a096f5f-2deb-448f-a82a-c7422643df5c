import djclick as click

from dossier.models import Account
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
)


@click.command()
@click.argument("account_key", type=str)
def load_semantic_document_statemgmt(account_key):
    """
    Load semantic document state machine for a specific account.

    Example:
        python manage.py load_semantic_document_statemgmt bcgeevo
        python manage.py load_semantic_document_statemgmt clientistest

    Args:
        account_key: The key of the account to update
    """
    created_objects = create_semantic_document_state_machine()

    account = Account.objects.filter(key=account_key).first()

    if account:
        account.active_semantic_document_work_status_state_machine = created_objects[
            "machine"
        ]
        account.save()
        click.echo(f"Successfully updated state machine for account: {account_key}")
    else:
        click.echo(f"Account with key '{account_key}' not found", err=True)
