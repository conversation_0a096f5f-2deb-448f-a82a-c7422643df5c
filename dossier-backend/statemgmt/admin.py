from adminsortable.admin import SortableAdmin
from django.contrib import admin

from statemgmt.models import (
    StateMachine,
    Status,
    StateTransition,
    StateCondition,
    TransitionPrecondition,
)
from django import forms


class StateTransitionAdminForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["from_state"].queryset = Status.objects.filter(
            state_machine_id=self.instance.state_machine_id
        )
        self.fields["to_state"].queryset = Status.objects.filter(
            state_machine_id=self.instance.state_machine_id
        )


class StateTransitionInline(admin.TabularInline):
    model = StateTransition
    # form = StateTransitionAdminForm
    fk_name = "from_state"
    extra = 0
    show_change_link = True

    def get_formset(self, request, obj=None, **kwargs):
        self.parent_obj = obj
        return super().get_formset(request, obj, **kwargs)

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "state_machine":
            if self.parent_obj is not None and self.parent_obj.state_machine:
                kwargs["initial"] = self.parent_obj.state_machine.uuid
                return db_field.formfield(**kwargs)
            if db_field.name == "to_state":
                print(self.parent_obj.state_machine)
                kwargs["queryset"] = Status.objects.filter(
                    state_machine_id=self.parent_obj.state_machine.uuid
                )
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(Status)
class StatusAdmin(SortableAdmin):
    list_display = [
        "uuid",
        "state_machine",
        "key",
        "name_de",
        "name_fr",
        "name_it",
        "name_en",
        "color",
    ]
    list_filter = ("state_machine",)

    inlines = (StateTransitionInline,)


@admin.register(StateCondition)
class StateConditionAdmin(SortableAdmin):
    list_display = ("uuid", "name")


class TransitionPreconditionInline(admin.StackedInline):
    model = TransitionPrecondition
    extra = 0
    show_change_link = True


class StatusInline(admin.TabularInline):
    model = Status
    extra = 0
    show_change_link = True
    fields = ["key", "name_de", "name_en", "color"]


@admin.register(StateTransition)
class StateTransitionAdmin(admin.ModelAdmin):
    form = StateTransitionAdminForm

    list_display = [
        "state_machine",
        "from_state",
        "to_state",
        "created_at",
        "updated_at",
    ]
    search_fields = ["from_state__key", "to_state__key"]
    list_filter = ("state_machine", "from_state", "to_state")

    readonly_fields = ["created_at", "updated_at"]

    inlines = (TransitionPreconditionInline,)


@admin.register(StateMachine)
class StateMachineAdmin(admin.ModelAdmin):
    list_display = ("uuid", "name", "plantuml")
    inlines = (StatusInline,)
    readonly_fields = ("plantuml",)
