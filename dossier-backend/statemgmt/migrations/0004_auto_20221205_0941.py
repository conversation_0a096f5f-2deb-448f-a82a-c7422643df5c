# Generated by Django 3.2.16 on 2022-12-05 08:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('statemgmt', '0003_auto_20221101_1000'),
    ]

    operations = [
        migrations.AddField(
            model_name='statemachine',
            name='start_status',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='statemgmt.status'),
        ),
        migrations.AddField(
            model_name='transitionprecondition',
            name='overrideable',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='transitionprecondition',
            name='show_anyway',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='transitionprecondition',
            name='warning_de',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='transitionprecondition',
            name='warning_en',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='transitionprecondition',
            name='warning_fr',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='transitionprecondition',
            name='warning_it',
            field=models.TextField(blank=True, null=True),
        ),
    ]
