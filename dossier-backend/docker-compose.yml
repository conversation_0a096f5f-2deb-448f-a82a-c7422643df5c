version: '3.8'
services:
  dec:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py dossier_event_consumer_v2
    environment:
      - DOSSIER_EVENTS_WORKER_QUEUE_NAME=DossierEvents.dev-${USERNAME}.DossierEventV1
      - SENTRY_ENVIRONMENT=dev-${USERNAME}
    networks:
      core-services:
      caddy:
    volumes:
      - .:/app

  diew:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py image_exporter_worker
    environment:
      - SENTRY_ENVIRONMENT=dev-${USERNAME}
    networks:
      core-services:
    volumes:
      - .:/app

  dms:
#    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    build:
      context: .
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    environment:
      - DOSSIER_EVENTS_WORKER_QUEUE_NAME=DossierEvents.dev-${USERNAME}.DossierEventV1
      - SENTRY_ENVIRONMENT=dev-${USERNAME}
      - IMAGE_EXPORTER_WORKER_QUEUE_NAME=DossierImageExporter.dev-${USERNAME}.ImageExportRequest
    volumes:
      - .:/app
    networks:
      core-services:
      caddy:
    labels:
      caddy: dms.hypo.duckdns.org
      caddy.reverse_proxy: "{{upstreams 8000}}"
      caddy.import: tls

  worker:
#    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    build:
      context: .
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py worker
    environment:
      - SENTRY_ENVIRONMENT=dev-${USERNAME}
    networks:
      caddy:
      core-services:
    volumes:
      - .:/app


networks:
  caddy:
    external: true
  core-services:
    external: true
