import json

import pytest
from django.urls import reverse
from jwcrypto.jwk import JW<PERSON>
from jwcrypto.jwt import JWT
from django.conf import settings

from core.authentication import AuthenticatedClient
from dossier.models import Dossier


@pytest.fixture
def tophypo_cdp_dossier(tophypo_account, token_data):
    # get the default account
    tophypo_account.cdp_field_set = "topHypoFieldSet"
    tophypo_account.save()
    dossier = Dossier.objects.create(
        account=tophypo_account,
        name="test tophypo dossier",
        external_id=token_data.get("external_dossier_id"),
    )

    return dossier


@pytest.mark.django_db
def test_user_without_dossier_access_cannot_obtain_cdp_token(
    testuser1_client: AuthenticatedClient, tophypo_cdp_dossier
):

    response = testuser1_client.get(
        reverse("tophypo-api:cdp-dossier-access-token", args=[tophypo_cdp_dossier.uuid])
    )

    assert response.status_code == 401


@pytest.mark.django_db
def test_user_with_dossier_access_can_obtain_cdp_token(
    tophypo_authenticated_client: AuthenticatedClient,
    tophypo_cdp_dossier,
    set_tophypo_JWK,
):
    response = tophypo_authenticated_client.get(
        reverse(
            "tophypo-api:cdp-dossier-access-token",
            args=[tophypo_cdp_dossier.external_id],
        )
    )
    assert response.status_code == 200
    jwt = JWT(
        jwt=response.json(),
        key=JWK.from_json(settings.INSTANCE_SIGNING_KEY),
        expected_type="JWS",
    )
    claims = json.loads(jwt.claims)
    assert claims["dossier_uuid"] == str(tophypo_cdp_dossier.uuid)
    assert claims["iss"] == "dms"
    assert claims["aud"] == "cdp"
    assert claims["exp"] > claims["iat"]
    assert claims["field_set"] == tophypo_cdp_dossier.account.cdp_field_set
