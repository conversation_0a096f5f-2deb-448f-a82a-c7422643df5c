import datetime
from datetime import <PERSON><PERSON><PERSON>
from typing import List, Optional
from uuid import UUID

import structlog
from django.conf import settings
from django.db import transaction
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.utils import timezone
from ninja import NinjaAP<PERSON>, UploadedFile, File, Form
from ninja.errors import ValidationError
from ninja.security import HttpBearer
from pydantic import HttpUrl

from bcge.services import create_export_status_object_list
from core.schema import Message, Error
from dossier import models as dossier_models
from dossier import schemas as dossier_schema
from dossier import services_external as dossier_services_external
from dossier.helpers_api import handle_api_validation_error
from dossier.models import (
    OriginalFile,
    DossierAccessGrant,
    DossierUser,
    OriginalFileSource,
    Dossier,
)
from dossier.schemas_external import PrepareCloseDossierResponse
from dossier.services_external import (
    serialize_semantic_document,
    create_dossier_api,
    update_dossier_api,
    get_dossier_with_access_check_api,
    check_dossier_ready_for_closing,
    create_dossier_close_ready_response,
    create_dossier_state_context,
    perform_dossier_close_api_wrapper,
    prepare_dossier_for_closing,
)
from processed_file.schemas import PageObjectSchema
from projectconfig.authentication import (
    authenticate_from_account,
    get_user_or_create,
)
from semantic_document.models import SemanticDocument
from semantic_document.services import (
    set_dossier_semantic_documents_state_ready_for_export,
    api_set_semantic_document_ready_for_export,
)
from processed_file.services import (
    export_aggregate_page_objects_from_semantic_document_with_title,
)
from semantic_document import (
    helpers as semantic_document_helpers,
    models as semantic_document_models,
    schemas as semantic_document_schemas,
)
from semantic_document.services_external import update_semantic_document_export_status
from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
)
from statemgmt.services import (
    StateTransitionError,
)
from workers.models import SemanticDocumentExport
import bcge.schemas.schemas as bcge_schemas

from semantic_document.services import map_confidence

logger = structlog.get_logger()


class BCGEJWTAuth(HttpBearer):
    def authenticate(self, request, token, *args, **kw):
        jwt = authenticate_from_account(token)
        if settings.API_ROLE not in jwt.user_roles:
            return
        return jwt


api = NinjaAPI(
    title="Hypodossier - BCGE API",
    csrf=False,
    auth=BCGEJWTAuth(),
    urls_namespace="bcge-api",
    version="0.1.0",
    servers=[],
)


@api.exception_handler(ValidationError)
def custom_validation_errors(request, exc):
    return handle_api_validation_error(api, logger, exc, request)


def map_dossier(dossier):
    # Same as SWISSFEX, but keep duplicate code, as dossier schema might diverge
    return bcge_schemas.Dossier(
        uuid=dossier.uuid,
        external_dossier_id=str(dossier.external_id),
        updated_at=dossier.updated_at,
        created_at=dossier.created_at,
    )


@api.get("/ping", response={200: Message}, url_name="ping", exclude_none=True)
def ping(request):
    return Message(detail="pong")


@api.post(
    "/dossier",
    response={201: bcge_schemas.Dossier, 409: Error},
    url_name="create-dossier",
    exclude_none=True,
)
def create_dossier(request, dossier_create: bcge_schemas.CreateDossier):

    return create_dossier_api(request, dossier_create)


@api.get(
    "/dossier/{external_dossier_id}/show",
    response={302: HttpUrl},
    auth=None,
    url_name="show-dossier",
)
def show_dossier(
    request,
    external_dossier_id: str,
) -> HttpResponse:
    """
    Redirects to the dossier for the user to view it if it exists or returns a 404
    """
    dossier = get_object_or_404(Dossier, external_id=external_dossier_id)
    account = dossier.account
    response_url = f"{account.dmf_endpoint}/dossier/{dossier.uuid}/view/page"
    return HttpResponseRedirect(redirect_to=response_url)


@api.patch(
    "/dossier/{external_dossier_id}",
    response={201: bcge_schemas.Dossier, 409: Error, 404: Message},
    url_name="update-dossier",
    exclude_none=True,
)
def update_dossier(
    request, external_dossier_id: str, dossier_change: bcge_schemas.ChangeDossier
):
    """Updates a new Dossier based on the provided parameters

    We provide a external_dossier_id as part of the URL and allow the client to change it
    as part of ChangeDossier
    """

    return update_dossier_api(
        request=request,
        external_dossier_id=external_dossier_id,
        dossier_change=dossier_change,
    )


@api.post(
    "/dossier/{external_dossier_id}/original-files",
    response={201: dossier_schema.CreatedObjectReference, 409: Message, 404: Message},
    url_name="add-original-file",
    exclude_none=True,
)
@transaction.atomic
def add_original_file(
    request,
    external_dossier_id: str,
    file: UploadedFile = File(...),
    allow_duplicate_and_rename: bool = Form(False),
):
    """Add an original file to a dossier"""
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    response_code, original_file = dossier_services_external.add_original_file(
        dossier=dossier,
        file=file,
        allow_duplicate_and_rename=allow_duplicate_and_rename,
        source=OriginalFileSource.API.value,
        create_user=dossier_user.user,
    )

    return response_code, original_file


@api.get(
    "/dossier/{external_dossier_id}/filename/{original_filename}",
    response={200: bool},
    url_name="check-original-filename",
    exclude_none=True,
    description="Check if a file with the same name already exists in the dossier. Return True if file already exists, else False",
)
def check_original_filename(
    request,
    external_dossier_id: str,
    original_filename: str,
):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    original_files = OriginalFile.objects.filter(dossier=dossier)

    if original_files.exists():
        for original_file in original_files:
            if original_file.file.name == original_filename:
                return 200, True

    return 200, False


@api.get(
    "/dossier/{external_dossier_id}/semantic-documents",
    response={200: List[bcge_schemas.SemanticDocument], 404: Message},
    url_name="semantic-documents",
    exclude_none=True,
)
def get_semantic_documents(request, external_dossier_id: str, show_pages: bool = False):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_documents = dossier_services_external.get_semantic_documents(
        dossier=dossier,
        semantic_document_serializer=bcge_schemas.SemanticDocument,
        page_serializer=bcge_schemas.SemanticPage,
        map_confidence=map_confidence,
        show_pages=show_pages,
    )

    return semantic_documents


@api.patch(
    "/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}",
    response={200: bcge_schemas.SemanticDocument, 404: Message},
    url_name="update-semantic-document",
    exclude_none=True,
)
def update_semantic_document(
    request,
    external_dossier_id: str,
    semantic_document_uuid: UUID,
    update: bcge_schemas.SemanticDocumentUpdate,
):
    """
    Update the external_semantic_document_id and/or
    access_mode for a semantic document using a semantic_document_uuid
    """
    dossier_user = request.auth.get_user_or_create()
    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document = get_object_or_404(
        SemanticDocument, uuid=semantic_document_uuid, dossier=dossier
    )

    for attr, value in update.model_dump().items():
        if value:
            setattr(semantic_document, attr, value)

    semantic_document.save()

    return serialize_semantic_document(
        semantic_document=semantic_document,
        semantic_document_serializer=bcge_schemas.SemanticDocument,
        page_serializer=bcge_schemas.SemanticPage,
        map_confidence=map_confidence,
    )


@api.get(
    "/document-categories",
    response={200: bcge_schemas.DocumentCategories},
    url_name="document-categories",
    exclude_none=True,
)
def get_document_categories(request):
    dossier_user = request.auth.get_user_or_create()
    account = dossier_user.account
    return bcge_schemas.DocumentCategories(
        root={
            category.name: bcge_schemas.DocumentCategory(
                key=category.name,
                id=category.id,
                title_de=category.de,
                title_en=category.en or "",
                title_fr=category.fr or "",
                title_it=category.it or "",
                description_de=category.description_de,
                description_en=category.description_en,
                description_fr=category.description_fr,
                description_it=category.description_it,
            )
            for category in dossier_models.DocumentCategory.objects.filter(
                account__key=account.key
            ).all()
        }
    )


@api.post(
    "/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-state-ready-for-export",
    url_name="semantic-document-set-state-ready-for-export",
    description="Set the state of a Semantic document to ready for export, and dispatch export process. "
    "Will only process a document that has SemanticDocument.work_state=IN_FRONT_OFFICE",
    response={200: UUID, 204: None, 404: Message},
)
def post_api_set_semantic_document_ready_for_export(
    request, external_dossier_id, semantic_document_uuid: UUID
):
    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    # This throws a 404 if the uuid does not exist
    result: Optional[UUID] = api_set_semantic_document_ready_for_export(
        dossier=dossier, semantic_document_uuid=semantic_document_uuid
    )

    if result is None:
        return 204, None  # No Content when the document was deleted

    return 200, result  # Return the UUID when available


@api.post(
    "/export/dossier/{external_dossier_id}/set-semantic-documents-state-ready-for-export",
    url_name="dossier-set-semantic-documents-state-ready-for-export",
    description="Set the state of Semantic documents within a dossier to ready for export, and dispatch export process. "
    "Will only process a document that has SemanticDocument.work_state=IN_FRONT_OFFICE. This returns a list of a semantic document export UUIDs (not semantic document UUIDs).",
    response=List[UUID],
)
def set_semantic_documents_state_ready_for_export(request, external_dossier_id):
    """
    This deletes empty non-deleted documents without warning as it does not make sense to export them and we want
    a "fully exported" dossier after this call.
    @param request:
    @param external_dossier_id:
    @return:
    """
    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    return set_dossier_semantic_documents_state_ready_for_export(dossier=dossier)


@api.get(
    "/export/{external_dossier_id}/semantic-documents-available",
    response=List[bcge_schemas.ExportStatusSemanticDocument],
    url_name="dossier-semantic-document-export-status",
    exclude_none=True,
    description="Get the status of all semantic document exports available for a dossier",
)
def get_semantic_document_exports_available(request, external_dossier_id: str):

    request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semdoc_exports = list(
        (
            SemanticDocumentExport.objects.filter(
                semantic_document__dossier=dossier, done__isnull=False
            )
            .filter(
                semantic_document__work_status__key=SemanticDocumentState.EXPORT_AVAILABLE.value
            )
            .order_by("done")
            .select_related(
                "semantic_document",
                "semantic_document__dossier",
                "semantic_document__document_category",
            )
        ).all()
    )

    return create_export_status_object_list(semdoc_exports)


@api.get(
    "/export/all-semantic-documents-available",
    response=List[bcge_schemas.ExportStatusSemanticDocument],
    url_name="all-semantic-document-export-status",
    exclude_none=True,
    description="Get the status of all semantic document exports available for an account",
)
def get_all_semantic_document_exports_available(request):

    dossier_user = request.auth.get_user_or_create()

    export_semantic_docs = (
        SemanticDocumentExport.objects.filter(
            semantic_document__dossier__account=dossier_user.account,
            # Ignore all dossiers, that have no external id
            semantic_document__dossier__external_id__isnull=False,
            done__isnull=False,
        )
        .filter(
            semantic_document__work_status__key=SemanticDocumentState.EXPORT_AVAILABLE.value
        )
        .order_by("done")
        .select_related(
            "semantic_document",
            "semantic_document__dossier",
            "semantic_document__document_category",
        )
    )

    ret = create_export_status_object_list(export_semantic_docs)
    return ret


@api.post(
    "/export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-done",
    response=List[UUID],
    url_name="dossier-semantic-document-export-set-done",
    exclude_none=True,
    description="Confirm successful export for a semantic document. Returns UUID of affected export.",
)
@transaction.atomic
def update_export_status_done(
    request, external_dossier_id: str, semantic_document_uuid: UUID
):
    target_state = SemanticDocumentState.EXPORT_DONE
    return update_semantic_document_export_status(
        request, external_dossier_id, semantic_document_uuid, target_state
    )


@api.post(
    "/export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-error",
    response=List[UUID],
    url_name="dossier-semantic-document-export-set-error",
    exclude_none=True,
    description="Confirm unsuccessful export for a semantic document. Returns list of UUID of affected exports. This is one export in all normal successful operations.",
)
@transaction.atomic
def update_export_status_error(
    request,
    external_dossier_id: str,
    semantic_document_uuid: UUID,
    error_message: Optional[bcge_schemas.ErrorMessageSchema] = None,
):
    target_state = SemanticDocumentState.EXPORT_ERROR
    return update_semantic_document_export_status(
        request,
        external_dossier_id,
        semantic_document_uuid,
        target_state,
        error_message=error_message.error_message if error_message else None,
    )


@api.delete(
    "/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}",
    response={200: semantic_document_schemas.SavingResultWithMessage},
    url_name="semantic-document-soft-delete",
    include_in_schema=False,
)
def soft_delete_semantic_document(
    request, external_dossier_id: str, semantic_document_uuid: UUID
):
    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document_helpers.semantic_restore_or_delete(
        dossier,
        semantic_document_models.SemanticDocument.all_objects,
        semantic_document_uuid,
        True,
    )

    return semantic_document_schemas.SavingResultWithMessage(message="deleted")


@api.put(
    "/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}/restore",
    response={200: semantic_document_schemas.SavingResultWithMessage},
    url_name="semantic-document-restore",
    include_in_schema=False,
)
def undelete_semantic_document(
    request, external_dossier_id: str, semantic_document_uuid: UUID
):
    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document_helpers.semantic_restore_or_delete(
        dossier,
        semantic_document_models.SemanticDocument.all_objects,
        semantic_document_uuid,
        False,
    )

    return semantic_document_schemas.SavingResultWithMessage(message="restored")


@api.delete(
    "/dossier/{external_dossier_id}",
    response={202: Message, 404: Message},
    url_name="dossier-delete",
    exclude_none=True,
)
def delete_dossier(request, external_dossier_id):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    dossier.expiry_date = timezone.now() - timedelta(days=1)
    dossier.save()

    return 202, {"detail": "Dossier Scheduled for deletion"}


@api.post(
    "/dossier-user-grant",
    response=Message,
    url_name="set-dossier-user-grant",
    exclude_none=True,
    description="Grants temporary access to a dossier for a user. If a datetime in the past is set, then access is revoked",
)
@transaction.atomic
def set_dossier_user_grant(
    request,
    dossier_access_grant: bcge_schemas.DossierAccessGrant,
):
    api_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=api_user,
        is_manager=True,
        external_dossier_id=dossier_access_grant.external_dossier_id,
    )

    # At bcge the only identifier we get is the username (which is the emailaddress)
    # via access grant schema.
    # Firstname, lastname, email and username is provided via sso.

    # 241216 we assume that the username must always be lowercase
    lowercase_name = dossier_access_grant.username.lower()
    if lowercase_name != dossier_access_grant.username:
        logger.info(
            "uppercase username in set_dossier_access_grant",
            username=dossier_access_grant.username,
            lowercase_name=lowercase_name,
        )

    dossier_user: DossierUser = get_user_or_create(
        account=dossier.account,
        username=lowercase_name,
    )

    dag, _ = DossierAccessGrant.objects.update_or_create(
        user=dossier_user.user,
        dossier=dossier,
        defaults={
            "expires_at": dossier_access_grant.expires_at,
            "has_access": dossier_access_grant.has_access,
        },
    )

    if not dag.has_access:
        # PG: an alternative would be to delete the grant
        logger.info(
            "Access revoked",
            username=dossier_access_grant.username,
            lowercase_name=lowercase_name,
        )

        return 200, {"detail": "Access revoked"}

    if dossier_access_grant.expires_at < timezone.now():
        logger.info(
            "Access expired",
            username=dossier_access_grant.username,
            lowercase_name=lowercase_name,
        )

        return 200, {"detail": "Access expired"}

    return 200, {
        "detail": f"Access granted for {dag.user}, expires at {dag.expires_at}"
    }


@api.get(
    "/original-files",
    response=List[bcge_schemas.OriginalFileSchema],
    url_name="get-original-files",
    exclude_none=True,
    description="Get original files created after a timestamp, filterable via source (either 'API' or 'DMF' (Dossier Manager Frontend)",
)
def get_original_files(
    request,
    created_after_timestamp: Optional[datetime.datetime] = None,
    source: Optional[bcge_schemas.OriginalFileSourceSchema] = None,
) -> List[bcge_schemas.OriginalFileSchema]:
    dossier_user = request.auth.get_user_or_create()

    filters = {
        "dossier__account": dossier_user.account,
        "dossier__external_id__isnull": False,
    }

    if source:
        filters["source"] = source

    if created_after_timestamp:
        filters["created_at__gt"] = created_after_timestamp

    original_files = OriginalFile.objects.filter(**filters)

    return [
        bcge_schemas.OriginalFileSchema(
            original_file_uuid=original_file.uuid,
            external_dossier_id=original_file.dossier.external_id,
            created_at=original_file.created_at,
        )
        for original_file in original_files
    ]


@api.get(
    "/dossier/{external_dossier_id}/original-files",
    response=List[bcge_schemas.OriginalFileSchema],
    url_name="get-dossier-original-files",
    exclude_none=True,
    description="Get original files for a dossier, created after a timestamp, filterable via source (either 'API' or 'DMF' (Dossier Manager Frontend)",
)
def get_dossier_original_files(
    request,
    external_dossier_id,
    created_after_timestamp: Optional[datetime.datetime] = None,
    source: Optional[bcge_schemas.OriginalFileSourceSchema] = None,
) -> List[bcge_schemas.OriginalFileSchema]:
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    filters = {
        "dossier": dossier,
        "dossier__external_id__isnull": False,
    }

    if source:
        filters["source"] = source

    if created_after_timestamp:
        filters["created_at__gt"] = created_after_timestamp

    original_files = OriginalFile.objects.filter(**filters)

    return [
        bcge_schemas.OriginalFileSchema(
            original_file_uuid=original_file.uuid,
            external_dossier_id=original_file.dossier.external_id,
            created_at=original_file.created_at,
        )
        for original_file in original_files
    ]


@api.get(
    "/export/original-file/{original_file_uuid}",
    response=bcge_schemas.ExportOriginalFileSchema,
    url_name="export-original-file",
    exclude_none=True,
    description="Export original file by uuid",
)
def export_original_file(
    request,
    original_file_uuid: UUID,
) -> bcge_schemas.ExportOriginalFileSchema:
    dossier_user = request.auth.get_user_or_create()

    original_file = get_object_or_404(
        OriginalFile, uuid=original_file_uuid, dossier__account=dossier_user.account
    )

    return bcge_schemas.ExportOriginalFileSchema(
        original_file_uuid=original_file.uuid,
        external_dossier_id=original_file.dossier.external_id,
        file_url=original_file.file.get_fast_url(),
        created_at=original_file.created_at,
    )


@api.get(
    "/semanticdocument/{semantic_document_uuid}/unique-page-objects",
    response={200: List[PageObjectSchema], 404: Message},
    url_name="semantic-document-unique-page-objects",
    exclude_none=True,
    description="Return page objects for a specific semantic document - for a semantic page, "
    "if multiple page objects of the same type exist (same category key), only return the first one. "
    "This way the page objects are unique per semantic page",
)
def get_semantic_document_unique_page_objects(request, semantic_document_uuid):
    dossier_user = request.auth.get_user_or_create()

    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_document_uuid)

    # Perform access check
    get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=semantic_document.dossier.external_id,
    )

    page_objects = export_aggregate_page_objects_from_semantic_document_with_title(
        semantic_document=semantic_document,
        exclude_page_object_type_names=["OBJECT", "IMAGE"],
    )

    return page_objects


@api.post(
    "/dossier/{external_dossier_id}/prepare-close-dossier",
    response={
        200: PrepareCloseDossierResponse,
        400: Message,
        404: Message,
    },
    url_name="prepare-close-dossier",
    description="Prepare a dossier for closing by setting state to CLOSING and making it read-only. "
    "If close_dossier_if_possible is True, also attempt to close a dossier if it is possible.",
)
def prepare_close_dossier(
    request, external_dossier_id: str, close_dossier_if_possible: bool = True
):
    """
    Prepares a dossier for closing by:
    1. Validating it can be closed
    2. Setting state to CLOSING
    3. Making it read-only
    4. Triggering semantic document exports if needed
    """
    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,
        external_dossier_id=external_dossier_id,
    )

    # Check if dossier can be closed as part of context
    context = create_dossier_state_context(
        dossier=dossier, is_system=True, is_user=False
    )

    status_code, response = prepare_dossier_for_closing(
        dossier=dossier,
        context=context,
        close_dossier_if_possible=close_dossier_if_possible,
    )

    return status_code, response


@api.get(
    "/dossier/{external_dossier_id}/check-dossier-close-ready",
    response={200: bcge_schemas.DossierCloseReadyResponse},
    url_name="check-dossier-close-ready",
    exclude_none=True,
)
def check_dossier_close_ready(request, external_dossier_id):
    """
    Check if a dossier is ready to be closed. If not return instructions what the user needs to do
    """
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    stats, evaluation = check_dossier_ready_for_closing(dossier)
    res1 = create_dossier_close_ready_response(stats, evaluation)

    # We need to convert to Client specific pydantic schema
    res2: bcge_schemas.DossierCloseReadyResponse = (
        bcge_schemas.DossierCloseReadyResponse.model_validate(res1.model_dump())
    )
    return 200, res2


@api.post(
    "/dossier/{external_dossier_id}/close-dossier",
    response={200: bcge_schemas.DossierCloseResponse, 400: Message},
    url_name="close-dossier",
    exclude_none=True,
)
def close_dossier(request, external_dossier_id: str):
    """
    Check if a dossier can be closed and close it if all requirements are fulfilled.
    Return success (or not). In case it was not successful, instructions for user are returned.

    Rely on the state machine, dossier.work_status to ensure the dossier is read-only

    """
    dossier_user = request.auth.get_user_or_create()

    dossier: Dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,
        external_dossier_id=external_dossier_id,
    )
    try:

        success, evaluation = perform_dossier_close_api_wrapper(dossier)
    except StateTransitionError as e:
        return 400, {"detail": e.message}

    ret = bcge_schemas.DossierCloseResponse(
        success=success,
        msg_nok_de=evaluation.msg_nok_de,
        msg_nok_en=evaluation.msg_nok_en,
        msg_nok_fr=evaluation.msg_nok_fr,
        msg_nok_it=evaluation.msg_nok_it,
    )
    return 200, ret


@api.get(
    "/dossier/{external_dossier_id}/status",
    response={200: bcge_schemas.DossierStatusResponse, 404: Message},
    url_name="dossier-status",
    description="Get the current status of a dossier if one is set",
)
def get_dossier_status(request, external_dossier_id: str):
    """
    Returns the current status of a dossier if one is set.
    Requires authentication and proper access rights.
    """
    dossier_user = request.auth.get_user_or_create()

    dossier: Dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,
        external_dossier_id=external_dossier_id,
    )

    if not dossier.work_status:
        return 404, {"detail": "No status set for this dossier"}

    return 200, bcge_schemas.DossierStatusResponse(
        status_key=dossier.work_status.key,
    )
