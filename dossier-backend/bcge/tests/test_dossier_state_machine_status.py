import pytest
import structlog
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser

from dossier.statemachine_types import Do<PERSON>rState, DOSSIER_READ_ONLY_WORK_STATUS
from dossier_zipper.conftest import mocked_get_dossier  # noqa: F401
from statemgmt.models import Status


User: AbstractUser = get_user_model()


logger = structlog.get_logger(__name__)


pytestmark = pytest.mark.django_db

# Test violates dry as similar tests are in Dossier tests, however this ensures that
# state machine explicitly loaded by bcge_account has these states


def test_ensure_state_keys_in_state_machine(bcge_account):

    state_machine = bcge_account.active_work_status_state_machine

    start_status = state_machine.start_status

    assert start_status.key == DossierState.OPEN.value

    # These states must exist in "statemgmt/configurations/default/default_state_machine_2025_02_21.json"

    assert (
        Status.objects.filter(
            state_machine=state_machine,
            key__in=[
                DossierState.OPEN.value,
                DossierState.CLOSING.value,
                DossierState.CLOSED.value,
            ],
        ).count()
        == 3
    )


def test_ensure_read_only_state_keys_in_dossier_read_only_work_status_keys(
    bcge_account,
):
    # These must exist in DOSSIER_READ_ONLY_WORK_STATUS so that the Dossier is considered read-only
    # when in these states
    assert DossierState.CLOSING.value in DOSSIER_READ_ONLY_WORK_STATUS
    assert DossierState.CLOSED.value in DOSSIER_READ_ONLY_WORK_STATUS
