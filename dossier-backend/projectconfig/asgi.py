"""
ASGI config for backend project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/howto/deployment/asgi/
"""

import inspect
import os

import structlog
from asgiref.typing import LifespanStartupCompleteEvent, LifespanShutdownCompleteEvent
from django.core.asgi import get_asgi_application
from django.core.handlers.asgi import ASGIHandler

from dossier.helpers_timezone import log_timezone_info
from projectconfig.asgi_signals import asgi_startup, asgi_shutdown

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "projectconfig.settings")
django_asgi_app = get_asgi_application()


logger = structlog.get_logger(__name__)
from channels.routing import ProtocolTypeRouter, URLRouter

from projectconfig.authentication import QueryAuthMiddleware
import dossier.routing


channels_application = ProtocolTypeRouter(
    {
        "http": django_asgi_app,
        "websocket": QueryAuthMiddleware(
            URLRouter(dossier.routing.websocket_urlpatterns)
        ),
    }
)


class LifeSpanHandler(ASGIHandler):
    async def __call__(self, scope, receive, send):
        if scope["type"] == "lifespan":
            while True:
                message = await receive()

                match message["type"]:
                    case "lifespan.startup":
                        await self.dispatch_signal(asgi_startup, scope)
                        await send(
                            LifespanStartupCompleteEvent(
                                type="lifespan.startup.complete"
                            )
                        )
                    case "lifespan.shutdown":
                        await self.dispatch_signal(asgi_shutdown, scope)
                        await send(
                            LifespanShutdownCompleteEvent(
                                type="lifespan.shutdown.complete"
                            )
                        )
                        return
                    case _:
                        raise ValueError(
                            f"Unknown lifespan message type: {message['type']}"
                        )
        else:
            return await channels_application(scope, receive, send)

    async def dispatch_signal(self, signal, scope):
        response = signal.send(sender=self.__class__, scope=scope)
        for _, response in response:
            if not response:
                continue

            if inspect.isawaitable(response):
                await response
            else:
                response()


application = LifeSpanHandler()

log_timezone_info(logger)
