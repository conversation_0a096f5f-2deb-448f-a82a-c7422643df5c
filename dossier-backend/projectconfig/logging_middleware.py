import structlog
from django.utils.deprecation import MiddlewareMixin

logger = structlog.get_logger()


class BadRequestLoggerMiddleware(MiddlewareMixin):
    """
    process_response will be called with a real HttpResponse,
    even if the view was async.
    """

    def process_response(self, request, response):
        if response.status_code == 400:
            # best to use raw body here; request.json() is async
            try:
                payload = request.body.decode("utf-8", errors="replace")
            except Exception:
                payload = "<unavailable>"
            logger.error(
                "400 Bad Request on %s %s — payload: %r",
                request.method,
                request.get_full_path(),
                payload,
            )
        return response
