import uuid
from datetime import <PERSON><PERSON><PERSON>

import pytest
from django.contrib.auth.models import AnonymousUser
from django.core.cache import cache
from django.test import RequestFactory
from django.utils import timezone
from freezegun import freeze_time
import jwt
from jwcrypto import jwk as jwcrypto_jwk

from dossier.exceptions import HttpError
from dossier.models import Account, JWK
from dossier.services import create_expiration_date
from django.conf import settings
from projectconfig.authentication import (
    authenticate_from_JWK,
    merge_jwk_qs,
    authenticate_from_account,
    JWTAuth,
)
from projectconfig.jwk import convert_jwk_to_pem

pytestmark = pytest.mark.django_db


def generate_jwk() -> jwcrypto_jwk.JWK:
    jwk = jwcrypto_jwk.JWK.generate(
        kty="RSA", kid="swissfex", size=2048, use="sig", alg="RS256"
    )
    return jwk


def get_token_data():
    return {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "service-swissfex-first service-swissfex-last",
        "given_name": "service-swissfex-first",
        "family_name": "service-swissfex-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        "user_roles": ["api_role"],
        "account_key": "swissfexd",
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }


def gen_signed_token(key, data=None):
    if data is None:
        data = get_token_data()
    return jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **data},
        key=convert_jwk_to_pem(key),
        algorithm="RS256",
    )


@pytest.fixture
def account():
    account, _ = Account.objects.update_or_create(
        defaults=dict(
            name="swissfex development account",
            default_bucket_name="dms-default-bucket",
            key="swissfexd",
        ),
        dmf_endpoint="https://www.localhost",
    )
    return account


def test_authenticate_from_JWK():
    jwk_data: jwcrypto_jwk.JWK = generate_jwk()

    token_data = gen_signed_token(jwk_data)

    authenticate_from_JWK(token_data, jwk_data.export_public(as_dict=True))


def test_merge_jwk_qs(account):
    # Test that we can merge a queryset of JWKs into a single JWK
    # And that they authenticate correctly
    jwk_1: jwcrypto_jwk.JWK = generate_jwk()
    jwk_2: jwcrypto_jwk.JWK = generate_jwk()
    jwk_3: jwcrypto_jwk.JWK = generate_jwk()

    JWK.objects.create(jwk=jwk_1.export_public(as_dict=True), account=account)
    JWK.objects.create(jwk=jwk_2.export_public(as_dict=True), account=account)
    JWK.objects.create(jwk=jwk_3.export_public(as_dict=True), account=account)

    jwk_qs = JWK.objects.filter(account__key=account.key, enabled=True)

    merged_jwk = merge_jwk_qs(jwk_qs)

    assert len(merged_jwk.keys) == 3

    # Create 3 different tokens and check that they can be decrypted with the merged JWK
    assert authenticate_from_JWK(
        gen_signed_token(jwk_1), merged_jwk.model_dump(exclude_unset=True)
    )
    assert authenticate_from_JWK(
        gen_signed_token(jwk_2), merged_jwk.model_dump(exclude_unset=True)
    )
    assert authenticate_from_JWK(
        gen_signed_token(jwk_3), merged_jwk.model_dump(exclude_unset=True)
    )

    # Generate a JWK not in the queryset and check that it cannot be decrypted with the merged JWK
    with pytest.raises(HttpError, match="Failed to decode token"):
        authenticate_from_JWK(
            gen_signed_token(generate_jwk()), merged_jwk.model_dump(exclude_unset=True)
        )


def test_authenticate_from_account(account):
    cache.delete(f"jwk{account.key}")

    jwk_1 = generate_jwk()
    jwk_2 = generate_jwk()
    jwk_3 = generate_jwk()

    jwk_instance_1 = JWK.objects.create(
        jwk=jwk_1.export_public(as_dict=True), account=account
    )
    JWK.objects.create(jwk=jwk_2.export_public(as_dict=True), account=account)
    JWK.objects.create(jwk=jwk_3.export_public(as_dict=True), account=account)

    assert authenticate_from_account(gen_signed_token(jwk_1))
    assert authenticate_from_account(gen_signed_token(jwk_2))
    assert authenticate_from_account(gen_signed_token(jwk_3))

    # Generate a JWK not associated with account and check that it cannot be decrypted with the merged JWK
    with pytest.raises(HttpError, match="Failed to decode token"):
        authenticate_from_account(gen_signed_token(generate_jwk()))

    # Delete a JWK and check that it cannot be decrypted with the merged JWK
    jwk_instance_1.delete()
    # It should still work due to cashing
    assert authenticate_from_account(gen_signed_token(jwk_1))

    # Move time forward so cache expires
    with freeze_time(timezone.now() + timedelta(minutes=6)):
        with pytest.raises(HttpError, match="Failed to decode token"):
            authenticate_from_account(gen_signed_token(jwk_1))

        assert authenticate_from_account(gen_signed_token(jwk_2))
        assert authenticate_from_account(gen_signed_token(jwk_3))


def test_authenticate_from_account_failure_no_account_key(account):
    cache.delete(f"jwk{account.key}")
    # Check that we fail if no account key is supplied in the token
    jwk_1 = generate_jwk()

    JWK.objects.create(jwk=jwk_1.export_public(as_dict=True), account=account)

    token_data = get_token_data()
    token_data.pop("account_key")

    with pytest.raises(HttpError, match="No account key supplied in token"):
        authenticate_from_account(gen_signed_token(jwk_1, data=token_data))


def test_authenticate_from_account_failure_no_public_key(account):
    # Check that auth will fail if no public key is associated with the account
    jwk_1 = generate_jwk()

    cache.delete(f"jwk{account.key}")
    with pytest.raises(HttpError, match="No Public key found for account"):
        authenticate_from_account(gen_signed_token(jwk_1))


def test_request_has_jwt_after_jwt_authentication(rf: RequestFactory, mocker):
    request = rf.get("someurl")
    request.user = AnonymousUser()

    # invalid authentication
    mocked_auth = mocker.patch("projectconfig.authentication.Auth")
    auth_instance = mocked_auth.return_value = mocker.MagicMock()
    auth_instance.authenticate.side_effect = Exception("Boom!")

    token = mocker.MagicMock()
    request.auth = JWTAuth().authenticate(request, token)
    assert not hasattr(request, "jwt")

    # valid authentication
    mocker.patch("projectconfig.authentication.Auth")
    request.auth = JWTAuth().authenticate(request, token)
    assert hasattr(request, "jwt")
