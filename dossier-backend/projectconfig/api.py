from ninja import Ninja<PERSON><PERSON>

from dossier.api import dossier_router, core_router, access_delegation_router
from dossier.api_dmf_v3 import dmf_v3_router
from dossier.internal_api import internal_router
from multi_tenancy.authentication import MultiTenancyJ<PERSON><PERSON><PERSON>

from projectconfig.authentication import <PERSON><PERSON><PERSON><PERSON>
from semantic_document.api import semantic_documents_router
from semantic_document.api_pss import pss_router
from semantic_document.api_semantic_document import semantic_document_router
from semantic_document.api_semantic_page import semantic_page_router
from statemgmt.api import statemgmt
from dossier.doccheck_api import router as doccheck_router
from dossier.management_api import management_router
from multi_tenancy.api import router as multi_tenancy_router

api = NinjaAPI(
    title="Hypodossier API",
    csrf=False,
    auth=JWTAuth(),
    urls_namespace="api",
    version="1.1.1",
)

api.add_router("/", core_router)
api.add_router("/doccheck", doccheck_router)
api.add_router("/dossier/", dossier_router)
# API router implementing component based views for to dossier management frontend
api.add_router("/dmf/v3/", dmf_v3_router)
api.add_router("/internal/", internal_router)
api.add_router("/semantic_documents/", semantic_documents_router)
api.add_router("/semantic_document/", semantic_document_router)
api.add_router("/semantic_page/", semantic_page_router)
api.add_router("/page-stream-segmentation/", pss_router)
api.add_router("/statemgmt/", statemgmt)
api.add_router("/access_delegation/", access_delegation_router)
# Note: we use custom auth for /management/ route,
# Simplified Auth that only checks the JWT is valid against settings.MANAGEMENT_PUBLIC_JWK
# and that token contains "mgmt-api" role
api.add_router("/management/", management_router)
api.add_router("/accounts/", multi_tenancy_router, auth=MultiTenancyJWTAuth())
