# Register your models here.
import structlog
from django.contrib import admin
from django.db.models import Subquery
from django.utils.html import format_html
from rangefilter.filters import DateRangeFilter

from dossier.admin import DossierCreatedAtFilter
from semantic_document.models import SemanticDocument
from semantic_document.services_admin import (
    admin_reset_restart_semantic_document_export,
    admin_reset_semantic_document_export,
    admin_force_semantic_document_export_done,
)
from workers.models import SemanticDocumentExport

logger = structlog.get_logger(__name__)


@admin.register(SemanticDocumentExport)
class SemanticDocumentExportAdmin(admin.ModelAdmin):

    def get_dossier_url(self, obj):
        if obj.done and obj.file:
            url = obj.file.get_fast_url()
            return format_html('<a href="{}" target="_blank">Sem Doc URL</a>', url)
        return "N/A"

    get_dossier_url.short_description = "Export URL"

    list_display = (
        "file",
        "uuid",
        "semantic_document",
        "semantic_document_workstatus",
        "semantic_document_dossiername",
        "updated_at",
        "done",
        "get_dossier_url",
    )  # list of fields to display

    search_fields = (
        "file__data",
        "uuid",
        "semantic_document__dossier__name",
        "semantic_document__dossier__uuid",
    )  # fields to search
    list_filter = (
        "done",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        "semantic_document__work_status",
        ("semantic_document__dossier__created_at", DossierCreatedAtFilter),
    )  # fields to filter

    ordering = ("-updated_at",)

    # needed to search for the correct file, semantic document efficiently if there are > 100
    raw_id_fields = ["file", "semantic_document"]

    actions = (
        "reset_semantic_document_export",
        "reset_restart_semantic_document_export",
        "perform_force_semantic_document_done",
    )

    def dossierfile_name(self, obj):
        return obj.file.name

    def semantic_document_workstatus(self, obj):
        return obj.semantic_document.work_status

    def semantic_document_dossiername(self, obj):
        return obj.semantic_document.dossier.name

    semantic_document_dossiername.short_description = "Dossier"

    @admin.action(description="Reset export for related semantic documents")
    def reset_semantic_document_export(modeladmin, request, queryset):

        unique_semantic_documents = SemanticDocument.objects.filter(
            uuid__in=Subquery(
                queryset.values_list("semantic_document", flat=True).distinct()
            )
        )

        admin_reset_semantic_document_export(unique_semantic_documents, request)

    @admin.action(
        description="Restart related semantic document export (reset, then start again)"
    )
    def reset_restart_semantic_document_export(modeladmin, request, queryset):

        semdoc_uuids = list(
            queryset.values_list("semantic_document", flat=True).distinct()
        )
        unique_semantic_documents = SemanticDocument.objects.filter(
            uuid__in=semdoc_uuids
        )
        admin_reset_restart_semantic_document_export(request, unique_semantic_documents)

    @admin.action(description="Force related semantic document export done status")
    def perform_force_semantic_document_done(modeladmin, request, queryset):
        unique_semantic_documents = SemanticDocument.objects.filter(
            uuid__in=Subquery(
                queryset.values_list("semantic_document", flat=True).distinct()
            )
        )
        admin_force_semantic_document_export_done(request, unique_semantic_documents)
