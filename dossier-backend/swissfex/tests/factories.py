import json
import os
import random
import uuid
from typing import Optional

from faker import Faker
from faker.providers import address, date_time, internet, lorem, person
import jwt
from jwcrypto import jwk
import dossier.schemas as dossier_schemas
from dossier.fakes import (
    add_some_fake_semantic_documents,
    load_initial_document_categories,
)
from dossier.helpers_timezone import create_faker_past_datetime_with_timezone
from dossier.models import Account, Dossier<PERSON>ser, Dossier
from dossier.services import create_expiration_date
from django.conf import settings
from projectconfig.authentication import get_user_or_create
from projectconfig.jwk import load_jwk_from_env
from swissfex.schemas import schemas
from swissfex.services import create_or_update_dossier
from swissfex.tests.data import DATA_PATH


class SwissFexAccountFactoryFaker:
    def __init__(self, account: Optional[Account] = None):
        self.document_categories = None

        if account is None:
            account, _ = Account.objects.update_or_create(
                defaults=dict(
                    name="SwissFex dev",
                    default_bucket_name="dms-default-bucket",
                ),
                dmf_endpoint="https://www.localhost",
                key="swissfexd",
            )

        self.account = account

        faker = Faker(locale="de_CH")
        faker.add_provider(person)
        faker.add_provider(address)
        faker.add_provider(internet)
        faker.add_provider(date_time)
        faker.add_provider(lorem)
        self.faker = faker

    def load_initial_document_categories(self):
        document_categories, _, _, _ = load_initial_document_categories(self.account)
        self.document_categories = document_categories
        return document_categories

    def create_dossier(
        self,
        current_user: DossierUser = None,
    ) -> Dossier:
        if current_user is None:
            current_user = get_user_or_create(
                account=self.account,
                username="<EMAIL>",
                email="<EMAIL>",
                fname="service-swissfex-first",
                lname="service-swissfex-last",
            )

        return create_or_update_dossier(
            self.account,
            schemas.DossierCreateJWT(
                exp=0,
                account_name="swissfexd",
                language=random.choice(list(dossier_schemas.Language)),
                current_user=current_user,
                dossier_name=schemas.DossierName(self.faker.sentence()),
            ),
        )

    def create_sample_dossier(self):
        new_dossier = self.create_dossier()
        new_dossier.created_at = create_faker_past_datetime_with_timezone(self.faker)

        add_some_fake_semantic_documents(new_dossier)
        return new_dossier

    def create_token_data(self, minutes=10080):
        return {
            "aud": "account",
            "exp": create_expiration_date(minutes=10080),
            "name": "service-swissfex-first service-swissfex-last",
            "given_name": "service-swissfex-first",
            "family_name": "service-swissfex-last",
            "preferred_username": "<EMAIL>",
            "email": "<EMAIL>",
            "external_dossier_id": schemas.DossierName(
                self.faker.sentence()
            ),  # Not currently used, as we directly provide this
            # during dossier creation via API parameter
            "user_roles": ["api_role"],
            "account_key": "swissfexd",
            "account_name": "swissfex development account",
            "jti": str(uuid.uuid4()),  # unique identifier for the token
        }

    def generate_jwt_token(self, token_data=None):
        jwks_public_private = load_jwk_from_env(
            jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
        ).model_dump()

        if token_data is None:
            token_data = self.create_token_data()

        key = jwks_public_private["keys"][0]
        jwk_key = jwk.JWK.from_json(json.dumps(key))
        pem = jwk_key.export_to_pem(private_key=True, password=None)
        return jwt.encode(
            payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
            key=pem,
            algorithm="RS256",
        )
