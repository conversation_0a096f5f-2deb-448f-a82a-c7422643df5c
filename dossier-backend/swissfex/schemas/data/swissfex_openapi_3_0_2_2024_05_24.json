{"openapi": "3.0.2", "info": {"title": "Hypodossier - Swissfex API", "version": "0.7.0", "description": ""}, "paths": {"/partner/swissfex/api/0.7/dossier/create": {"post": {"operationId": "swissfex_api_create_dossier", "summary": "Create Dossier", "parameters": [], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "description": "Creates a new Dossier based on the provided parameters", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDossier"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/": {"patch": {"operationId": "swissfex_api_update_dossier", "summary": "Update Dossier", "parameters": [], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "description": "Updates a new Dossier based on the provided parameters", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeDossier"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/copy": {"post": {"operationId": "swissfex_api_copy_dossier", "summary": "<PERSON><PERSON>", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierCopyResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "description": "Creates a new Dossier based on the provided parameters", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CopyDossier"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/copy_async": {"post": {"operationId": "swissfex_api_copy_dossier_async", "summary": "<PERSON><PERSON>", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierCopyAsyncResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "description": "Creates a new Dossier based on the provided parameters", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CopyDossier"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/dossier/{external_dossier_id}/copy_status": {"get": {"operationId": "swissfex_api_get_dossier_copy_status", "summary": "Get Dossier Copy Status", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierCopyStatus"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/original-file": {"post": {"operationId": "swissfex_api_add_original_file", "summary": "Add Original File", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatedObjectReference"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "requestBody": {"content": {"multipart/form-data": {"schema": {"title": "MultiPartBodyParams", "type": "object", "properties": {"file": {"title": "File", "type": "string", "format": "binary"}, "allow_duplicate_and_rename": {"title": "Allow Duplicate And Rename", "default": false, "type": "boolean"}, "force_document_category_key": {"title": "Force Document Category Key", "type": "string"}, "force_title_suffix": {"title": "Force Title Suffix", "type": "string"}, "force_external_semantic_document_id": {"title": "Force External Semantic Document Id", "type": "string"}, "force_access_mode": {"title": "OriginalFileForceAccessMode", "description": "An enumeration.", "enum": ["read_write", "read_only"], "type": "string"}, "force_semantic_document_custom_attribute": {"title": "Force Semantic Document Custom Attribute", "type": "string"}}, "required": ["file"]}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/details": {"get": {"operationId": "swissfex_api_get_dossier", "summary": "Get Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/file-status": {"get": {"operationId": "swissfex_api_get_file_status", "summary": "Get File Status", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierProcessingStatus"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-documents": {"get": {"operationId": "swissfex_api_get_semantic_documents", "summary": "Get Semantic Documents", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "query", "name": "show_pages", "schema": {"title": "Show Pages", "default": false, "type": "boolean"}, "required": false}, {"in": "query", "name": "show_soft_deleted", "schema": {"title": "Show Soft Deleted", "default": false, "type": "boolean"}, "required": false}, {"in": "query", "name": "show_all_documents_for_soft_deleted", "schema": {"title": "Show All Documents For Soft Deleted", "default": false, "type": "boolean"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "array", "items": {"$ref": "#/components/schemas/SemanticDocument"}}}}}}, "description": "Return schemantic documents for a dossier\nif show_pages is true, then also return the pages for each document", "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}": {"patch": {"operationId": "swissfex_api_update_semantic_document", "summary": "Update Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocument"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"title": "Response", "type": "string"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "description": "Update a schemantic document for a dossier\nif show_pages is true, then also return the pages for each document", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSemanticDocument"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}, "delete": {"operationId": "swissfex_api_soft_delete_semantic_document", "summary": "Soft Delete Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string", "format": "uuid"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/document-categories": {"get": {"operationId": "swissfex_api_get_document_categories", "summary": "Get Document Categories", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentCategories"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/dossier/": {"post": {"operationId": "swissfex_api_export_dossier_export", "summary": "Export Dossier Export", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequest"}}}}}, "description": "Export a dossier as a zip file\n\nFor an external dossier, dispatch a request to rabbitmq for dossier export and return a download url\n\nIf a dossier export already exists, create a new dossier export and return a download url", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportDossierExport"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/dossier/{export_uuid}/status": {"get": {"operationId": "swissfex_api_get_dossier_export_status", "summary": "Get Dossier Export Status", "parameters": [{"in": "path", "name": "export_uuid", "schema": {"title": "Export U<PERSON>", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportStatus"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}": {"delete": {"operationId": "swissfex_api_delete_dossier", "summary": "Delete Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}": {"post": {"operationId": "swissfex_api_export_semantic_document_pdf", "summary": "Export Semantic Document Pdf", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocumentPDFExportRequest"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/{semantic_document_export_request_uuid}/status": {"get": {"operationId": "swissfex_api_get_semantic_document_export_status", "summary": "Get Semantic Document Export Status", "parameters": [{"in": "path", "name": "semantic_document_export_request_uuid", "schema": {"title": "Semantic Document Export Request Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportStatus"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-page/{semantic_page_uuid}": {"delete": {"operationId": "swissfex_api_soft_delete_semantic_page", "summary": "Soft Delete Semantic Page", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_page_uuid", "schema": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "format": "uuid"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}/restore": {"put": {"operationId": "swissfex_api_undelete_semantic_document", "summary": "Undelete Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string", "format": "uuid"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-page/{semantic_page_uuid}/restore": {"put": {"operationId": "swissfex_api_undelete_semantic_page", "summary": "Undelete Semantic Page", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_page_uuid", "schema": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "format": "uuid"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}}, "components": {"schemas": {"Dossier": {"title": "Dossier", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}, "created_at": {"title": "Created At", "type": "string", "format": "date-time"}}, "required": ["uuid", "external_dossier_id", "updated_at", "created_at"]}, "Error": {"title": "Error", "type": "object", "properties": {"code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}}, "required": ["code", "message"]}, "Language": {"title": "Language", "description": "An enumeration.", "enum": ["de", "fr", "it", "en"], "type": "string"}, "CreateDossier": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}, "name": {"title": "Name", "maxLength": 255, "type": "string"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["external_dossier_id", "name", "language"]}, "ChangeDossier": {"title": "Change<PERSON><PERSON><PERSON>", "type": "object", "properties": {"external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}, "name": {"title": "Name", "maxLength": 255, "type": "string"}, "lang": {"$ref": "#/components/schemas/Language"}}, "required": ["external_dossier_id"]}, "DossierCopyResponse": {"title": "DossierCopyResponse", "type": "object", "properties": {"dossier_uuid": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "format": "uuid"}, "external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}, "created_at": {"title": "Created At", "type": "string", "format": "date-time"}}, "required": ["dossier_uuid", "external_dossier_id", "updated_at", "created_at"]}, "CopyDossier": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"new_external_dossier_id": {"title": "New External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}}, "required": ["new_external_dossier_id"]}, "DossierCopyAsyncResponse": {"title": "DossierCopyAsyncResponse", "type": "object", "properties": {"external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}}, "required": ["external_dossier_id"]}, "ExportProcessingStatus": {"title": "ExportProcessingStatus", "description": "An enumeration.", "enum": ["PROCESSING", "ERROR", "PROCESSED"], "type": "string"}, "DossierCopyStatus": {"title": "DossierCopyStatus", "type": "object", "properties": {"external_dossier_id": {"title": "External Dossier Id", "type": "string"}, "status": {"$ref": "#/components/schemas/ExportProcessingStatus"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}}, "required": ["external_dossier_id", "status"]}, "CreatedObjectReference": {"title": "CreatedObjectReference", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}}, "required": ["uuid"]}, "Message": {"title": "Message", "type": "object", "properties": {"detail": {"title": "Detail", "type": "string"}}, "required": ["detail"]}, "FileStatus": {"title": "FileStatus", "description": "An enumeration.", "enum": ["processing", "error", "processed"], "type": "string"}, "ExtractedFile": {"title": "ExtractedFile", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "path_from_original": {"title": "Path From Original", "type": "string"}, "file_name": {"title": "File Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "file_url": {"title": "File Url", "minLength": 1, "maxLength": 65536, "format": "uri", "type": "string"}, "created_at": {"title": "Created At", "type": "string", "format": "date-time"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}}, "required": ["uuid", "path_from_original", "file_name", "status", "file_url", "created_at", "updated_at"]}, "OriginalFile": {"title": "OriginalFile", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "name": {"title": "Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "extracted_files": {"title": "Extracted Files", "type": "array", "items": {"$ref": "#/components/schemas/ExtractedFile"}}, "file_url": {"title": "File Url", "minLength": 1, "maxLength": 65536, "format": "uri", "type": "string"}, "created_at": {"title": "Created At", "type": "string", "format": "date-time"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}}, "required": ["uuid", "name", "status", "extracted_files", "file_url", "created_at", "updated_at"]}, "DossierProcessingStatus": {"title": "DossierProcessingStatus", "type": "object", "properties": {"dossier_uuid": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "format": "uuid"}, "external_id": {"title": "External Id", "type": "string"}, "progress": {"title": "Progress", "type": "integer"}, "original_files": {"title": "Original Files", "type": "array", "items": {"$ref": "#/components/schemas/OriginalFile"}}}, "required": ["dossier_uuid", "external_id", "progress", "original_files"]}, "Confidence": {"title": "Confidence", "description": "An enumeration.", "enum": ["certain", "high", "medium", "low"], "type": "string"}, "SemanticPage": {"title": "SemanticPage", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "number": {"title": "Number", "description": "Page number is zero based. First page has page number 0", "type": "integer"}, "image_url": {"title": "Image Url", "type": "string"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}, "deleted_at": {"title": "Deleted At", "type": "string", "format": "date-time"}}, "required": ["uuid", "number", "image_url", "updated_at"]}, "AccessMode": {"title": "AccessMode", "description": "Access mode of a semantic document (not the same as access mode of a dossier)", "enum": ["read_only", "read_write"], "type": "string"}, "SemanticDocument": {"title": "SemanticDocument", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "title": {"title": "Title", "type": "string"}, "title_lang": {"title": "Title Lang", "type": "string"}, "title_suffix": {"title": "Title Suffix", "type": "string"}, "external_semantic_document_id": {"title": "External Semantic Document Id", "type": "string"}, "document_category_id": {"title": "Document Category Id", "type": "string"}, "document_category_key": {"title": "Document Category Key", "type": "string"}, "document_category_title_de": {"title": "Document Category Title De", "type": "string"}, "document_category_title_en": {"title": "Document Category Title En", "type": "string"}, "document_category_title_fr": {"title": "Document Category Title Fr", "type": "string"}, "document_category_title_it": {"title": "Document Category Title It", "type": "string"}, "document_category_confidence": {"$ref": "#/components/schemas/Confidence"}, "semantic_pages": {"title": "Semantic Pages", "type": "array", "items": {"$ref": "#/components/schemas/SemanticPage"}}, "access_mode": {"$ref": "#/components/schemas/AccessMode"}, "custom_attribute": {"title": "Custom Attribute", "maxLength": 255, "type": "string"}, "created_at": {"title": "Created At", "type": "string", "format": "date-time"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}, "last_change": {"title": "Last Change", "type": "string", "format": "date-time"}, "deleted_at": {"title": "Deleted At", "type": "string", "format": "date-time"}}, "required": ["uuid", "title", "title_lang", "document_category_id", "document_category_key", "document_category_title_de", "document_category_title_en", "document_category_title_fr", "document_category_title_it", "semantic_pages", "access_mode", "created_at", "updated_at", "last_change"]}, "UpdateSemanticDocument": {"title": "UpdateSemanticDocument", "type": "object", "properties": {"document_category_key": {"title": "Document Category Key", "type": "string"}, "title_suffix": {"title": "Title Suffix", "type": "string"}, "external_semantic_document_id": {"title": "External Semantic Document Id", "maxLength": 255, "type": "string"}, "access_mode": {"$ref": "#/components/schemas/AccessMode"}, "custom_attribute": {"title": "Custom Attribute", "maxLength": 255, "type": "string"}}}, "SavingResultWithMessage": {"title": "SavingResultWithMessage", "type": "object", "properties": {"message": {"title": "Message", "type": "string"}}, "required": ["message"]}, "DocumentCategory": {"title": "DocumentCategory", "type": "object", "properties": {"key": {"title": "Key", "type": "string"}, "id": {"title": "Id", "type": "string"}, "title_de": {"title": "Title De", "type": "string"}, "title_en": {"title": "Title En", "type": "string"}, "title_fr": {"title": "Title Fr", "type": "string"}, "title_it": {"title": "Title It", "type": "string"}, "description_de": {"title": "Description De", "type": "string"}, "description_en": {"title": "Description En", "type": "string"}, "description_fr": {"title": "Description Fr", "type": "string"}, "description_it": {"title": "Description It", "type": "string"}}, "required": ["key", "id", "title_de", "title_en", "title_fr", "title_it"]}, "DocumentCategories": {"title": "DocumentCategories", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/DocumentCategory"}}, "ExportRequest": {"title": "ExportRequest", "type": "object", "properties": {"export_uuid": {"title": "Export U<PERSON>", "type": "string", "format": "uuid"}}, "required": ["export_uuid"]}, "ExportDossierExport": {"title": "ExportDossierExport", "type": "object", "properties": {"external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}, "add_uuid_suffix": {"title": "Add Uuid Suffix", "default": false, "type": "boolean"}}, "required": ["external_dossier_id"]}, "ExportStatus": {"title": "ExportStatus", "type": "object", "properties": {"export_uuid": {"title": "Export U<PERSON>", "type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/ExportProcessingStatus"}, "dossier_url": {"title": "Dossier Url", "minLength": 1, "maxLength": 65536, "format": "uri", "type": "string"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}}, "required": ["export_uuid", "status"]}, "SemanticDocumentPDFExportRequest": {"title": "SemanticDocumentPDFExportRequest", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}}, "required": ["uuid"]}}, "securitySchemes": {"SwissfexJWTAuth": {"type": "http", "scheme": "bearer"}}}, "servers": null}