import structlog

import djclick as click

from assets import ASSETS_PATH
from dossier.doc_cat_helpers import load_document_categories_from_path
from dossier.models import Account

logger = structlog.get_logger()


@click.command()
@click.argument("account_name")
def load_document_categories_swissfex(account_name: str):
    """
    python manage.py load_document_categories_swissfex swissfext
    @param account_name:
    @return:
    """
    assert account_name
    logger.info(f"Load document categories for account '{account_name}'...")
    account = Account.objects.get(key=account_name)
    load_document_categories_from_path(account, info_logging=True)
    load_document_categories_from_path(
        account,
        ASSETS_PATH / "document_category/swissfex/DocumentCategory-2024-01-19.json",
        info_logging=True,
    )
