import structlog

import djclick as click

from swissfex.tests.factories import SwissFexAccountFactoryFaker

logger = structlog.get_logger()


@click.command()
def run():
    # Generate a long-lived JWT token for testing purposes based off privatekey in
    # tests/data/jwks-example.json
    # Currently lasts a week
    # Run via python manage.py generate_jwt_token
    factory = SwissFexAccountFactoryFaker()
    print("TOKEN:\n")
    print(factory.generate_jwt_token())
