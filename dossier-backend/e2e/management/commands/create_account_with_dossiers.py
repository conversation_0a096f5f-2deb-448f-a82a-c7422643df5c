import djclick as click
import structlog
from django.utils import timezone

from dossier.models import Account
from e2e.sample_accounts import create_basic_account_with_dossiers

logger = structlog.getLogger(__name__)


@click.command()
@click.argument("account_key", nargs=1)
@click.argument("dossier_nr", type=click.INT, nargs=1)
@click.argument("semantic_document_nr", type=click.INT, nargs=1)
@click.argument("semantic_page_nr", type=click.INT, nargs=1)
@click.option("--delete", is_flag=True)
def create_account_with_dossiers(
    account_key: str,
    dossier_nr: int = 1,
    semantic_document_nr: int = 3,
    semantic_page_nr: int = 3,
    delete: bool = False,
):
    """
    Create an account with dossiers for testing purposes.

    This command creates an account with a specified number of dossiers, semantic documents,
    and semantic pages. It's useful for testing and development environments.

    Command examples:
        # Create an account with 2 dossiers, 3 semantic documents per dossier, and 3 pages per document
        python manage.py create_account_with_dossiers "test_account" 2 3 3

        # Delete an existing account before creating a new one
        python manage.py create_account_with_dossiers "test_account" 2 3 3 --delete

    Args:
        account_key: The unique key for the account to create
        dossier_nr: Number of dossiers to create (default: 1)
        semantic_document_nr: Number of semantic documents per dossier (default: 3)
        semantic_page_nr: Number of semantic pages per semantic document (default: 3)
        delete: Flag to delete an existing account with the same key before creating a new one
    """
    start = timezone.now()
    logger.info("account_key: " + account_key)
    try:
        if delete:
            start_delete = timezone.now()
            try:
                logger.info("Delete flag set, deleting account", key=account_key)
                account = Account.objects.filter(key=account_key).first()
                if account:
                    account.delete()
            finally:
                logger.info(
                    "Delete Account duration",
                    duration_seconds=(timezone.now() - start_delete).total_seconds(),
                )
        create_basic_account_with_dossiers(
            account_key, dossier_nr, semantic_document_nr, semantic_page_nr
        )
    finally:
        logger.info(
            "Create Account duration",
            duration_seconds=(timezone.now() - start).total_seconds(),
        )
