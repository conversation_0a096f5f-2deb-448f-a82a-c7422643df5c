from django.core.validators import MinValueValidator
from django.db import models
from django.db.models import CASCADE, PROTECT

from core.behaviors import Timestampable
from dossier.models import (
    Dossier,
    ExtractedFile,
    DocumentCategory,
    PageCategory,
    Languages,
    DossierFile,
)


class ConfidenceLevel(models.TextChoices):
    LOW = "low", "low"
    MEDIUM = "medium", "medium"
    HIGH = "high", "high"


class ProcessedFile(Timestampable):
    dossier = models.ForeignKey(
        Dossier, on_delete=CASCADE, related_name="processed_files"
    )

    # Changed to OneToOne Filed, we should have only one processed file per extracted file
    extracted_file = models.OneToOneField(
        ExtractedFile, on_delete=CASCADE, related_name="processed_files"
    )

    processed_pages: models.QuerySet["ProcessedPage"]


class ProcessedPage(Timestampable):
    processed_file = models.ForeignKey(
        ProcessedFile, on_delete=CASCADE, related_name="processed_pages"
    )
    document_category = models.ForeignKey(DocumentCategory, on_delete=CASCADE)
    page_category = models.ForeignKey(PageCategory, on_delete=CASCADE)
    dossier = models.ForeignKey(
        Dossier, on_delete=CASCADE, related_name="processed_pages"
    )
    confidence_value = models.FloatField(default=0)

    number = models.IntegerField(validators=[MinValueValidator(0)])
    lang = models.CharField(
        max_length=2, choices=Languages.choices, default=Languages.GERMAN
    )

    image = models.ForeignKey(
        DossierFile, on_delete=CASCADE, related_name="+", null=True, blank=True
    )
    searchable_pdf = models.ForeignKey(
        DossierFile, on_delete=CASCADE, related_name="+", null=True, blank=True
    )
    searchable_txt = models.ForeignKey(
        DossierFile, on_delete=CASCADE, related_name="+", null=True, blank=True
    )


class PageObjectTitle(Timestampable):
    key = models.CharField(max_length=255, blank=False, null=False, unique=True)
    de = models.CharField(max_length=255)
    en = models.CharField(max_length=255)
    fr = models.CharField(max_length=255)
    it = models.CharField(max_length=255)

    def __str__(self):
        return self.key


class BoundingBox(models.Model):
    class Meta:
        abstract = True

    ref_height = models.IntegerField()
    ref_width = models.IntegerField()
    top = models.IntegerField()
    left = models.IntegerField()
    right = models.IntegerField()
    bottom = models.IntegerField()


class PageObjectType(Timestampable):
    name = models.CharField(max_length=255, unique=True)

    def __str__(self):
        return self.name


class PageObject(Timestampable, BoundingBox):
    processed_page = models.ForeignKey(
        ProcessedPage, on_delete=CASCADE, related_name="page_objects"
    )
    key = models.ForeignKey(PageObjectTitle, on_delete=PROTECT)
    type = models.ForeignKey(PageObjectType, on_delete=PROTECT)
    value = models.TextField(blank=True, null=True)
    confidence_value = models.FloatField()
    confidence_formatted = models.CharField(max_length=16, blank=True, null=True)
    confidence_level = models.CharField(max_length=16, choices=ConfidenceLevel.choices)
    visible = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.key.key} ({self.type.name}) - Confidence: {self.confidence_level} ({self.confidence_value:.2f})"
