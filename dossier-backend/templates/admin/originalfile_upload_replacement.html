{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" href="{% static "admin/css/forms.css" %}">
<style>
  .upload-form {
    margin-top: 20px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #e0e0e0;
  }
  .file-details {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #fff;
    border-left: 4px solid #417690;
  }
  .file-info-item {
    margin-bottom: 8px;
  }
  .info-label {
    font-weight: bold;
    min-width: 120px;
    display: inline-block;
  }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
  <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
  &rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
  &rsaquo; <a href="{% url 'admin:dossier_originalfile_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
  &rsaquo; <a href="{% url 'admin:dossier_originalfile_change' original_file.uuid %}">{{ original_file }}</a>
  &rsaquo; {% trans 'Upload Replacement File' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <h1>Upload Replacement File</h1>
  
  <div class="file-details">
    <h2>Original File Details</h2>
    <div class="file-info-item">
      <span class="info-label">File Name:</span> {{ original_file.file.name }}
    </div>
    <div class="file-info-item">
      <span class="info-label">Dossier:</span> {{ original_file.dossier.name }}
    </div>
    <div class="file-info-item">
      <span class="info-label">Status:</span> {{ original_file.status }}
    </div>
    {% if original_file.file_replacement %}
    <div class="file-info-item">
      <span class="info-label">Current Replacement:</span> {{ original_file.file_replacement.name }}
    </div>
    {% endif %}
  </div>
  
  <div class="upload-form">
    <p>
      Upload a fixed version of this file. The new file will be used for processing while maintaining
      all references to the original file.
    </p>
    
    <form method="post" enctype="multipart/form-data">
      {% csrf_token %}
      <div class="form-row">
        <label for="id_replacement_file">Select Replacement File:</label>
        <input type="file" name="replacement_file" id="id_replacement_file" required>
      </div>
      <div class="submit-row">
        <input type="submit" value="Upload Replacement File" class="default">
        <a href="{% url 'admin:dossier_originalfile_change' original_file.uuid %}" class="button cancel-link">Cancel</a>
      </div>
    </form>
  </div>
</div>
{% endblock %} 