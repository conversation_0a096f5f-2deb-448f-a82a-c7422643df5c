from functools import partial

import httpx
import structlog
from django.conf import settings
from ninja.errors import HttpError, AuthenticationError

from dossier import jwt_extract
from dossier.models import Dossier
from projectconfig.authentication import get_user_or_create
from zkb.services import check_dossier_access_external_service

logger = structlog.get_logger(__name__)


def authorize(login_id: str, external_dossier_id: str) -> bool:
    cert = (
        (
            settings.ZKB_AUTHORIZATION_CERT_PEM_FILE,
            settings.ZKB_AUTHORIZATION_CERT_KEY_FILE,
        )
        if settings.ZKB_AUTHORIZATION_CERT_PEM_FILE
        else None
    )

    with httpx.Client(
        proxy=settings.ZKB_SOCKS5_PROXY,
        cert=cert,
    ) as client:
        response = client.get(
            f"{settings.ZKB_AUTHORIZATION_ENDPOINT}/partnerExtern/hasDatenraumrecht?partnerUuid={external_dossier_id}&logonId={login_id}"
        )
        if response.is_error:
            logger.warning(
                "error response in external zkb service",
                response_status=response.status_code,
                response_text=response.content,
            )
        response.raise_for_status()

        return response.json()


def check_zkb_dossier_access(jwt: dict, dossier: Dossier, external_authorization):
    account = jwt_extract.get_account(jwt)

    if dossier.account != account:
        raise HttpError(
            404,
            f"Dossier with uuid {dossier.uuid} and external_id {dossier.external_id} not found in account{account.key}",
        )

    # check zkb dossier access only decided about dossiers which are created by the external entity. If the dossier.external_id is not set, the general access check is used
    if dossier.external_id is None:
        return None

    if not (jwt_extract.is_manager(jwt)):
        # Only UserID is used for the external authorization service, currently only ZKB uses this
        user_id = jwt.get("UserID")
        if user_id is None:
            raise HttpError(403, "Missing UserID in the JWT and not a Dossier-Manager")

        dossier_user = get_user_or_create(
            account=dossier.account,
            # user_id is filled with the UserID which is not the username.
            # the username is the preferred_username
            username=jwt.get("preferred_username"),
            email=jwt.get("email"),
            fname=jwt.get("given_name"),
            lname=jwt.get("family_name"),
        )

        has_access = check_dossier_access_external_service(
            user=dossier_user.user,
            # user_id should be passed to external auth
            user_id=user_id,
            dossier=dossier,
            external_service_check_callable=external_authorization,
            url=settings.ZKB_AUTHORIZATION_ENDPOINT,
        )

        if not has_access:
            raise AuthenticationError()


check_dossier_access_external_call_zkb = partial(
    check_zkb_dossier_access,
    external_authorization=lambda user_id, dossier_id, url: authorize(
        user_id, dossier_id
    ),
)

check_zkb_dossier_access_external_always_true = partial(
    check_zkb_dossier_access, external_authorization=lambda *args: True
)

check_zkb_dossier_access_external_always_false = partial(
    check_zkb_dossier_access, external_authorization=lambda *args: False
)
