# Generated by Django 3.2.3 on 2021-06-29 11:56

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0007_auto_20210623_1453'),
    ]

    operations = [
        migrations.CreateModel(
            name='DossierExport',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('done', models.DateTimeField(blank=True, null=True)),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exports', to='dossier.dossier')),
                ('file', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dossier.dossierfile')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
