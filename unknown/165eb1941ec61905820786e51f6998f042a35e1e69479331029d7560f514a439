from uuid import UUID
from django.utils import timezone

import djclick as click
import structlog

from dossier.helpers_model_copier import copy_dossier_models
from dossier.models import Dossier

logger = structlog.get_logger(__name__)


@click.command()
@click.argument("source_dossier_uuid", type=click.UUID)
@click.argument("new_external_id", type=click.STRING)
def copy_dossier(source_dossier_uuid: UUID, new_external_id: str):
    """
    Copy a dossier with all processed entities to a new dossier.

    Example:
        python manage.py copy_dossier 254e93ec-c0f2-4133-be04-24170c60e650 254e93ec-c0f2-4133-be04-24170c60e65z

    @param source_dossier_uuid:
    @param new_external_id:
    @return:
    """
    logger.info(
        "Copying dossier",
        source_dossier_uuid={source_dossier_uuid},
        new_external_id={new_external_id},
    )
    time = timezone.now()

    dossier = Dossier.objects.get(uuid=source_dossier_uuid)

    new_instances = list(
        copy_dossier_models(
            dossier=dossier,
            external_id=new_external_id,
        ).values()
    )

    success = len(new_instances) > 0

    if success:
        new_dossier: Dossier = new_instances[0]
        duration = timezone.now() - time

        logger.info(
            "Copying finished",
            source_dossier_uuid={source_dossier_uuid},
            new_dossier_uuid=new_dossier.uuid,
            num_instances=len(new_instances),
            duration=duration,
        )
