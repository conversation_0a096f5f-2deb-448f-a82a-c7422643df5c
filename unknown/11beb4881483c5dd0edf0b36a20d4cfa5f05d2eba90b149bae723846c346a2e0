from datetime import <PERSON><PERSON><PERSON>

import pytest
from django.urls import reverse
from django.utils import timezone

from dossier import schemas
from dossier.dossier_access_external import (
    get_access_check_provider_default_dossier_access_grant,
    DAP_DEFAULT_ACCESS_GRANT_CHECK,
    create_or_update_access_grant,
)
from dossier.services import create_dossier
from finnova.schemas import AccountName

HTTP_401_UNAUTHORIZED = 401


def test_provider(db):
    dap = get_access_check_provider_default_dossier_access_grant()
    assert dap.name == DAP_DEFAULT_ACCESS_GRANT_CHECK


def test_dossier_access_grant_setup(db, finnova_account_factory):

    account = finnova_account_factory.account
    assert account
    assert account.key == AccountName.finnovadev.value
    print(account)
    assert account.dossier_access_check_provider.name == DAP_DEFAULT_ACCESS_GRANT_CHECK
    # state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    # state_machine.start_status = state_machine.status_set.first()
    # assert state_machine.start_status
    #
    # account.active_work_status_state_machine = state_machine
    #
    # user = User.objects.create_user(
    #     "testuser", "<EMAIL>", "some randomeness"
    # )
    # new_dossier = create_dossier(account, "sample dossier", "de", user)


@pytest.mark.skip(reason="Does not work, needs fixing in pipeline")
def test_dossier_access_grant_single_user_grant_in_db(
    db,
    finnova_account_factory,
    finnovauser1_user,
    finnovauser1_client,
    finnovauser2_user,
):

    account = finnova_account_factory.account
    assert account.dossier_access_check_provider.name == DAP_DEFAULT_ACCESS_GRANT_CHECK
    assert account.key == AccountName.finnovadev.value
    dossier_a = create_dossier(account, "dossier a", "de", finnovauser1_user.user)
    assert dossier_a

    dossier_a.owner = finnovauser2_user.user
    dossier_a.save()

    # Step 1: No grant provided therefore not authorized
    response_1 = finnovauser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier_a.uuid})
    )
    assert response_1.status_code == HTTP_401_UNAUTHORIZED

    # Step 2: Grant access
    create_or_update_access_grant(finnovauser1_user.user, dossier_a)
    response_2 = finnovauser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier_a.uuid})
    )
    assert response_2.status_code == 200
    grant = schemas.DossierAccessGrant.model_validate_json(response_2.content)
    assert grant.dossier_uuid == dossier_a.uuid
    assert grant.has_access is True

    # Step 3: revoke access
    create_or_update_access_grant(finnovauser1_user.user, dossier_a, has_access=False)
    response_3 = finnovauser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier_a.uuid})
    )
    assert response_3.status_code == HTTP_401_UNAUTHORIZED

    # Step 4: expired access
    create_or_update_access_grant(
        finnovauser1_user.user,
        dossier_a,
        expires_at=timezone.now() + timedelta(minutes=-2),
        has_access=True,
    )
    response_4 = finnovauser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier_a.uuid})
    )
    assert response_4.status_code == HTTP_401_UNAUTHORIZED


def test_dossier_access_grant_single_user_grant_via_api(
    db,
    finnova_account_factory,
    finnovauser1_user,
    finnovauser1_client,
    finnovauser2_user,
):

    account = finnova_account_factory.account
    assert account.key == AccountName.finnovadev.value
    dossier_a = create_dossier(account, "dossier a", "de", finnovauser1_user.user)
    assert dossier_a

    # Step 1: Grant access
    create_or_update_access_grant(finnovauser1_user.user, dossier_a)
    response_2 = finnovauser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier_a.uuid})
    )
    assert response_2.status_code == 200
    grant = schemas.DossierAccessGrant.model_validate_json(response_2.content)
    assert grant.dossier_uuid == dossier_a.uuid
    assert grant.has_access is True
